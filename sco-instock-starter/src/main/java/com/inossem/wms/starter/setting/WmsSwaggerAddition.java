package com.inossem.wms.starter.setting;

import com.inossem.wms.common.util.UtilCollection;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import com.google.common.collect.Sets;

import springfox.documentation.builders.OperationBuilder;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.builders.ResponseBuilder;
import springfox.documentation.service.ApiDescription;
import springfox.documentation.service.Operation;
import springfox.documentation.service.ParameterType;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.ApiListingScannerPlugin;
import springfox.documentation.spi.service.contexts.DocumentationContext;
import springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator;

/**
 * swagger登陆自定义
 *
 * <AUTHOR>
 * @date 2021/04/14 10:14
 **/
@Component
public class WmsSwaggerAddition implements ApiListingScannerPlugin {
    /**
     * Implement this method to manually add ApiDescriptions 实现此方法可手动添加ApiDescriptions
     *
     * @param context - Documentation context that can be used infer documentation context
     * @return List of {@link ApiDescription}
     * @see ApiDescription
     */
    @Override
    public List<ApiDescription> apply(DocumentationContext context) {
        // 返回参数格式
        Operation usernamePasswordOperation = new OperationBuilder(
            new CachingOperationNameGenerator())
            .method(HttpMethod.POST)
            .summary("用户名密码登录")
            // 接收参数格式
            .notes("API Parameter Example: {\"password\":\"abcd1234\",\"userCode\":\"admin\"}")
            .consumes(UtilCollection.newHashSet(MediaType.APPLICATION_FORM_URLENCODED_VALUE))
            .produces(UtilCollection.newHashSet(MediaType.APPLICATION_JSON_VALUE))
            .tags(Sets.newHashSet("登录"))
            .requestParameters(Arrays.asList(
                new RequestParameterBuilder()
                    .description("用户名")
                    .name("userCode")
                    .required(true)
                    .in(ParameterType.QUERY)
                    .build(),
                new RequestParameterBuilder()
                    .description("密码")
                    .name("password")
                    .required(true)
                    .in(ParameterType.QUERY)
                    .build()
            ))
            .responses(Collections.singletonList(
                new ResponseBuilder()
                    .code("200")
                    .description("请求成功")
                    .build()
            ))
            .build();

        ApiDescription loginApiDescription = new ApiDescription(
            "login",
            "/wms/web/login",
            "登录接口",
            "登录接口",
            Collections.singletonList(usernamePasswordOperation),
            false);

        return Collections.singletonList(loginApiDescription);
    }

    /**
     * 是否使用此插件
     *
     * @param documentationType swagger文档类型
     * @return true 启用
     */
    @Override
    public boolean supports(DocumentationType documentationType) {
        return DocumentationType.SWAGGER_2.equals(documentationType);
    }
}
