package com.inossem.wms.starter.interceptor;

import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.MethodParameter;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.View;
import org.springframework.web.util.WebUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponseWrapper;
import java.lang.reflect.Parameter;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 参数解析器 - 上下文处理 - 当前用户处理
 *
 * <AUTHOR>
 */
public class WmsBizContextArgumentResolver implements HandlerMethodArgumentResolver {

    /**
     * 用于判定是否需要处理该参数分解，返回true为需要，并会去调用下面的方法resolveArgument
     *
     * @param methodParameter 当前参数
     * @return 是否需要处理
     */
    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        return methodParameter.getParameterType().equals(BizContext.class);
    }

    /**
     * 用于处理参数分解的方法，返回的Object就是controller方法上的形参对象
     *
     * @param methodParameter 当前参数
     * @param modelAndViewContainer 上下文容器
     * @param nativeWebRequest 这是一个接收Request和Response的请求
     * @param webDataBinderFactory 请求参数的绑定
     * @return Object
     */
    @Override
    public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer modelAndViewContainer, NativeWebRequest nativeWebRequest,
        WebDataBinderFactory webDataBinderFactory) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (methodParameter.getParameterType().equals(BizContext.class)) {
            ParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();
            String[] parameterNames = discoverer.getParameterNames(Objects.requireNonNull(methodParameter.getMethod()));

            BizContext ctx = new BizContext();
            // post/put请求绑定上下文
            Set<String> keySet = modelAndViewContainer.getModel().keySet();
            for (String keyStr : keySet) {
                Object obj = modelAndViewContainer.getModel().get(keyStr);
                if (obj.getClass().equals(BeanPropertyBindingResult.class)) {
                    BeanPropertyBindingResult result = (BeanPropertyBindingResult)obj;
                    Parameter[] parameters = methodParameter.getExecutable().getParameters();
                    for (int n = 0; n < parameters.length; n++) {
                        if(parameters[n].getType().isInterface()){
                            // 接口类型判断
                            if(parameters[n].getType().isAssignableFrom(result.getTarget().getClass())){
                                ctx.setContextData(parameterNames[n], result.getTarget());
                            }
                        }else{
                            if (parameters[n].getType().equals(result.getTarget().getClass()) && null != parameterNames) {
                                // 取参数名称为key值
                                ctx.setContextData(parameterNames[n], result.getTarget());
                            }
                        }
                    }
                }
            }
            if (authentication != null) {
                // user权限配置
                ctx.setCurrentUser((CurrentUser)authentication.getDetails());
            }

            // get/delete请求绑定上下文
            HttpServletRequest request = nativeWebRequest.getNativeRequest(HttpServletRequest.class);
            Map pathVariables = (Map)request.getAttribute(View.PATH_VARIABLES);
            if (null != pathVariables) {
                for (Object key : pathVariables.keySet()) {
                    // 取参数名称为key值
                    ctx.setContextData(key.toString(), pathVariables.get(key));
                }
            }
            // 上传文件处理
            if(request.getContentType().contains(MediaType.MULTIPART_FORM_DATA_VALUE)){
                MultipartHttpServletRequest multipartRequest =
                    WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
                Map<String, MultipartFile> multipartFileMap = multipartRequest.getFileMap();
                for(String name:multipartFileMap.keySet()){
                    for (int n = 0; n < parameterNames.length; n++) {
                        if(parameterNames[n].equals(name)){
                            ctx.setContextData(parameterNames[n], multipartFileMap.get(name));
                        }
                    }
                }
            }


            // 将request和response对象放入到上下文
            ctx.setRequest(nativeWebRequest.getNativeRequest(HttpServletRequestWrapper.class));
            ctx.setResponse(nativeWebRequest.getNativeResponse(HttpServletResponseWrapper.class));
            return ctx;
        }
        return null;
    }

}
