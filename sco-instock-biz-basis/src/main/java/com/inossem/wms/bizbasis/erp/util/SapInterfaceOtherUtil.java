package com.inossem.wms.bizbasis.erp.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.sap.SapConst;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyItemDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputBinDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.erp.dto.ErpReceiveReceiptItemDTO;
import com.inossem.wms.common.model.erp.po.ReserveReceiptCreatePO;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilErp;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilSpring;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调用sap冲销
 */
@Slf4j
public class SapInterfaceOtherUtil {

    /**
     * 调用sap传输附件请求报文
     * @param postingItem
     * @return
     */
   public static JSONObject getAttSynRequestParams (String postingItem){
       JSONObject params = JSONObject.parseObject(postingItem, JSONObject.class);
       BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
       CurrentUser user = bizCommonService.getUser();
       String userCode = user != null ? user.getUserCode() : SapConst.DEFAULT_USER_CODE;
       JSONObject paramsOfImport = UtilErp.getImport(userCode, Const.STRING_EMPTY, SapConst.TYPE_TWO);
       params.put("IS_IMPORT", paramsOfImport);
       return params;
   }

    /**
     * 创建预留单请求报文
     * @return
     */
    public static JSONObject getCreateReserveReceiptParams (ReserveReceiptCreatePO po, CurrentUser user){
        JSONObject params = new JSONObject();
        /* ======  I_Import参数 接口通用输入参数 ====== */
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), EnumReceiptType.RESERVE_RECEIPT.getValue().toString(), SapConst.TYPE_TWO);
        if (UtilObject.isNull(po.getBizReceiptApplyHeadDTO())){
            log.error("======领料出库申请信息为空:{}",JSONObject.toJSON(po));
            return null;
        }
        BizReceiptApplyHeadDTO headDTO = po.getBizReceiptApplyHeadDTO();
        Integer callReceiptType= headDTO.getReceiptType();
        String  callReceiptCode=headDTO.getReceiptCode();
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        /* ====== I_RKPF 预留数据抬头 ====== */
        JSONObject obj_RKPF = new JSONObject();
        // SAP-领料单号
        obj_RKPF.put("ZLLDH", headDTO.getReceiptNum());
        // SAP工厂
        obj_RKPF.put("WERKS", Const.STRING_EMPTY);
        // SAP-预定基准日期
        String strCreateTime = UtilDate.getStringDateForDate(headDTO.getCreateTime()).replace("-", "");
        obj_RKPF.put("RES_DATE",strCreateTime);
        // SAP-用户名
        obj_RKPF.put("CREATED_BY", headDTO.getCreateUserCode());
        // SAP-工作分解结构元素 (WBS 元素) 8位的
        obj_RKPF.put("PSPNR",headDTO.getWhCodeOut());
        // SAP-成本中心 和jiayuan确认可以不传
        obj_RKPF.put("KOSTL", Const.STRING_EMPTY);
        // SAP-主资产号 和jiayuan确认可以不传
        obj_RKPF.put("ANLN1", Const.STRING_EMPTY);

        /* ==================== 预留数据明细 ====================*/
        JSONArray paramArrayRESB = new JSONArray();

        // 需要创建预留的行项目按照行项目rid进行排序
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList().stream()
                .sorted(Comparator.comparing(BizReceiptApplyItemDTO::getRid)).collect(Collectors.toList());
        itemDTOList.forEach(bizReceiptApplyItemDTO -> {
            JSONObject obj_RESB = new JSONObject();
            // SAP-领料单号
            obj_RESB.put("ZLLDH",headDTO.getReceiptNum());
            // SAP-领料单行号
            obj_RESB.put("ZLLDXH", bizReceiptApplyItemDTO.getRid());
            // SAP-工厂
            obj_RESB.put("WERKS", bizReceiptApplyItemDTO.getFtyCode());
            // SAP-批号 和jiayuan确认可以不传
            obj_RESB.put("BATCH", Const.STRING_EMPTY);
            // SAP-库存地点
            obj_RESB.put("STORE_LOC", bizReceiptApplyItemDTO.getLocationCode());
            // SAP-特殊库存
            obj_RESB.put("SOBKZ", bizReceiptApplyItemDTO.getSpecStock());
            // SAP-物料编号
            obj_RESB.put("MATERIAL", bizReceiptApplyItemDTO.getMatCode());
            // SAP-需求量
            obj_RESB.put("QUANTITY", bizReceiptApplyItemDTO.getQty());
            // SAP-组件的需求日期
            String strDate = UtilDate.getStringDateForDate(headDTO.getApplyTime()).replace("-", "");
            obj_RESB.put("REQ_DATE", strDate);
            // SAP-总账科目编号 和jiayuan确认可以不传
            obj_RESB.put("SAKNR", Const.STRING_EMPTY);
            // SAP-订单号 和jiayuan确认可以不传
            obj_RESB.put("AUFNR", Const.STRING_EMPTY);
            // SAP-设备编号 和jiayuan确认可以不传
            obj_RESB.put("EQUNR", Const.STRING_EMPTY);
            // SAP-资产分类 和jiayuan确认可以不传
            obj_RESB.put("ANLKL", Const.STRING_EMPTY);
            // SAP-项目文本 和jiayuan确认可以不传
            obj_RESB.put("SHORT_TEXT", Const.STRING_EMPTY);
            paramArrayRESB.add(obj_RESB);
        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_RKPF", obj_RKPF);
        params.put("T_RESB", paramArrayRESB);
        return params;
    }
    /**
     * 创建预留单请求报文
     * @return
     */
    public static JSONObject getCreateReserveReceiptParamsNew (ReserveReceiptCreatePO po, CurrentUser user){
        JSONObject params = new JSONObject();
        /* ======  I_Import参数 接口通用输入参数 ====== */
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), EnumReceiptType.RESERVE_RECEIPT.getValue().toString(), SapConst.TYPE_TWO);
        if (UtilObject.isNull(po.getBizReceiptApplyHeadDTO())){
            log.error("======领料出库申请信息为空:{}",JSONObject.toJSON(po));
            return null;
        }
        BizReceiptApplyHeadDTO headDTO = po.getBizReceiptApplyHeadDTO();
        Integer callReceiptType= headDTO.getReceiptType();
        String  callReceiptCode=headDTO.getReceiptCode();
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        /* ====== I_RKPF 预留数据抬头 ====== */
        JSONObject obj_RKPF = new JSONObject();
        // SAP-领料单号
        obj_RKPF.put("ZLLDH", headDTO.getReceiptNum());
        // SAP工厂
        obj_RKPF.put("WERKS", Const.STRING_EMPTY);
        // SAP-预定基准日期
        String strCreateTime = UtilDate.getStringDateForDate(headDTO.getCreateTime()).replace("-", "");
        obj_RKPF.put("RES_DATE",strCreateTime);
        // SAP-用户名
        obj_RKPF.put("CREATED_BY", headDTO.getCreateUserCode());
        // SAP-工作分解结构元素 (WBS 元素) 8位的
        obj_RKPF.put("PSPNR",headDTO.getWhCodeOut());
        // SAP-成本中心 和jiayuan确认可以不传
        obj_RKPF.put("KOSTL", Const.STRING_EMPTY);
        // SAP-主资产号 和jiayuan确认可以不传
        obj_RKPF.put("ANLN1", Const.STRING_EMPTY);

        /* ==================== 预留数据明细 ====================*/
        JSONArray paramArrayRESB = new JSONArray();

        // 需要创建预留的行项目按照行项目rid进行排序
        List<BizReceiptApplyItemDTO> itemDTOList = headDTO.getItemList().stream()
                .sorted(Comparator.comparing(BizReceiptApplyItemDTO::getRidReservedNum)).collect(Collectors.toList());
        itemDTOList.forEach(bizReceiptApplyItemDTO -> {
            JSONObject obj_RESB = new JSONObject();
            // SAP-领料单号
            obj_RESB.put("ZLLDH",headDTO.getReceiptNum());
            // SAP-领料单行号
            obj_RESB.put("ZLLDXH", bizReceiptApplyItemDTO.getRidReserved());
            // SAP-工厂
            obj_RESB.put("WERKS", bizReceiptApplyItemDTO.getFtyCode());
            // SAP-批号 和jiayuan确认可以不传
            obj_RESB.put("BATCH", Const.STRING_EMPTY);
            // SAP-库存地点
            obj_RESB.put("STORE_LOC", bizReceiptApplyItemDTO.getLocationCode());
            // SAP-特殊库存
            obj_RESB.put("SOBKZ", bizReceiptApplyItemDTO.getSpecStock());
            // SAP-物料编号
            obj_RESB.put("MATERIAL", bizReceiptApplyItemDTO.getMatCode());
            // SAP-需求量
            obj_RESB.put("QUANTITY", bizReceiptApplyItemDTO.getQty());
            // SAP-组件的需求日期
            String strDate = UtilDate.getStringDateForDate(headDTO.getApplyTime()).replace("-", "");
            obj_RESB.put("REQ_DATE", strDate);
            // SAP-总账科目编号 和jiayuan确认可以不传
            obj_RESB.put("SAKNR", Const.STRING_EMPTY);
            // SAP-订单号 和jiayuan确认可以不传
            obj_RESB.put("AUFNR", Const.STRING_EMPTY);
            // SAP-设备编号 和jiayuan确认可以不传
            obj_RESB.put("EQUNR", Const.STRING_EMPTY);
            // SAP-资产分类 和jiayuan确认可以不传
            obj_RESB.put("ANLKL", Const.STRING_EMPTY);
            // SAP-项目文本 和jiayuan确认可以不传
            obj_RESB.put("SHORT_TEXT", Const.STRING_EMPTY);
            paramArrayRESB.add(obj_RESB);
        });

        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("I_RKPF", obj_RKPF);
        params.put("T_RESB", paramArrayRESB);
        return params;
    }
    /**
     * SAP关闭预留请求报文
     * @param closingItem
     * @return
     */
    public static JSONObject getCloseReservationParams (String closingItem){
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        JSONObject params = new JSONObject();
        // 关闭SAP预留, 返回值 ERP对象
        // JSON转实体List
        List<BizReceiptOutputItemDTO> itemDTOList = JSONObject.parseArray(closingItem,BizReceiptOutputItemDTO.class);
        Integer callReceiptType= itemDTOList.get(0).getReceiptType();
        String  callReceiptCode=itemDTOList.get(0).getReceiptCode();
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        // TODO-BO 2022/5/24 封装SAP关闭预留参数(等SAP预留接口)
        /* ====================  I_IMPORT 接口通用输入参数  ==================== */
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(), itemDTOList.get(0).getReceiptCode(), SapConst.TYPE_ONE);
        params.put("I_RSNUM",itemDTOList.get(0).getReservedOrderCode());
        params.put("IS_IMPORT",paramsOfImport);
        return params;
    }

    /**
     * 创建物料凭证 领料出库
     */
    public static JSONObject getSynCreateStockBinInfoParams (String postingItem){
        //        JSONArray jsonArray = new JSONArray(Collections.singletonList(postingItem));
        List<BizReceiptOutputItemDTO> itemDTOList = JSONObject.parseArray(postingItem,BizReceiptOutputItemDTO.class);
        log.info("需要创建物料凭证的信息-实体:{}",itemDTOList);
        log.info("需要创建物料凭证的信息-JSON:{}",postingItem);
        BizCommonService bizCommonService= UtilSpring.getBean("bizCommonService");
        CurrentUser user = bizCommonService.getUser();
        JSONObject params = new JSONObject();
        JSONObject paramsOfImport = UtilErp.getImport(user.getUserCode(),itemDTOList.get(0).getReceiptCode(), SapConst.TYPE_CREATE_STOCK_BIN_INFO);
        log.info("创建物料凭证的信息:{}",postingItem);
        Integer callReceiptType= itemDTOList.get(0).getReceiptType();
        String  callReceiptCode=itemDTOList.get(0).getReceiptCode();
        params.put("callerReceiptType", callReceiptType);
        params.put("callReceiptCode", callReceiptCode);
        /* ================ IS_HEADER ================ */
        JSONObject obj_IS_HEADER = new JSONObject();
        // 凭证中的过账日期
//        String posingDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getPostingDate());
        String posingDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getPostingDate()).replace("-", "");
        obj_IS_HEADER.put("PSTNG_DATE",posingDate);
        // 凭证中的凭证日期
//        String docDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getDocDate());
        String docDate = UtilDate.getStringDateForDate(itemDTOList.get(0).getDocDate()).replace("-", "");
        obj_IS_HEADER.put("DOC_DATE",docDate);
        // 参考凭证号
        obj_IS_HEADER.put("REF_DOC_NO",itemDTOList.get(0).getMatDocCode());
        // 用户名
        obj_IS_HEADER.put("PR_UNAME",user.getUserCode());

        if(StringUtils.isNotEmpty(itemDTOList.get(0).getReceiptNum())){   //需求计划领用
            obj_IS_HEADER.put("HEADER_TXT",itemDTOList.get(0).getReceiptNum());
            obj_IS_HEADER.put("TXT_LL",itemDTOList.get(0).getActReceiveUserName()); //领料人
            obj_IS_HEADER.put("TXT_FL",itemDTOList.get(0).getSendMatUserName()); //发料人
        }
        /**
         * 佳园0721非要去掉
         凭证抬头文本
         obj_IS_HEADER.put("HEADER_TXT","部分退货");
         */
        /* ================ T_ITEM ================ */
        JSONArray paramsArrayT_ITEM = new JSONArray();
        itemDTOList.forEach(bizReceiptOutputItemDTO -> {
            List<BizReceiptOutputBinDTO> binDTOList = bizReceiptOutputItemDTO.getBinDTOList();
            binDTOList.forEach(bizReceiptOutputBinDTO -> {
                String batchCode = bizReceiptOutputBinDTO.getBatchInfo().getBatchCode();
                JSONObject obj_T_ITEM = new JSONObject();
                // 物料编号
                obj_T_ITEM.put("MATERIAL",bizReceiptOutputItemDTO.getMatCode());
                // 工厂
                obj_T_ITEM.put("PLANT",bizReceiptOutputItemDTO.getFtyCode());
                // 库存地点
                obj_T_ITEM.put("STGE_LOC",bizReceiptOutputItemDTO.getLocationCode());
                // 批号
                obj_T_ITEM.put("BATCH",batchCode);
                // 特殊库存标识
                obj_T_ITEM.put("SPEC_STOCK",bizReceiptOutputItemDTO.getSpecStock());
                // 供应商帐户号
                obj_T_ITEM.put("VENDOR","");
                // 以输入单位计的数量
                obj_T_ITEM.put("ENTRY_QNT",bizReceiptOutputBinDTO.getQty());
                if(bizReceiptOutputItemDTO.getReceiptType().equals(EnumReceiptType.UNITIZED_STOCK_OUTPUT_MAT_REQ.getValue())) {
                    obj_T_ITEM.put("ENTRY_QNT",bizReceiptOutputBinDTO.getAmount());
                }
                if(StringUtils.isNotEmpty(itemDTOList.get(0).getReceiptNum())){ //需求计划领用
                    ErpReceiveReceiptItemDTO receiveReceiptItemDTO =bizReceiptOutputItemDTO.getReceiveReceiptItemDTO();
                    if(receiveReceiptItemDTO !=null){
                        obj_T_ITEM.put("ZWZPL_NO",receiveReceiptItemDTO.getReqPlanNo()); //物资需求计划号
                    }
                }

                // 条目单位
                obj_T_ITEM.put("ENTRY_UOM",bizReceiptOutputItemDTO.getUnitCode());
                // 项目文本
                obj_T_ITEM.put("ITEM_TEXT",bizReceiptOutputItemDTO.getItemRemark());
                // 预留/相关需求的编号
                obj_T_ITEM.put("RESERV_NO",bizReceiptOutputItemDTO.getReservedOrderCode());
                // 预留/相关需求的项目编号
                obj_T_ITEM.put("RES_ITEM",bizReceiptOutputItemDTO.getReservedOrderRid());
                // 该预留的最后发货
                obj_T_ITEM.put("WITHDRAWN",Const.STRING_EMPTY);
                // 工作分解结构元素 (WBS 元素)
                obj_T_ITEM.put("WBS_ELEM",bizReceiptOutputItemDTO.getSpecStockCode());
                // 生产日期
                obj_T_ITEM.put("PROD_DATE",Const.STRING_EMPTY);
                // 工作分解结构元素 (WBS 元素)
                obj_T_ITEM.put("VAL_WBS_ELEM",bizReceiptOutputItemDTO.getSpecStockCode());
                obj_T_ITEM.put("ZDJBH",bizReceiptOutputItemDTO.getReceiptCode());
                obj_T_ITEM.put("ZDJXM",bizReceiptOutputItemDTO.getRid());
                obj_T_ITEM.put("ZHXH",bizReceiptOutputBinDTO.getBid());
                paramsArrayT_ITEM.add(obj_T_ITEM);
            });
        });
        // 组装参数
        params.put("IS_IMPORT", paramsOfImport);
        params.put("IS_HEADER",obj_IS_HEADER);
        params.put("T_ITEM",paramsArrayT_ITEM);

        String erpUrl = String.format(UtilConst.getInstance().getErpUrl(), SapConst.NAME_CREATE_STOCK_BIN_INFO);
        log.debug("调用ERP创建物料凭证信息:{}", params.toJSONString());
        return params;
    }
}
