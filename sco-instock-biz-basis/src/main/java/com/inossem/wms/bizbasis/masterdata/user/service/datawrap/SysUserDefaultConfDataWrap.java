package com.inossem.wms.bizbasis.masterdata.user.service.datawrap;

import com.inossem.wms.bizbasis.masterdata.user.dao.SysUserDefaultConfMapper;
import com.inossem.wms.common.model.auth.user.entity.SysUserDefaultConf;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户默认配置信息表，该表用于记录用户不同业务配置项的个性化信息。通常用于在用户进行某些业务操作时，系统需要记录下不同用户的个性化配置，便于用户操作
 * </p>
 *
 */
@Service
public class SysUserDefaultConfDataWrap extends BaseDataWrap<SysUserDefaultConfMapper, SysUserDefaultConf> {


}
