<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizbasis.erp.dao.ErpSaleReceiptItemMapper">

    <select id="getSaleReceiptItemList" resultType="com.inossem.wms.common.model.erp.dto.ErpSaleReceiptItemDTO">
        SELECT
        esri.id,
        esri.head_id,
        esri.rid,
        esri.spec_stock,
        esri.spec_stock_code,
        esri.spec_stock_name,
        esri.sap_apply_user_code,
        esri.sap_apply_user_name,
        esri.sap_apply_company,
        esri.receipt_qty,
        esri.demand_qty,
        esri.submit_qty,
        esri.batch_erp,
        esri.price,
        esri.money,
        esri.unit_id,
        esri.fty_id,
        esri.wh_id,
        esri.location_id,
        esri.mat_id,
        esri.move_type_id,
        esri.create_time,
        esri.modify_time,
        esrh.erp_receipt_type,
        esrh.erp_receipt_type_name,
        esrh.erp_create_user_code,
        esrh.erp_create_user_name,
        esrh.erp_create_time
        FROM erp_sale_receipt_item esri
        INNER JOIN erp_sale_receipt_head esrh ON esri.head_id = esrh.id
        <where>
            <if test="po.preReceiptCode != null and po.preReceiptCode != '' ">
                AND esrh.receipt_code = #{po.preReceiptCode}
            </if>
            <if test="po.startTime != null ">
                AND date_format(esri.create_time,'%Y%m%d') <![CDATA[ >= ]]> date_format(#{po.startTime},'%Y%m%d')
            </if>
            <if test="po.endTime != null ">
                AND date_format(esri.create_time,'%Y%m%d') <![CDATA[ <= ]]> date_format(#{po.endTime},'%Y%m%d')
            </if>
            <if test="po.ftyId != null ">
                AND esri.fty_id = #{po.ftyId}
            </if>
            <if test="po.locationId != null ">
                AND esri.location_id = #{po.locationId}
            </if>
            <if test="po.matId != null ">
                AND esri.mat_id = #{po.matId}
            </if>
            <if test="po.isReturnFlag != null ">
                AND esrh.is_return_flag = #{po.isReturnFlag}
            </if>
        </where>
    </select>

</mapper>
