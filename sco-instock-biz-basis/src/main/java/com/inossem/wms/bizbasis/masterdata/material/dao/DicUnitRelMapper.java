package com.inossem.wms.bizbasis.masterdata.material.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.inossem.wms.common.model.masterdata.unit.entity.DicUnitRel;
import com.inossem.wms.common.model.masterdata.unit.po.DicUnitRelSearchPO;
import com.inossem.wms.common.model.masterdata.unit.vo.DicUnitRelPageVO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 物料计量单位换算表 Mapper 接口
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-28
 */
@Mapper
public interface DicUnitRelMapper extends WmsBaseMapper<DicUnitRel> {
    /**
     * 物料计量单位换算关系列表分页结果集查询
     *
     * @param page
     * @param wrapper
     * <AUTHOR>
     * @return
     */
    List<DicUnitRelPageVO> selectDicUnitRelPageVOList(IPage<DicUnitRelPageVO> page, @Param(Constants.WRAPPER) QueryWrapper<DicUnitRelSearchPO> wrapper);
}
