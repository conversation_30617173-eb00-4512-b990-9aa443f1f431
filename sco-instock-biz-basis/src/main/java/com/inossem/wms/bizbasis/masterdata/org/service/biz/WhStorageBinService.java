package com.inossem.wms.bizbasis.masterdata.org.service.biz;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.CheckDataService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.EditCacheService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageBinDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicWhStorageSectionDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockBinDataWrap;
import com.inossem.wms.bizbasis.stock.service.datawrap.StockInsDocBinDataWrap;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumCheckType;
import com.inossem.wms.common.enums.EnumIsYesOrNo;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.DicDeleteCheckPO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.masterdata.storagebin.entity.DicWhStorageBin;
import com.inossem.wms.common.model.masterdata.storagebin.po.DicWhStorageBinImport;
import com.inossem.wms.common.model.masterdata.storagebin.po.DicWhStorageBinSavePO;
import com.inossem.wms.common.model.masterdata.storagebin.po.DicWhStorageBinSearchPO;
import com.inossem.wms.common.model.masterdata.storagebin.vo.DicWhStorageBinExportVO;
import com.inossem.wms.common.model.masterdata.storagebin.vo.DicWhStorageBinPageVO;
import com.inossem.wms.common.model.org.section.entity.DicWhStorageSection;
import com.inossem.wms.common.model.org.storagetype.entity.DicWhStorageType;
import com.inossem.wms.common.model.org.wh.entity.DicWh;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.vo.BinEmptyVO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class WhStorageBinService {

    @Autowired
    protected CheckDataService checkDataService;
    @Autowired
    protected DataFillService dataFillService;
    @Autowired
    private DicWhStorageBinDataWrap dicWhStorageBinDataWrap;
    @Autowired
    private EditCacheService editCacheService;
    @Autowired
    protected StockInsDocBinDataWrap stockInsDocBinDataWrap;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private DicWhStorageSectionDataWrap dicWhStorageSectionDataWrap;
    @Autowired
    protected StockBinDataWrap stockBinDataWrap;

    /**
     * 获取仓位列表
     *
     * @param ctx 上下文对象
     * <AUTHOR>
     * @date 2021/3/1
     * @return 仓位详情列表
     */
    public PageObjectVO<DicWhStorageBinPageVO> getPage(BizContext ctx) {
        DicWhStorageBinSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        log.info("获取仓位列表 po：{}", JSONObject.toJSONString(po));
        if (null == po) {
            po = new DicWhStorageBinSearchPO();
        }
        // 查询条件设置
        WmsQueryWrapper<DicWhStorageBinSearchPO> wrapper = new WmsQueryWrapper<>();
        wrapper.lambda()
                .eq(UtilString.isNotNullOrEmpty(po.getWhCode()), DicWhStorageBinSearchPO::getWhCode, DicWh.class, po.getWhCode())
                .eq(UtilString.isNotNullOrEmpty(po.getTypeCode()), DicWhStorageBinSearchPO::getTypeCode, DicWhStorageType.class, po.getTypeCode())
                .eq(UtilString.isNotNullOrEmpty(po.getSectionCode()), DicWhStorageBinSearchPO::getSectionCode, DicWhStorageSection.class, po.getSectionCode())
                .like(UtilString.isNotNullOrEmpty(po.getBinCode()), DicWhStorageBinSearchPO::getBinCode, DicWhStorageBin.class, po.getBinCode())
                .eq(UtilNumber.isNotEmpty(po.getWhId()), DicWhStorageBinSearchPO::getWhId, DicWhStorageBin.class, po.getWhId())
                .eq(UtilNumber.isNotEmpty(po.getTypeId()), DicWhStorageBinSearchPO::getTypeId, DicWhStorageBin.class, po.getTypeId())
                .eq(UtilNumber.isNotEmpty(po.getSectionId()), DicWhStorageBinSearchPO::getSectionId, DicWhStorageBin.class, po.getSectionId())
                .eq(UtilString.isNotNullOrEmpty(po.getOverWeight()), DicWhStorageBinSearchPO::getOverWeight, DicWhStorageBin.class, po.getOverWeight());
        IPage<DicWhStorageBinPageVO> page = po.getPageObj(DicWhStorageBinPageVO.class);
        dicWhStorageBinDataWrap.getDicWhStorageBinPageVOList(page, wrapper);
        long totalCount = page.getTotal();
        return new PageObjectVO<>(page.getRecords(), totalCount);
    }

    /**
     * 获取仓位详情
     *
     * @param ctx 上下文对象
     * @return 仓位详情
     *
     */
    public SingleResultVO<DicWhStorageBinDTO> get(BizContext ctx) {
        Long binId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("仓位详情查询 binId：{}", binId);
        if (UtilNumber.isEmpty(binId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        DicWhStorageBin bin = dicWhStorageBinDataWrap.getById(binId);
        log.info("仓位id：{}，详情：{}", binId, JSONObject.toJSONString(bin));
        DicWhStorageBinDTO dto = UtilBean.newInstance(bin, DicWhStorageBinDTO.class);
        dataFillService.fillRlatAttrForDataObj(dto);
        return new SingleResultVO<>(dto);
    }

    /**
     * 新增或修改方法
     *
     * @param ctx 上下文对象
     *
     */
    public void addOrUpdate(BizContext ctx) {
        DicWhStorageBinSavePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        log.info("新增/修改仓位信息 po：{}", JSONObject.toJSONString(po));
        if (null == po.getStorageBinInfo()) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        DicWhStorageBinDTO dto = po.getStorageBinInfo();

        if (null == dto.getWhId() || null == dto.getTypeId() || UtilString.isNullOrEmpty(dto.getBinCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 根据是否存在ID判断是否为新增
        if (UtilNumber.isEmpty(dto.getId())) {
            // 新增
            dto.setCreateUserId(currentUser.getId());
            DicWhStorageBin dicWhStorageBin=dicWhStorageBinDataWrap.getOne(new QueryWrapper<DicWhStorageBin>() {
                {
                    lambda().eq(DicWhStorageBin::getWhId, dto.getWhId())
                            .eq(DicWhStorageBin::getTypeId,dto.getTypeId())
                            .eq(DicWhStorageBin::getBinCode,dto.getBinCode());
                }
            });
            if(null!=dicWhStorageBin){
                throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_DUPLICATE_POSITION_NUMBER);
            }
        }
        // 修改
        else if (null == dicWhStorageBinDataWrap.getById(dto.getId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_ERROR);
        }
        dto.setModifyUserId(currentUser.getId());
        updateGroupNo(dto);
        DicWhStorageBin bin = UtilBean.newInstance(dto, DicWhStorageBin.class);

        if (dicWhStorageBinDataWrap.saveOrUpdate(bin)) {
            log.info("仓位：{}，保存成功", bin.getBinCode());
            // 刷新缓存
            List<Long> binIdList = new ArrayList<>();
            binIdList.add(bin.getId());
            editCacheService.refreshBinCache(binIdList);
        }
    }

    private void updateGroupNo(DicWhStorageBinDTO entity) {
        String code = entity.getBinCode();
        String[] codes = code.split("-");
        int len = codes.length;
        if (len < 4) {
            entity.setGroupShelfNo("");
            entity.setGroupLineNo("");
            entity.setGroupColumnNo("");
            entity.setGroupBinNo("");
            return;
        }
        String subShelfCode = codes[1];
        int subShelfLen = subShelfCode.length();
        String subLineCode = codes[2];
        int subLineLen = subLineCode.length();
        String subColumnCode = codes[3];
        int subColumnLen = subColumnCode.length();
        if (subShelfLen != 2 || subLineLen != 2 || subColumnLen != 2) {
            entity.setGroupShelfNo("");
            entity.setGroupLineNo("");
            entity.setGroupColumnNo("");
            entity.setGroupBinNo("");
            return;
        }
        StringBuilder fullBuilder = new StringBuilder();
        fullBuilder.append(subShelfCode);
        fullBuilder.append(subLineCode);
        fullBuilder.append(subColumnCode);
        Pattern pattern = Pattern.compile("^[0-9]*$");
        Matcher matcher = pattern.matcher(fullBuilder.toString());
        if (!matcher.find()) {
            entity.setGroupShelfNo("");
            entity.setGroupLineNo("");
            entity.setGroupColumnNo("");
            entity.setGroupBinNo("");
            return;
        }
        if (subShelfLen < 4) {
            StringBuilder builder = new StringBuilder("");
            for (int i = 4; i > subShelfLen; i--) {
                builder.append("0");
            }
            builder.append(subShelfCode);
            entity.setGroupShelfNo(builder.toString());
        }
        if (subLineLen < 4) {
            StringBuilder builder = new StringBuilder("");
            for (int i = 4; i > subLineLen; i--) {
                builder.append("0");
            }
            builder.append(subLineCode);
            entity.setGroupLineNo(builder.toString());
        }
        if (subColumnLen < 4) {
            StringBuilder builder = new StringBuilder("");
            for (int i = 4; i > subColumnLen; i--) {
                builder.append("0");
            }
            builder.append(subColumnCode);
            entity.setGroupColumnNo(builder.toString());
        }
        dataFillService.fillAttr(entity);
        String corpCode = entity.getCorpCode();
        String ftyCode = entity.getFtyCode();
        String groupWhNo = entity.getGroupWhNo();
        String groupTypeNo = entity.getGroupTypeNo();
        if (groupTypeNo == null)
            groupTypeNo = "";
        String groupDepositType = entity.getGroupDepositType();
        if (groupDepositType == null)
            groupDepositType = "";
        StringBuilder noBuilder = new StringBuilder();
        noBuilder.append(corpCode).append("-");
        noBuilder.append(ftyCode).append("-");
        noBuilder.append(groupWhNo).append("-");
        noBuilder.append(groupTypeNo).append("-");
        noBuilder.append(groupDepositType).append("-");
        noBuilder.append(entity.getGroupShelfNo()).append("-");
        noBuilder.append(entity.getGroupLineNo()).append("-");
        noBuilder.append(entity.getGroupColumnNo());
        entity.setGroupBinNo(noBuilder.toString());
    }

    /**
     * 删除方法
     *
     * @param ctx 上下文对象
     *
     */
    public String remove(BizContext ctx) {
        Long binId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        log.info("删除仓位 binId：{}", binId);
        if (UtilNumber.isEmpty(binId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验是否可删除
        checkDataService.dicDeleteCheck(new DicDeleteCheckPO(EnumCheckType.BIN.getValue(), binId));
        String binCode = dicWhStorageBinDataWrap.getById(binId).getBinCode();
        // 逻辑删除
        if (dicWhStorageBinDataWrap.removeById(binId)) {
            log.info("仓位：{}，删除成功", binId);
            // 从缓存中删除
            editCacheService.deleteBinCache(binId);
        }else{
            throw new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_DELETE_FAILURE, binCode);
        }
        return binCode;
    }

    /**
     * 仓位对外服务接口
     * 根据条件查询仓位列表
     * @param queryWrapper 查询条件
     * @return List<DicWhStorageBin>
     */
    public List<DicWhStorageBin> getWhStorageBinList(QueryWrapper<DicWhStorageBin> queryWrapper) {
        return dicWhStorageBinDataWrap.list(queryWrapper);
    }
    /* *************************************************MQ*************************************************************/
    /**
     * 修改仓位使用状态 刷新仓位缓存
     * @param binIdList
     */
    @WmsMQListener(tags = TagConst.UPDATE_BIN_EMPTY_STATUS)
    public void updateBinEmpty(List<Long> binIdList) {
        //先查询是否空仓位，再修改仓位主数据，防止死锁
        List<BinEmptyVO> binEmptyVOList = stockInsDocBinDataWrap.selectBinIsEmpty(binIdList);

        //过滤仓位主数.仓位库存is_empty 标识不相等的项目。
        binEmptyVOList = binEmptyVOList.stream().filter(obj -> obj.getBinIsEmpty().compareTo(obj.getIsEmpty()) != 0).collect(Collectors.toList());
        if(UtilCollection.isNotEmpty(binEmptyVOList)){
            stockInsDocBinDataWrap.updateBinIsEmpty(binEmptyVOList);
            //刷新缓存
            List<Long> relBinIdList = binEmptyVOList.stream().map(BinEmptyVO::getBinId).collect(Collectors.toList());
            editCacheService.refreshBinCache(binIdList);
        }
    }

    /**
     * 仓位主数据EXCEL导入
     * @param ctx
     */
    public void importStorageBin(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        CurrentUser user = ctx.getCurrentUser();

        try {
            //获取EXCEL数据
            List<DicWhStorageBinImport> whStorageBinList = (List<DicWhStorageBinImport>) UtilExcel.readExcelData(file.getInputStream(), DicWhStorageBinImport.class);
            //判断EXCEL中主键重复的值
            Map<String, List<DicWhStorageBinImport>> checkMap = whStorageBinList.stream().collect(Collectors.groupingBy(item -> item.getWhCode() + "-" + item.getTypeCode() + "-" + item.getBinCode()));
            for (String key : checkMap.keySet()) {
                List<DicWhStorageBinImport> checkList = checkMap.get(key);
                if(checkList.size() > 1){
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEL_HAS_SAME_CODE,key);
                }
            }

            whStorageBinList.forEach(
                    bin -> {
                        //仓位已存在校验 ： 根据仓库号，存储类型，仓位吗，判断数据是否存在
                        Long binId = dictionaryService.getBinIdCacheByCode(bin.getWhCode(),bin.getTypeCode(),bin.getBinCode());
                        if(!UtilNumber.isEmpty(binId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_EXIST,bin.getWhCode() + "-" + bin.getTypeCode() + "-" + bin.getBinCode());
                        }
                        //仓库校验
                        Long whId = dictionaryService.getWhIdCacheByCode(bin.getWhCode());
                        if(UtilNumber.isEmpty(whId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_WAREHOUSE_NOT_EXIST,bin.getWhCode());
                        }
                        //存储类型校验
                        Long typeId = dictionaryService.getStorageTypeIdCacheByCode(bin.getWhCode(),bin.getTypeCode());
                        if(UtilNumber.isEmpty(typeId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_TYPE_NOT_EXIST,bin.getWhCode() + "-" + bin.getTypeCode());
                        }
                        //存储区校验
                        Long sectionId = dictionaryService.getStorageSectionIdCacheByCode(typeId,bin.getSectionCode());
                        if(UtilNumber.isEmpty(sectionId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_TYPE_NOT_EXIST,bin.getTypeCode() + "-" + bin.getSectionCode());
                        }

                        //公司校验
                        Long corpId  = dictionaryService.getCorpIdCacheByCode( bin.getCorpCode());
                        if(UtilNumber.isEmpty(sectionId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_CORP_NOT_EXIST,bin.getTypeCode() + "-" + bin.getSectionCode());
                        }

                        //工厂校验
                        Long ftyId = dictionaryService.getFtyIdCacheByCode( bin.getFtyCode());
                        if(UtilNumber.isEmpty(sectionId)){
                            throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST,bin.getTypeCode() + "-" + bin.getSectionCode());
                        }
                        bin.setCorpId(corpId);
                        bin.setFtyId(ftyId);
                        bin.setSectionId(sectionId);
                        bin.setWhId(whId);
                        bin.setTypeId(typeId);
                        bin.setCreateUserId(user.getId());
                        bin.setModifyUserId(user.getId());
                    }
            );
            //批量插入数据
            dicWhStorageBinDataWrap.saveBatchDto(whStorageBinList);

            List<Long> binId = new ArrayList<Long>();
            for(DicWhStorageBinImport storageBin : whStorageBinList){
                binId.add(storageBin.getId());
            }
            //刷新缓存
            editCacheService.refreshBinCache(binId);
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    /**
     * 根据存储类型id获取存储区
     * @param typeId 存储类型id
     * @return
     */
    public MultiResultVO<DicWhStorageSection> getSectionByTypeId(long typeId) {
        // 该方法暂时不可用有问题  先查库
        List<DicWhStorageSection>  dicWhStorageSectionList = dicWhStorageSectionDataWrap
                .selectSectionByTypeId(typeId);

        return new MultiResultVO<>(dicWhStorageSectionList);
    }

    /**
     * 计算所有 仓位 库存是否超重
     */
    public void calculateStockBinOverWeightJob() {
        List<DicWhStorageBin> updateList = new ArrayList<>();
        List<StockBin> list = stockBinDataWrap.list();

        Map<Long, List<StockBin>> collect = list.stream().collect(Collectors.groupingBy(e -> e.getBinId()));
        for (Map.Entry<Long, List<StockBin>> entry : collect.entrySet()) {
            DicWhStorageBinDTO bin = dictionaryService.getBinCacheById(entry.getKey());
            System.out.println(bin.getBinCode());
            //仓位库存实际重量
            BigDecimal totalWeight = BigDecimal.ZERO;
            for (StockBin stockBin : entry.getValue()) {
                DicMaterialDTO mat = dictionaryService.getMatCacheById(stockBin.getMatId());
                if(UtilObject.isNull(mat)){
                    continue;
                }
                BigDecimal qty = BigDecimal.ZERO;
                if (UtilNumber.isNotEmpty(stockBin.getQty())) {
                    qty = qty.add(stockBin.getQty());
                }
                if (UtilNumber.isNotEmpty(stockBin.getQtyTransfer())) {
                    qty = qty.add(stockBin.getQtyTransfer());
                }
                if (UtilNumber.isNotEmpty(stockBin.getQtyInspection())) {
                    qty = qty.add(stockBin.getQtyInspection());
                }
                if (UtilNumber.isNotEmpty(stockBin.getQtyFreeze())) {
                    qty = qty.add(stockBin.getQtyFreeze());
                }
                if (UtilNumber.isNotEmpty(stockBin.getQtyHaste())) {
                    qty = qty.add(stockBin.getQtyHaste());
                }
                if (UtilNumber.isNotEmpty(stockBin.getQtyTemp())) {
                    qty = qty.add(stockBin.getQtyTemp());
                }
                BigDecimal binWeight = qty.multiply(mat.getGrossWeight());
                totalWeight = totalWeight.add(binWeight);
            }
            BigDecimal storageBinWeight = UtilNumber.isEmpty(bin.getWeight()) ? BigDecimal.ZERO : bin.getWeight();
            String overWeight = totalWeight.compareTo(storageBinWeight) > 0 ? EnumIsYesOrNo.YES.getValue() : EnumIsYesOrNo.NO.getValue();

            DicWhStorageBin update  = new DicWhStorageBin();
            update.setId(entry.getKey());
            update.setOverWeight(overWeight);
            updateList.add(update);
        }
        dicWhStorageBinDataWrap.updateBatchById(updateList);
    }

    /**
     * 导出Excel
     */
    public void export(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("仓位主数据"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        PageObjectVO<DicWhStorageBinPageVO> vo = this.getPage(ctx);
        List<DicWhStorageBinExportVO> list = UtilCollection.toList(vo.getResultList(), DicWhStorageBinExportVO.class);
        UtilExcel.writeExcel(DicWhStorageBinExportVO.class, list, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");

        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());

        return fileName + "-" + yyyyMmDd;
    }
}
