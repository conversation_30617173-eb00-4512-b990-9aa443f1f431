package com.inossem.wms.bizbasis.masterdata.wbs.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.inossem.wms.bizbasis.masterdata.wbs.service.biz.DicWbsService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.mat.info.po.LogMaterialNetWeightRecordSearchPO;
import com.inossem.wms.common.model.masterdata.wbs.entity.DicWbs;
import com.inossem.wms.common.model.masterdata.wbs.po.DicWbsSearchPO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * WBS主数据管理
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Slf4j
@RestController
@Api(tags = "WBS主数据")
public class DicWbsController {

    @Autowired
    protected DicWbsService dicWbsService;

    @ApiOperation(value = "分页查询", tags = {"WBS主数据"})
    @PostMapping(value = "/wbs/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicWbs>> getPage(@RequestBody DicWbsSearchPO po, BizContext ctx) {
        dicWbsService.getPage(ctx);
        PageObjectVO<DicWbs> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "同步WBS", tags = {"WBS主数据"})
    @PostMapping(value = "/wbs/sync", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> sync(@RequestBody LogMaterialNetWeightRecordSearchPO po, BizContext ctx) {
        dicWbsService.sync(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }
} 