package com.inossem.wms.bizdomain.bi.service.component;

import com.inossem.wms.bizdomain.exchangerate.service.datawrap.DicExchangeRateDataWrap;
import com.inossem.wms.common.model.masterdata.exchangerate.entity.DicExchangeRate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * BiPurchaseComponent 汇率转换功能测试
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@ExtendWith(MockitoExtension.class)
public class BiPurchaseComponentTest {

    @Mock
    private DicExchangeRateDataWrap dicExchangeRateDataWrap;

    @InjectMocks
    private BiPurchaseComponent biPurchaseComponent;

    // 币种常量
    private static final Integer CURRENCY_USD = 10; // 美元
    private static final Integer CURRENCY_CNY = 20; // 人民币
    private static final Integer CURRENCY_PKR = 30; // 卢比

    @Test
    public void testConvertToUSD_WithUSD() throws Exception {
        // 测试美元转美元，应该直接返回原金额
        BigDecimal amount = new BigDecimal("1000.00");
        Integer currency = CURRENCY_USD;
        Integer year = 2024;
        Integer month = 1;

        BigDecimal result = invokeConvertToUSD(amount, currency, year, month);
        
        assertEquals(amount, result);
    }

    @Test
    public void testConvertToUSD_WithCNY() throws Exception {
        // 测试人民币转美元
        BigDecimal amount = new BigDecimal("7000.00"); // 7000人民币
        Integer currency = CURRENCY_CNY;
        Integer year = 2024;
        Integer month = 1;

        // 模拟汇率数据：1美元 = 7人民币
        DicExchangeRate exchangeRate = new DicExchangeRate();
        exchangeRate.setYear(year);
        exchangeRate.setMonth(month);
        exchangeRate.setCnyRate(new BigDecimal("7.00"));
        
        when(dicExchangeRateDataWrap.getOne(any())).thenReturn(exchangeRate);

        BigDecimal result = invokeConvertToUSD(amount, currency, year, month);
        
        // 7000 / 7 = 1000 美元
        assertEquals(new BigDecimal("1000.00"), result);
    }

    @Test
    public void testConvertToUSD_WithPKR() throws Exception {
        // 测试卢比转美元
        BigDecimal amount = new BigDecimal("280000.00"); // 280000卢比
        Integer currency = CURRENCY_PKR;
        Integer year = 2024;
        Integer month = 1;

        // 模拟汇率数据：1美元 = 280卢比
        DicExchangeRate exchangeRate = new DicExchangeRate();
        exchangeRate.setYear(year);
        exchangeRate.setMonth(month);
        exchangeRate.setPkrRate(new BigDecimal("280.00"));
        
        when(dicExchangeRateDataWrap.getOne(any())).thenReturn(exchangeRate);

        BigDecimal result = invokeConvertToUSD(amount, currency, year, month);
        
        // 280000 / 280 = 1000 美元
        assertEquals(new BigDecimal("1000.00"), result);
    }

    @Test
    public void testConvertToUSD_NoExchangeRate() throws Exception {
        // 测试找不到汇率数据时使用默认汇率
        BigDecimal amount = new BigDecimal("7000.00");
        Integer currency = CURRENCY_CNY;
        Integer year = 2024;
        Integer month = 1;

        when(dicExchangeRateDataWrap.getOne(any())).thenReturn(null);

        BigDecimal result = invokeConvertToUSD(amount, currency, year, month);
        
        // 使用默认汇率 7.0：7000 / 7 = 1000 美元
        assertEquals(new BigDecimal("1000.00"), result);
    }

    /**
     * 通过反射调用私有方法 convertToUSD
     */
    private BigDecimal invokeConvertToUSD(BigDecimal amount, Integer currency, Integer year, Integer month) throws Exception {
        Method method = BiPurchaseComponent.class.getDeclaredMethod("convertToUSD", BigDecimal.class, Integer.class, Integer.class, Integer.class);
        method.setAccessible(true);
        return (BigDecimal) method.invoke(biPurchaseComponent, amount, currency, year, month);
    }
}