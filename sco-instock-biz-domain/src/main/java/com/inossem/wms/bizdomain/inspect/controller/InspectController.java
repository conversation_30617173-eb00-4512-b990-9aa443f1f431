package com.inossem.wms.bizdomain.inspect.controller;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectWriteOffPO;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectHeadVO;
import com.inossem.wms.common.model.label.dto.BizReceiptPalletSortingItemDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.inossem.wms.bizdomain.inspect.service.biz.InspectService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectDeletePO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectSearchPO;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectUpdatePO;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectPdaLabelVo;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectPreHeadVo;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import io.swagger.annotations.ApiOperation;

/**
 * <p>
 * 采购验收 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-09
 */

@RestController
@Api(tags = "验收管理")
public class InspectController {

    @Autowired
    private InspectService bizInspectNoticeHeadService;

    /**
     * 采购验收-初始化
     *
     * @param ctx 入参上下文
     */
    @ApiOperation(value = "采购验收-初始化", tags = {"验收管理-采购验收"})
    @PostMapping(value = "/purchase/inspects/init", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInspectHeadDTO>> init(BizContext ctx) {
        bizInspectNoticeHeadService.init(ctx);
        BizResultVO<BizReceiptInspectHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 采购验收单-分页
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件对象"}
     * @return 采购验收单分页信息
     */
    @ApiOperation(value = "采购验收-分页", tags = {"验收管理-采购验收"})
    @PostMapping(value = "/purchase/inspects/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<BizReceiptInspectHeadVO>> getPage(@RequestBody BizReceiptInspectSearchPO po,
        BizContext ctx) {
        bizInspectNoticeHeadService.getPage(ctx);
        PageObjectVO<BizReceiptInspectHeadVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 采购验收-详情
     *
     * @param id 主表id
     * @param ctx 入参上下文 {"id":"主表id"}
     * @return 采购验收单详情
     */
    @ApiOperation(value = "采购验收-详情", tags = {"验收管理-采购验收"})
    @GetMapping(value = "/purchase/inspects/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInspectHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        bizInspectNoticeHeadService.getInfo(ctx);
        BizResultVO<BizReceiptInspectHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 采购验收-保存
     *
     * @param po 保存采购验收单表单
     * @param ctx 入参上下文 {"po":"保存采购验收单表单"}
     * @return 验收单号
     */
    @ApiOperation(value = "采购验收-保存", tags = {"验收管理-采购验收"})
    @PostMapping(value = "/purchase/inspects/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> save(@RequestBody BizReceiptInspectHeadDTO po, BizContext ctx) {
        bizInspectNoticeHeadService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_SAVE_SUCCESS, code);
    }

    /**
     * 采购验收-提交
     *
     * @param po 提交采购验收单表单
     * @param ctx 入参上下文 {"po":"提交采购验收单表单"}
     * @return 验收单号
     */
    @ApiOperation(value = "采购验收-提交", tags = {"验收管理-采购验收"})
    @PostMapping(value = "/purchase/inspects/submit", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> submit(@RequestBody BizReceiptInspectHeadDTO po, BizContext ctx) {
        bizInspectNoticeHeadService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_SUBMIT_SUCCESS, code);
    }

    /**
     * 采购验收-删除
     *
     * @param po 删除出的行项目id
     * @param ctx 入参上下文 {"po":"删除出的行项目id"}
     */
    @ApiOperation(value = "采购验收-删除", tags = {"验收管理-采购验收"})
    @DeleteMapping("/purchase/inspects/ids")
    public BaseResult<Long> delete(@RequestBody BizReceiptInspectDeletePO po, BizContext ctx) {
        bizInspectNoticeHeadService.delete(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_DELETE_SUCCESS, po.getReceiptCode());
    }

    /**
     * 采购验收-前续单据
     *
     * @param po 查询条件
     * @param ctx 入参上下文 {"po":"查询条件"}
     * @return 物料信息
     */
    @ApiOperation(value = "采购验收-前续单据【220:送货通知 240:采购订单 模板:290】", tags = {"验收管理-采购验收"})
    @PostMapping(value = "/purchase/inspects/mat-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptInspectPreHeadVo>>
        getReferReceiptItemList(@RequestBody BizReceiptPreSearchPO po, BizContext ctx) {
        bizInspectNoticeHeadService.getReferReceiptItemList(ctx);
        MultiResultVO<BizReceiptInspectPreHeadVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * PDA端采购验收-详情
     *
     * @param id 主表id
     * @param ctx 入参上下文 {"id":"主表id"}
     * @return PDA验收列表
     */
    @ApiOperation(value = "PDA端采购验收-详情", tags = {"验收管理-PDA端"})
    @GetMapping(value = "/purchase/inspects/pda-info/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<BizReceiptInspectHeadDTO>> getPdaInfo(@PathVariable("id") Long id, BizContext ctx) {
        bizInspectNoticeHeadService.getPdaInfo(ctx);
        BizResultVO<BizReceiptInspectHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * PDA端采购验收-打印
     *
     * @param po 验收表单
     * @param ctx 入参上下文 {"po":"验收表单"}
     * @return 验收管理Pda端标签相关数据
     */
    @ApiOperation(value = "PDA端采购验收-打印", tags = {"验收管理-PDA端"})
    @PostMapping(value = "/purchase/inspects/pda-print", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptInspectPdaLabelVo> pdaPrint(@RequestBody BizReceiptInspectUpdatePO po, BizContext ctx) {
        bizInspectNoticeHeadService.pdaPrint(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_PRINT_SUCCESS);
    }

    /**
     * PDA端采购验收-验收
     *
     * @param po 验收表单
     * @param ctx 入参上下文 {"po":"验收表单"}
     * @return 待码盘数据集合
     */
    @ApiOperation(value = "PDA端采购验收-验收", tags = {"验收管理-PDA端"})
    @PostMapping(value = "/purchase/inspects/pda-inspect", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptInspectPdaLabelVo> pdaInspect(@RequestBody BizReceiptInspectHeadDTO po,
        BizContext ctx) {
        bizInspectNoticeHeadService.pdaInspect(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_SUCCESS);
    }

    /**
     * PDA端采购验收-撤销
     *
     * @param po 撤销入参
     * @param ctx ctx 入参上下文 {"po":"撤销入参"}
     * @return PDA验收列表
     */
    @ApiOperation(value = "PDA端采购验收-撤销", tags = {"验收管理-PDA端"})
    @PostMapping(value = "/purchase/inspects/pda-revoke", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptInspectHeadDTO> pdaRevoke(@RequestBody BizReceiptInspectUpdatePO po, BizContext ctx) {
        bizInspectNoticeHeadService.pdaRevoke(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_REVOKE_SUCCESS);
    }

    /**
     * PDA端采购验收-生成码盘
     *
     * @param po PDA验收表单
     * @param ctx 入参上下文 {"po":"PDA验收表单"}
     * @return 生成的码盘数据
     */
    @ApiOperation(value = "PDA端采购验收-生成码盘", tags = {"验收管理-PDA端"})
    @PostMapping(value = "/purchase/inspects/save-pallet-sorting", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptInspectPdaLabelVo> savePalletSorting(@RequestBody BizReceiptInspectHeadDTO po,
        BizContext ctx) {
        bizInspectNoticeHeadService.savePalletSorting(ctx);
        BizReceiptInspectPdaLabelVo vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * PDA端采购验收-删除码盘
     *
     * @param po PDA验收表单
     * @param ctx 入参上下文 {"po":"PDA验收表单"}
     * @return 待码盘数据、已码盘数据
     */
    @ApiOperation(value = "PDA端采购验收-删除码盘", tags = {"验收管理-PDA端"})
    @PostMapping(value = {"/purchase/inspects/delete-pallet-sorting"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizReceiptInspectPdaLabelVo> deletePalletSorting(@RequestBody BizReceiptPalletSortingItemDTO po,
        BizContext ctx) {
        bizInspectNoticeHeadService.deletePalletSorting(ctx);
        BizReceiptInspectPdaLabelVo vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * WEB端 已完成状态冲销
     *
     * @param po WEB验收表单
     * @param ctx 入参上下文 {"po":"WEB验收表单"}
     * @return 单据号
     */
    @ApiOperation(value = "WEB端采购验收-冲销", tags = {"验收管理-WEB端"})
    @PostMapping(value = {"/purchase/inspects/write-off"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<String> writeOff(@RequestBody BizReceiptInspectWriteOffPO po, BizContext ctx) {
        bizInspectNoticeHeadService.writeOff(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_INSPEC_WRITE_OFF_SUCCESS, po.getReceiptCode());
    }

}