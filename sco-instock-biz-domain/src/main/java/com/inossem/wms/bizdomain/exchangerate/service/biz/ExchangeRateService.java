package com.inossem.wms.bizdomain.exchangerate.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizdomain.exchangerate.service.datawrap.DicExchangeRateDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.exchangerate.dto.DicExchangeRateDTO;
import com.inossem.wms.common.model.masterdata.exchangerate.entity.DicExchangeRate;
import com.inossem.wms.common.model.masterdata.exchangerate.po.DicExchangeRateSearchPO;
import com.inossem.wms.common.model.masterdata.exchangerate.vo.DicExchangeRatePageVO;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 汇率主数据业务服务类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
@Slf4j
public class ExchangeRateService {

    @Autowired
    private DicExchangeRateDataWrap dicExchangeRateDataWrap;

    @Autowired
    private DataFillService dataFillService;

    /**
     * 获取分页数据
     *
     * @param ctx 业务上下文对象，包含查询参数
     * @return 返回包含分页数据和总记录数的PageObjectVO对象
     */
    public PageObjectVO<DicExchangeRatePageVO> getPage(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        DicExchangeRateSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 构建查询条件
        LambdaQueryWrapper<DicExchangeRate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UtilObject.isNotNull(po.getYear()), DicExchangeRate::getYear, po.getYear())
                .eq(UtilObject.isNotNull(po.getMonth()), DicExchangeRate::getMonth, po.getMonth())
                .orderByDesc(DicExchangeRate::getYear, DicExchangeRate::getMonth);

        List<DicExchangeRatePageVO> result;
        long total;

        // 分页查询处理
        if (po.isPaging()) {
            IPage<DicExchangeRate> page = po.getPageObj(DicExchangeRate.class);
            dicExchangeRateDataWrap.page(page, queryWrapper);
            result = UtilCollection.toList(page.getRecords(), DicExchangeRatePageVO.class);
            total = page.getTotal();
        } else {
            List<DicExchangeRate> entityList = dicExchangeRateDataWrap.list(queryWrapper);
            result = UtilCollection.toList(entityList, DicExchangeRatePageVO.class);
            total = result.size();
        }

        dataFillService.fillAttr(result);
        return new PageObjectVO<>(result, total);
    }

    /**
     * 获取汇率主数据详情的方法。
     *
     * @param ctx 业务上下文，包含请求参数
     * @return 返回包含汇率主数据的单结果对象
     */
    public DicExchangeRateDTO getInfo(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* id非空效验 ******** */
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        DicExchangeRate dicExchangeRate = dicExchangeRateDataWrap.getById(id);

        DicExchangeRateDTO dicExchangeRateDTO = UtilBean.newInstance(dicExchangeRate, DicExchangeRateDTO.class);
        dataFillService.fillAttr(dicExchangeRateDTO);

        return dicExchangeRateDTO;
    }

    /**
     * 添加或更新汇率主数据。
     *
     * @param ctx 业务上下文，包含当前用户和待处理的数据对象
     */
    public void save(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        CurrentUser currentUser = ctx.getCurrentUser();
        DicExchangeRateDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 参数校验
        if (UtilObject.isNull(po.getYear()) || UtilObject.isNull(po.getMonth())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }

        // 新增
        if (UtilNumber.isEmpty(po.getId())) {
            // 检查同年同月是否已存在汇率数据
            LambdaQueryWrapper<DicExchangeRate> queryWrapper = new LambdaQueryWrapper<DicExchangeRate>()
                    .eq(DicExchangeRate::getYear, po.getYear())
                    .eq(DicExchangeRate::getMonth, po.getMonth());

            DicExchangeRate existingRate = dicExchangeRateDataWrap.getOne(queryWrapper);
            if (UtilObject.isNotNull(existingRate)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_EXIST, po.getYear() + Const.HYPHEN + po.getMonth());
            }

            po.setId(null);
            po.setCreateUserId(currentUser.getId());
            dicExchangeRateDataWrap.saveDto(po);
        } else {
            // 更新时检查同年同月是否已存在其他汇率数据
            LambdaQueryWrapper<DicExchangeRate> queryWrapper = new LambdaQueryWrapper<DicExchangeRate>()
                    .eq(DicExchangeRate::getYear, po.getYear())
                    .eq(DicExchangeRate::getMonth, po.getMonth())
                    .ne(DicExchangeRate::getId, po.getId());

            DicExchangeRate existingRate = dicExchangeRateDataWrap.getOne(queryWrapper);
            if (UtilObject.isNotNull(existingRate)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_EXIST, po.getYear() + Const.HYPHEN + po.getMonth());
            }

            po.setModifyUserId(currentUser.getId());
            dicExchangeRateDataWrap.updateDtoById(po);
        }
    }

    /**
     * 删除汇率主数据
     *
     * @param ctx 入参上下文
     */
    public void remove(BizContext ctx) {
        /* ********* 从上下文获取参数 ******** */
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* id非空效验 ******** */
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        // 查询汇率主数据
        DicExchangeRate dicExchangeRate = dicExchangeRateDataWrap.getById(id);

        // 汇率数据不存在
        if (UtilObject.isNull(dicExchangeRate)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_DATA_NOT_EXIST, String.valueOf(id));
        }

        // 删除汇率数据
        dicExchangeRateDataWrap.removeById(id);
    }

    /**
     * 根据年月查询汇率数据
     *
     * @param year 年份
     * @param month 月份
     * @return 汇率数据，如果未找到则返回null
     */
    public DicExchangeRate getExchangeRateByYearMonth(Integer year, Integer month) {
        if (UtilNumber.isEmpty(year) || UtilNumber.isEmpty(month)) {
            return null;
        }

        LambdaQueryWrapper<DicExchangeRate> queryWrapper = new LambdaQueryWrapper<DicExchangeRate>()
                .eq(DicExchangeRate::getYear, year)
                .eq(DicExchangeRate::getMonth, month);

        return dicExchangeRateDataWrap.getOne(queryWrapper, false);
    }

}
