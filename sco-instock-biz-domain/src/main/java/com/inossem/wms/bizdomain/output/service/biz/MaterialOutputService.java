package com.inossem.wms.bizdomain.output.service.biz;

import com.inossem.wms.bizbasis.masterdata.user.service.biz.UserService;
import com.inossem.wms.bizdomain.apply.service.component.MaterialOrderOutApplyComponent;
import com.inossem.wms.bizdomain.output.service.component.MaterialOutputComponent;
import com.inossem.wms.bizdomain.output.service.component.OtherOutputComponent;
import com.inossem.wms.bizdomain.output.service.component.OutputComponent;
import com.inossem.wms.bizdomain.output.service.component.callback.OutputTaskCallbackComponent;
import com.inossem.wms.bizdomain.task.service.component.UnLoadComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.po.BizReceiptOutputSearchPO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilSpring;
import com.inossem.wms.system.log.service.biz.ExceptionLogHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> wang
 * @description
 * @date 2022/4/24 13:32
 */
@Slf4j
@Service
public class MaterialOutputService {
    @Autowired
    private MaterialOutputComponent materialOutputComponent;

    @Autowired
    private OutputComponent outputComponent;

    @Autowired
    private OtherOutputComponent otherOutputComponent;

    @Autowired
    private MaterialOrderOutApplyComponent materialOrderOutApplyComponent;

    @Autowired
    private OutputTaskCallbackComponent outputTaskCallbackComponent;
    @Autowired
    private UnLoadComponent unLoadComponent;
    @Autowired
    private ExceptionLogHandler exceptionLogHandler;


    /**
     *初始化出库单
     * @param ctx - 上下文
     */
    public void init(BizContext ctx) {
        // 设置按钮
        materialOutputComponent.setInit(ctx);
        // 开启单据流
        outputComponent.setExtendRelation(ctx);
        // 开启审批
         outputComponent.setExtendWf(ctx);
        // 开启附件
        outputComponent.setExtendAttachment(ctx);
        // 开启操作日志
        outputComponent.setExtendOperationLog(ctx);

    }

    /**
     * 分页获取出库列表
     * @param ctx
     */
    public void getPage(BizContext ctx) {
//        outputComponent.getPage(ctx);
        materialOutputComponent.getPage(ctx);
    }

    /**
     * 获取物料库存
     */
    public void getMatStockInfo(BizContext ctx) {
        this.getMatStockInfoNoSameTime(ctx);
    }


    /**
     * 获取领料出库详情
     * @param ctx
     */
    public void getInfo(BizContext ctx) {
        // 获取详情
        materialOutputComponent.getInfo(ctx);
        // 设置批次图片
        outputComponent.setBatchImg(ctx);
        // 开启单据流
        outputComponent.setExtendRelation(ctx);
        // 开启审批
        // outputComponent.setExtendWf(ctx);
        // 开启附件
        outputComponent.setExtendAttachment(ctx);
        // 开启操作日志
        outputComponent.setExtendOperationLog(ctx);
    }

    /**
     * 领料出库-处理
     * @param ctx
     */
    public void deal(BizContext ctx) {
        // 出库配货策略
        outputComponent.autoDistribution(ctx);
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
    }

    /**
     * 领料出库详情-打印
     * @param ctx
     */
    public void printInfo(BizContext ctx) {
        // 获取打印数据
        materialOutputComponent.getPrintInfo(ctx);
    }


    /**
     * 保存单据
     * @param ctx
     */
    public void save(BizContext ctx) {
        this.saveNoSameTime(ctx);
    }

    /**
     * 先作业 提交un
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {
        // 校验
        materialOutputComponent.checkSubmit(ctx);
        // 提交单据
        materialOutputComponent.submitReceipt(ctx);
        // 保存操作日志
        outputComponent.saveBizReceiptOperationLog(ctx);
        // 保存附件
        outputComponent.saveBizReceiptAttachment(ctx);
        // 保存单据流
        materialOutputComponent.saveReceiptTree(ctx);
        // 【先作业模式】状态变更已提交
        outputComponent.updateStatusSubmitted(ctx);
        // 发送下架请求
        outputComponent.addTaskRequest(ctx);
    }

    /**
     * 获取配货信息
     * @param ctx
     */
    public void getItemInfo(BizContext ctx) {
        this.getItemInfoNoSameTime(ctx);
    }


    /**
     * 手动点击过账
     * @param ctx
     */
    public void post(BizContext ctx) {
        this.postTaskFirst(ctx);
    }

    /**
     * 手动点击过账
     * @param ctx
     */
    public void reqPlanPost(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Integer receiptStatus = headDTO.getReceiptStatus();
        // 校验
        outputComponent.checkPost(ctx);
        // 校验实际领料人是否存在，并保存
        materialOutputComponent.checkAndSaveActualReceiver(ctx);
        // 2023-07-04 根据业务方需求，增加出库过账批次信息的到期日期限制，所过账的物料要求批次信息的到期时间应在当前日期之后（即已到期的物资不可出库）
        materialOutputComponent.checkLifetimeDate(ctx);
        //7.11 列表页增加发料人，发料人取领料出库单点击“过账”或“关闭预留”的操作用户
        materialOutputComponent.updateSendUser(ctx);
        // 保存签名
        materialOutputComponent.saveAutograph(ctx);
        // 生成移动类型
        materialOutputComponent.generateInsMoveTypeAndCheck(ctx);
        // sap过账
        materialOutputComponent.postToSap(ctx);
        // InStock过账
        outputComponent.postToInStock(ctx);
        // 单据状态已完成
        outputComponent.updateStatusCompleted(ctx);
        // 作业中过账时处理下架作业请求
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue())) {
            unLoadComponent.closeUnLoadReq(headDTO.getId());
        }
    }

    /**
     * 冲销
     * @param ctx
     */
    public void writeOff(BizContext ctx) {
        // 校验
        outputComponent.checkWriteOff(ctx);
        // 生成冲销移动类型【非先过账模式】
        materialOutputComponent.generateWriteOffInsMoveTypeAndCheckNonPostFirst(ctx);
        // sap冲销
        materialOutputComponent.writeOffToSap(ctx);
        // InStock冲销
        outputComponent.writeOffToInStock(ctx);
        //单品冲销【先过账模式】
        outputComponent.writeOffToInStockLable(ctx);
        // 推送冲销修改请求
        outputComponent.addWriteOffRequest(ctx);
        // 更新批次维保日期
        materialOutputComponent.updateBatchMaintenanceDate(ctx);
    }

    /*============================================*/
    /**
     * 非同时模式 获取物料库存
     * @param ctx
     */
    private void getMatStockInfoNoSameTime(BizContext ctx) {
        // 获取物料特性库存【非同时模式】
        outputComponent.getMatFeatureStock(ctx);
    }


    /**
     * 非同时模式 保存
     * @param ctx
     */
    private void saveNoSameTime(BizContext ctx) {
        // 校验数据
        outputComponent.checkSave(ctx);
        // 保存单据抬头及下级信息
        materialOutputComponent.saveReceipt(ctx);
        // 保存操作日志
        outputComponent.saveBizReceiptOperationLog(ctx);
        // 保存附件
        outputComponent.saveBizReceiptAttachment(ctx);
        // 保存单据流
        materialOutputComponent.saveReceiptTree(ctx);
    }

    /**
     * 非同时模式 获取配货信息
     * @param ctx
     */
    private void getItemInfoNoSameTime(BizContext ctx) {
        // 配置单据类型
        BizReceiptOutputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        po.setReceiptType(EnumReceiptType.STOCK_OUTPUT_MAT_REQ.getValue());
        // 获取配货信息(特性库存)【非同时模式】
        materialOrderOutApplyComponent.getItemInfoByFeatureStock(ctx);
    }


    /**
     * 先作业模式 手动点击过账
     * @param ctx
     */
    private void postTaskFirst(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Integer receiptStatus = headDTO.getReceiptStatus();
        // 校验
        outputComponent.checkPost(ctx);
        // 校验实际领料人是否存在，并保存
        materialOutputComponent.checkAndSaveActualReceiver(ctx);
        //20230723-未同步单据修改库存地点时更新数据库 20200801取消此修改
//        materialOutputComponent.updateLocation(ctx);

        // 2023-07-04 根据业务方需求，增加出库过账批次信息的到期日期限制，所过账的物料要求批次信息的到期时间应在当前日期之后（即已到期的物资不可出库）
        materialOutputComponent.checkLifetimeDate(ctx);
        //7.11 列表页增加发料人，发料人取领料出库单点击“过账”或“关闭预留”的操作用户
        materialOutputComponent.updateSendUser(ctx);
        // 保存签名
        materialOutputComponent.saveAutograph(ctx);
        // 生成移动类型
        materialOutputComponent.generateInsMoveTypeAndCheck(ctx);
        // sap过账
        materialOutputComponent.postToSap(ctx);
//        // 调用sap关闭预留信息，整单关闭、删除
//        materialOutputComponent.closeReservationListAll(ctx);
        // InStock过账
        outputComponent.postToInStock(ctx);
        // 单据状态已完成
        outputComponent.updateStatusCompleted(ctx);
        // 作业中过账时处理下架作业请求
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue())) {
            unLoadComponent.closeUnLoadReq(headDTO.getId());
        }
    }

    // 同时模式过账
    public void submitSameTime(BizContext ctx) {
        outputComponent.submitReceiptSameTime(ctx);
        outputComponent.saveBizReceiptOperationLog(ctx);
        outputComponent.saveBizReceiptAttachment(ctx);
        otherOutputComponent.generateInsMoveTypeAndCheckSameTime(ctx);
        // sap过账
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        headDTO.getItemDTOList().forEach(o->o.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue()));
        materialOutputComponent.postToSap(ctx);
        outputComponent.postToInStock(ctx);
        outputComponent.updateStatusCompleted(ctx);
    }

    /**
     * 关闭预留
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void close(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Integer receiptStatus = headDTO.getReceiptStatus();

        Object logType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        if (UtilObject.isNull(logType)) {
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE);
        }

        // 保存操作日志
        outputComponent.saveBizReceiptOperationLog(ctx);
        // 调用sap关闭预留信息，整单关闭、删除
        materialOutputComponent.closeReservationListAll(ctx);
        // 单据状态已完成
        outputComponent.updateStatusCompleted(ctx);
        //7.11 列表页增加发料人，发料人取领料出库单点击“过账”或“关闭预留”的操作用户
        materialOutputComponent.updateSendUser(ctx);
        // 已提交状态关联预留时处理下架作业请求
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue())) {
            unLoadComponent.closeUnLoadReq(headDTO.getId());
        }
    }


    /**
     * 关闭预留
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void reqPlanClose(BizContext ctx) {
        BizReceiptOutputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Integer receiptStatus = headDTO.getReceiptStatus();

        Object logType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        if (UtilObject.isNull(logType)) {
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE);
        }

        // 保存操作日志
        outputComponent.saveBizReceiptOperationLog(ctx);
        // 单据状态已完成
        outputComponent.updateStatusCompleted(ctx);
        //7.11 列表页增加发料人，发料人取领料出库单点击“过账”或“关闭预留”的操作用户
        materialOutputComponent.updateSendUser(ctx);
        // 已提交状态关联预留时处理下架作业请求
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue())) {
            unLoadComponent.closeUnLoadReq(headDTO.getId());
        }
    }

    /**
     * 删除单据
     * @param ctx
     */
    public void delete(BizContext ctx) {
        this.deleteTaskFirst(ctx);
    }


    /**
     * 先作业模式 删除单据
     *
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteTaskFirst(BizContext ctx) {
        // 校验
        materialOutputComponent.checkDelete(ctx);
        outputComponent.checkTaskStatus(ctx);

        // 删除单据
        outputComponent.deleteReceipt(ctx);
        // 删除库存占用
        outputComponent.deleteOccupyStock(ctx);
        // 删除单据流
        materialOutputComponent.deleteReceiptTree(ctx);
        // 删除附件
        outputComponent.deleteBizReceiptAttachment(ctx);
        // 删除作业请求
        outputComponent.cancelTaskRequest(ctx);
    }


    /**
     * 下架回调
     *
     * @param ctx 上下文
     */
    @Entrance(call = {"outputTaskCallbackComponent#updateQtyAndStatus", "outputComponent#isTask",
            "outputComponent#checkPost", "matReqOutputComponent#generateInsMoveTypeAndCheck", "outputComponent#postToSap",
            "outputComponent#postToInStock", "outputComponent#deleteOccupyStock", "outputComponent#updateStatusCompleted"})
    @WmsMQListener(tags = TagConst.TASK_MAT_REQ_OUTPUT_CALLBACK)
    public void callbackByTask(BizContext ctx) {

        // TODO-BO: 2022/5/23 这里注释掉，需手动过账
        // 更新数量和状态
        outputTaskCallbackComponent.updateQtyAndStatus(ctx);

//        if (outputComponent.isTask(ctx)) {
//            try {
//                this.postTaskFirst(ctx);
//            } catch (RuntimeException re) {
//                log.info("MQ推送领料出库自动过账-------------------MQ异常不处理, 保留单据未同步状态即可");
//            }
//        }

        /*
        // 先作业模式
        if (outputComponent.isTask(ctx)) {
            // 校验
            outputComponent.checkPost(ctx);
            // 生成移动类型
            matReqOutputComponent.generateInsMoveTypeAndCheck(ctx);
            // sap过账
            outputComponent.postToSap(ctx);
            // InStock过账
            outputComponent.postToInStock(ctx);
            // 删除占用库存
            outputComponent.deleteOccupyStock(ctx);
            // 单据状态已完成
            outputComponent.updateStatusCompleted(ctx);
        }

         */
    }

    public void saveItemRemark(BizContext ctx) {
        // 保存单据行项目备注校验
        materialOutputComponent.checkSaveItemRemark(ctx);
        // 保存行项目备注
        materialOutputComponent.saveItemRemark(ctx);
    }

    /**
     * 定时任务关闭预留
     */
    public void closeReserveReceiptByScheduleJob() {
        log.info("执行定时任务-------->30天未处理领料出库自动关闭预留……………………………………");
        MaterialOutputComponent materialOutputComponentNew = UtilSpring.getBean("materialOutputComponent");
        // 查询符合条件的领料出库单
        List<BizReceiptOutputHeadDTO> headList = materialOutputComponentNew.getShouldClosedOutputReceipt();

        if (UtilCollection.isEmpty(headList)) {
            return;
        }
        UserService userService= UtilSpring.getBean("userService");
        CurrentUser currentUser=userService.getCurrentUserByUserCode(Const.ADMIN_USER_CODE);
        // 逐个执行关闭
        for (BizReceiptOutputHeadDTO headDTO : headList) {
            try {
                BizContext ctx = new BizContext();
                ctx.setCurrentUser(currentUser);
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_REVOKE_AUTO);
                MaterialOutputService materialOutputServiceNew=UtilSpring.getBean("materialOutputService");
                materialOutputServiceNew.close(ctx);
            } catch (Exception e) {
                ExceptionLogHandler exceptionLogHandlerNew=UtilSpring.getBean("exceptionLogHandler");
                // save log
                log.error("定时任务【关闭超期预留单】,单据{},执行失败,请检查!!!", headDTO.getReceiptCode());
                log.error("异常信息: ", e);
                exceptionLogHandlerNew.save(e, e.getMessage());
            }
        }
    }
}
