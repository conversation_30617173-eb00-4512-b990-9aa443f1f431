package com.inossem.wms.bizdomain.inspect.service.biz;

import com.inossem.wms.bizdomain.inspect.service.component.DistributeInspectComponent;
import com.inossem.wms.bizdomain.inspect.service.component.InspectCommonComponent;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 分配质检 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-25
 */
@Service
public class DistributeInspectService {

    @Autowired
    protected InspectCommonComponent inspectCommonComponent;

    @Autowired
    protected DistributeInspectComponent distributeInspectComponent;

    /**
     * 获取参检人列表
     *
     * @param ctx-po 查询用户列表入参
     * @return 用户列表
     */
    public void getUserList(BizContext ctx) {

        // 获取参检人列表
        inspectCommonComponent.setUserList(ctx);

    }

    /**
     * 获取参检人反显name
     *
     * @param ctx-po 查询用户列表入参
     * @return 用户name描述
     */
    public void getUserNameList(BizContext ctx) {

        // 获取参检人name
        inspectCommonComponent.setUserNameList(ctx);

    }

    /**
     * 查询分配质检单列表-分页
     *
     * @param ctx-po 分配质检单分页查询入参
     * @return 分配质检单列表
     */
    public void getPage(BizContext ctx) {

        // 查询分配质检单列表-分页
        distributeInspectComponent.setPage(ctx);

    }

    /**
     * 查询分配质检单详情
     *
     * @param ctx-id 分配质检单抬头表主键
     * @return 分配质检单详情
     */
    public void getInfo(BizContext ctx) {

        // 分配质检单详情
        distributeInspectComponent.getInfo(ctx);

        // 设置详情页单据流
        inspectCommonComponent.setInfoExtendRelation(ctx);

        // 开启附件
        inspectCommonComponent.setExtendAttachment(ctx);

        // 开启操作日志
        inspectCommonComponent.setExtendOperationLog(ctx);

    }

    /**
     * 分配质检-保存
     *
     * @param ctx-po 保存分配质检表单参数
     * @return ctx-receiptCode 分配质检单单号
     */
    @WmsMQListener(tags = TagConst.GEN_DISTRIBUTE_INSPECT_STOCK)
    @Transactional(rollbackFor = Exception.class)
    public void save(BizContext ctx) {

        // 保存分配质检
        inspectCommonComponent.saveInspect(ctx);

        // 保存附件
        inspectCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        inspectCommonComponent.saveBizReceiptOperationLog(ctx);

    }

    /**
     * 分配质检-提交
     *
     * @param ctx-po 提交分配质检表单参数
     * @return ctx-receiptCode 分配质检单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交分配质检单前进行校验
        distributeInspectComponent.checkSubmit(ctx);

        // 提交分配质检单
        distributeInspectComponent.submitLoseRegister(ctx);

        // 保存附件
        inspectCommonComponent.saveBizReceiptAttachment(ctx);

        // 保存操作日志
        inspectCommonComponent.saveBizReceiptOperationLog(ctx);

        // 单据状态判断
        inspectCommonComponent.updateStatusByItem(ctx);

        // 生成质检会签单
        distributeInspectComponent.genSignInspect(ctx);

    }

    /**
     * 分配质检-阅知
     *
     * @param ctx-po 提交分配质检表单参数
     * @return ctx-receiptCode 分配质检单单号
     */
    @Transactional(rollbackFor = Exception.class)
    public void review(BizContext ctx) {
        distributeInspectComponent.completeUmsTask(ctx);
    }

    public void revoke(BizContext ctx) {
        distributeInspectComponent.revoke(ctx);
    }

}
