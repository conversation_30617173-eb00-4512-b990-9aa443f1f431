package com.inossem.wms.bizdomain.returns.service.biz;

import com.inossem.wms.bizdomain.returns.service.component.ReturnComponent;
import com.inossem.wms.bizdomain.returns.service.component.TransferReturnComponent;
import com.inossem.wms.bizdomain.returns.service.component.callback.ReturnTaskCallbackComponent;
import com.inossem.wms.common.annotation.Entrance;
import com.inossem.wms.common.annotation.WmsMQListener;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.model.common.base.BizContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 退转库入库 业务实现层
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-06
 */
@Service
public class TransferReturnService {

    @Autowired
    protected ReturnComponent returnComponent;

    @Autowired
    protected TransferReturnComponent transferReturnComponent;

    @Autowired
    protected ReturnTaskCallbackComponent returnTaskCallbackComponent;

    /**
     * 查询退转库入库单列表-分页
     *
     * @param ctx-po 分页查询入参
     * @return 单据列表
     */
    public void getPage(BizContext ctx) {

        // 获取退库单列表(分页)
        returnComponent.getPage(ctx);

    }

    /**
     * 获取退转库入库单详情
     *
     * @param ctx 上下文
     */
    public void getInfo(BizContext ctx) {

        // 获取退转库入库单详情
        transferReturnComponent.getInfo(ctx);

        // 设置单据流
        returnComponent.setExtendRelation(ctx);

        // 开启附件
        returnComponent.setExtendAttachment(ctx);

        // 开启日志
        returnComponent.setExtendOperationLog(ctx);

    }

    /**
     * 保存单据
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    @WmsMQListener(tags = TagConst.GEN_TRANSFER_RETURN_STOCK)
    public void save(BizContext ctx) {

        // 保存校验
        returnComponent.checkSave(ctx);

        // 保存单据
        returnComponent.saveReceipt(ctx);

        // 保存日志
        returnComponent.saveBizReceiptOperationLog(ctx);

        // 保存单据附件
        returnComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        returnComponent.saveReceiptTree(ctx);

    }

    /**
     * 提交单据
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void submit(BizContext ctx) {

        // 提交单据
        transferReturnComponent.submitReceipt(ctx);

        // 保存日志
        returnComponent.saveBizReceiptOperationLog(ctx);

        // 保存单据附件
        returnComponent.saveBizReceiptAttachment(ctx);

        // 保存单据流
        returnComponent.saveReceiptTree(ctx);

        // 获取过账移动类型并校验(非同时模式)
        transferReturnComponent.generatePostInsMoveTypeAndCheck(ctx);

        // sap过账
        returnComponent.postToSap(ctx);

        // InStock过账
        returnComponent.postToInStock(ctx);

        // 普通标签生成上架请求
        returnComponent.generateLoadReq(ctx);

    }

    /**
     * 手动点击过账
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void post(BizContext ctx) {

        // 再次过账，单据状态校验
        returnComponent.checkPost(ctx);

        // 获取过账移动类型并校验(非同时模式)
        transferReturnComponent.generatePostInsMoveTypeAndCheck(ctx);

        // sap过账
        returnComponent.postToSap(ctx);

        // InStock过账
        returnComponent.postToInStock(ctx);

        // 普通标签生成上架请求
        returnComponent.generateLoadReq(ctx);

    }

    /**
     * 冲销
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void writeOff(BizContext ctx) {

        // 冲销校验
        returnComponent.checkWriteOff(ctx);

        // 获取冲销移动类型并校验（非同时模式）
        transferReturnComponent.generateWriteOffInsMoveTypeAndCheck(ctx);

        // sap冲销
        returnComponent.writeOffToSap(ctx);

        // InStock冲销
        returnComponent.writeOffToInStock(ctx);

        // 推送冲销修改请求
        returnComponent.updateTaskRequest(ctx);

    }

    /**
     * 删除单据
     *
     * @param ctx 上下文
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(BizContext ctx) {

        // 校验删除
        returnComponent.checkDelete(ctx);

        // 删除单据
        returnComponent.deleteReceipt(ctx);

        // 删除批次信息
        returnComponent.deleteBatchInfo(ctx);

        // 逻辑逻辑单据流
        returnComponent.deleteReceiptTree(ctx);

        // 删除附件
        returnComponent.deleteBizReceiptAttachment(ctx);

        // 删除作业请求
        returnComponent.cancelTaskRequest(ctx);

    }

    /**
     * 上架回调
     *
     * @param ctx 上下文
     */
    @WmsMQListener(tags = TagConst.TASK_TRANSFER_RETURN_CALLBACK)
    public void callbackByTask(BizContext ctx) {

        // 更新数量和状态
        returnTaskCallbackComponent.updateQtyAndStatus(ctx);

        // 更新批次入库时间
        returnComponent.updateInputDate(ctx);

    }

    /**
     *  退转库入库-物料标签打印
     * @param ctx
     */
    public void boxApplyLabelPrint(BizContext ctx) {
        // 打印物料标签校验
        returnTaskCallbackComponent.checkPrint(ctx);
        // 填充打印数据
        returnTaskCallbackComponent.fillPrintData(ctx);
    }

}
