package com.inossem.wms.bizdomain.report.controller;

import com.inossem.wms.bizdomain.report.service.biz.ReportService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizdomain.report.po.*;
import com.inossem.wms.common.model.bizdomain.report.vo.*;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.common.enums.DeliveryTypeMapVO;
import com.inossem.wms.common.model.common.enums.MoveTypeMapVO;
import com.inossem.wms.common.model.common.enums.ShippingTypeMapVO;
import com.inossem.wms.common.model.common.enums.SpecStockMapVO;
import com.inossem.wms.common.model.common.enums.StockStatusMapVO;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.po.StockInsDocBinPagePO;
import com.inossem.wms.common.model.stock.vo.StockInsDocBinVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 * 报表 前端控制器
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-10
 */
@RestController
@Api(tags = "仓储功能-报表管理")
public class ReportController {

    @Autowired
    protected ReportService reportService;
    private InputLedgerPO po;
    private BizContext ctx;


    /**
     * 特殊库存标识
     * @param ctx ctx
     * @return 特殊库存标识 vo
     */
    @ApiOperation(value = "报表-特殊库存下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-batch/sepc-stock/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<SpecStockMapVO>> getSepcStockList(BizContext ctx) {
        reportService.getSpecStockList(ctx);
        MultiResultVO<SpecStockMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 库存类型
     * @param ctx ctx
     * @return 库存类型vo
     */
    @ApiOperation(value = "报表-库存类型下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-batch/stock-status/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockStatusMapVO>> getStockStatusList(BizContext ctx) {
        reportService.getStockStatusList(ctx);
        MultiResultVO<StockStatusMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 批次库存查询 详情
     * @param po 入参
     * @param ctx ctx
     * @return 返回批次库存vo
     */
    @ApiOperation(value = "报表-批次库存详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-batch/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBatchVO>> selectStockBatchDetail(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        reportService.selectStockBatchDetail(ctx);
        PageObjectVO<StockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 批次库存导出Excel
     * @param po 入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-批次库存导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-batch/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockBatchDetail(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportStockBatchDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 批次库存查询 物料组分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回批次库存vo
     */
    @ApiOperation(value = "报表-批次库存-物料组分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-batch/mat-group-code/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBatchVO>> selectStockBatchGroupByMatGroup(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        reportService.selectStockBatchGroupByMatGroup(ctx);
        PageObjectVO<StockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 批次库存查询 库存地点分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回批次库存vo
     */
    @ApiOperation(value = "报表-批次库存-库存地点分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-batch/location/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBatchVO>> selectStockBatchGroupByLocation(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        reportService.selectStockBatchGroupByLocation(ctx);
        PageObjectVO<StockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 批次库存查询 仓库号分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回批次库存vo
     */
    @ApiOperation(value = "报表-批次库存-仓库分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-batch/wh/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBatchVO>> selectStockBatchGroupByWh(@RequestBody StockBatchSearchPO po, BizContext ctx) {
        reportService.selectStockBatchGroupByWh(ctx);
        PageObjectVO<StockBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 仓位库存查询 详情
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-bin/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBinVO>> selectStockBinDetail(@RequestBody StockBinSearchPO po, BizContext ctx) {
        reportService.selectStockBinDetail(ctx);
        PageObjectVO<StockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 仓位库存导出Excel
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-bin/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockBinExcel(@RequestBody StockBinSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportStockBinDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 仓位库存查询 仓库分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存仓库-分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-bin/wh/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBinVO>> selectStockBinGroupByWh(@RequestBody StockBinSearchPO po, BizContext ctx) {
        reportService.selectStockBinGroupByWh(ctx);
        PageObjectVO<StockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 仓位库存查询 存储类型分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存存储类型分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-bin/type/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBinVO>> selectStockBinGroupByType(@RequestBody StockBinSearchPO po, BizContext ctx) {
        reportService.selectStockBinGroupByType(ctx);
        PageObjectVO<StockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 仓位库存查询 详情 存储区分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回仓位库存vo
     */
    @ApiOperation(value = "报表-仓位库存-存储区分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-bin/section/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBinVO>> selectStockBinGroupBySection(@RequestBody StockBinSearchPO po, BizContext ctx) {
        reportService.selectStockBinGroupBySection(ctx);
        PageObjectVO<StockBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 库存积压分析
     * @param po 入参
     * @param ctx ctx
     * @return 返回库存积压vo
     */
    @ApiOperation(value = "报表-库存积压", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-age-analyse/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockAgeAnalyseVO>> selectStockAnalyse(@RequestBody StockAgeAnalysePO po, BizContext ctx) {
        reportService.selectStockAnalyse(ctx);
        MultiResultVO<StockAgeAnalyseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 库存积压分析 仓库分组 查询一年以上
     * @param po 入参
     * @param ctx ctx
     * @return 返回库存积压vo
     */
    @ApiOperation(value = "报表-库存积压-仓库号分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-age-analyse/wh-stock-analyse/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockAgeAnalyseVO>> selectStockAnalyseGroupByWh(@RequestBody StockAgeAnalysePO po, BizContext ctx) {
        reportService.selectStockAnalyseGroupByWh(ctx);
        MultiResultVO<StockAgeAnalyseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }




    /**
     * 业务凭证查询
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-业务凭证", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-ins-doc-batch/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockInsDocBatchVO>> selectStockInsDocBatch(@RequestBody StockInsDocBatchSearchPO po, BizContext ctx) {
        reportService.selectStockInsDocBatch(ctx);
        PageObjectVO<StockInsDocBatchVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 业务凭证查询导出Excel
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-业务凭证导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-ins-doc-batch/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockInsDocBatch(@RequestBody StockInsDocBatchSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportStockInsDocBatch(ctx);
        return BaseResult.success();
    }

    /**
     * 业务类型
     * @param ctx ctx
     * @return 业务类型 vo
     */
    @ApiOperation(value = "报表-业务类型下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-batch/receipt-type-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<SpecStockMapVO>> getReceiptTypeList(BizContext ctx) {
        reportService.getReceiptTypeList(ctx);
        MultiResultVO<SpecStockMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 作业查询
     * @param po 入参
     * @param ctx ctx
     * @return 作业vo
     */
    @ApiOperation(value = "报表-作业查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/task/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<TaskVO>> selectTask(@RequestBody TaskSearchPO po, BizContext ctx) {
        reportService.selectTask(ctx);
        PageObjectVO<TaskVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 作业查询导出Excel
     * @param po 入参
     * @param ctx ctx
     * @return 作业vo
     */
    @ApiOperation(value = "报表-作业查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/task/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportTask(@RequestBody TaskSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportTask(ctx);
        return BaseResult.success();
    }


    /**
     * 库存对账 全库
     * @param ctx ctx
     * @return 标准返回对象
     */
    @ApiOperation(value = "报表-库存对账提交", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-diff", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> stockDiff(BizContext ctx) {
        reportService.stockDiff();
        return BaseResult.success();
    }

    /**
     * 库存对账查询
     * @param po 入参
     * @param ctx ctx
     * @return 库存对账返回参数
     */
    @ApiOperation(value = "报表-库存对账查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-diff/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockDiffVO>> selectStockDiff(@RequestBody StockDiffSearchPO po, BizContext ctx) {
        reportService.selectStockDiff(ctx);
        PageObjectVO<StockDiffVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 库存对账查询导出Excel
     * @param po 入参
     * @param ctx ctx
     * @return 库存对账返回参数
     */
    @ApiOperation(value = "报表-库存对账查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-diff/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockDiff(@RequestBody StockDiffSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportStockDiff(ctx);
        return BaseResult.success();
    }

    /**
     * 库存积压分析 库存地点分组
     * @param po 入参
     * @param ctx ctx
     * @return 返回库存积压vo
     */
    @ApiOperation(value = "报表-库存积压-库存地点分组", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-age-analyse", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockAgeAnalyseLocationVO>> selectStockAnalyseGroupByLocation(@RequestBody StockAgeAnalysePO po, BizContext ctx) {
        reportService.selectStockAnalyseGroupByLocation(ctx);
        MultiResultVO<StockAgeAnalyseLocationVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * erp库存金额分析
     * @param ctx ctx
     * @return 库存金额返回参数
     */
    @ApiOperation(value = "报表-erp库存金额分析", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/erp-stock-money-analyse", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockMoneyAnalyseVO>> erpStockMoneyAnalyse(BizContext ctx) {
        reportService.erpStockMoneyAnalyse(ctx);
        MultiResultVO<StockMoneyAnalyseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 出入库统计 7天内
     * @param ctx ctx
     * @return 出入库统计返回参数
     */
    @ApiOperation(value = "报表-出入库统计", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/in-out-analyse", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<InAndOutAnalyseVO>> selectInAndOut(BizContext ctx) {
        reportService.selectInAndOut(ctx);
        MultiResultVO<InAndOutAnalyseVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * erp库存周转率 最近半年
     * @param ctx ctx
     * @return 出入库统计返回参数
     */
    @ApiOperation(value = "报表-库存周转率", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-turnover", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockTurnoverVO>> selectStockTurnover(@RequestBody StockTurnoverPO po, BizContext ctx) {
        reportService.selectStockTurnover(ctx);
        MultiResultVO<StockTurnoverVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 月末 计算库存周转率
     * @param ctx ctx
     * @return 基本返回对象
     */
    @ApiOperation(value = "报表-计算库存周转率", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/collect-stock-turnover", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult collectStockTurnover(BizContext ctx) {
        reportService.collectStockTurnover();

        return BaseResult.success();
    }

    /**
     * pda仓位库存查询
     * @param ctx ctx
     * @return 仓位库存
     */
    @ApiOperation(value = "报表-pda仓位库存查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/pda-stock-bin/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<StockBinDTO>> getPdaStockBin(@RequestBody StockBinPdaSearchPO po, BizContext ctx) {
        reportService.getPdaStockBin(ctx);
        MultiResultVO<StockBinDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 重量库存查询
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-重量库存查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-bin-weight/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockBinWeightVo>> selectStockBinWeightDetail(@RequestBody StockBinWeightSearchPO po, BizContext ctx) {
        reportService.selectStockBinWeightDetail(ctx);
        PageObjectVO<StockBinWeightVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 重量库存查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-重量库存查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-bin-weight/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockBinWeightDetail(@RequestBody StockBinWeightSearchPO po, BizContext ctx) {
        reportService.exportStockBinWeightDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 电子秤报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-电子秤报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/electronic-scale-record/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ElectronicScaleVO>> selectElectronicScaleRecord(@RequestBody ElectronicScalePO po, BizContext ctx) {
        reportService.selectElectronicScaleRecord(ctx);
        PageObjectVO<ElectronicScaleVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 入库台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-入库台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/input-ledger/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<InputLedgerVo>> selecInputLedgerRecord(@RequestBody InputLedgerPO po, BizContext ctx) {
        reportService.selectInputLedgerRecord(ctx);
        PageObjectVO<InputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 入库台账报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-入库台账报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/input-ledger/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportInputLedgerRecord(@RequestBody InputLedgerPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportInputLedgerRecord(ctx);
        return BaseResult.success();
    }

    /**
     * 移动类型
     * @param ctx ctx
     * @return 移动类型 vo
     */
    @ApiOperation(value = "报表-移动类型下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/input-ledger/move-type/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<MoveTypeMapVO>> getMoveTypeList(BizContext ctx) {
        reportService.getMoveTypeList(ctx);
        MultiResultVO<MoveTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 直抵现场台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-直抵现场台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/direct-scene/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DirectSceneVo>> selecInputLedgerRecord(@RequestBody DirectScenePO po, BizContext ctx) {
        reportService.selectDirectSceneRecord(ctx);
        PageObjectVO<DirectSceneVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 直抵现场台账查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-直抵现场台账报表查询导出Excel selectDeliveryTrackRecord", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/direct-scene/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportInputLedgerRecord(@RequestBody DirectScenePO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportDirectSceneRecord(ctx);
        return BaseResult.success();
    }

    /**
     * 入库物资跟踪报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-入库物资跟踪报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/delivery-track/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DeliveryTrackVo>> selectDeliveryTrackRecord(@RequestBody DeliveryTrackPO po, BizContext ctx) {
        reportService.selectDeliveryTrackRecord(ctx);
        PageObjectVO<DeliveryTrackVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 入库物资跟踪报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-入库物资跟踪报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/delivery-track/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportDeliveryTrackRecord(@RequestBody DeliveryTrackPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportDeliveryTrackRecord(ctx);
        return BaseResult.success();
    }


    /**
     * 出库台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-出库台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/output-ledger/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<OutputLedgerVo>> selecOutputLedgerRecord(@RequestBody OutputLedgerPO po, BizContext ctx) {
        reportService.selectOutputLedgerRecord(ctx);
        PageObjectVO<OutputLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 出库台账报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-出库台账报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/output-ledger/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportOutputLedgerRecord(@RequestBody OutputLedgerPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportOutputLedgerRecord(ctx);
        return BaseResult.success();
    }

    /**
     * 退旧换新台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-退旧换新台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/return-new-ledger/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ReturnNewLedgerVo>> selectReturnNewLedgerRecord(@RequestBody ReturnNewLedgerPO po, BizContext ctx) {
        reportService.selectReturnNewLedgerRecord(ctx);
        PageObjectVO<ReturnNewLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 退旧换新台账报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-退旧换新台账报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/return-new-ledger/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportReturnNewLedgerRecord(@RequestBody ReturnNewLedgerPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportReturnNewLedgerRecord(ctx);
        return BaseResult.success();
    }



    /**
     * 工器具台账报表查询
     *
     * @param ctx ctx
     * @return 重量库存
     */
    @ApiOperation(value = "报表-退工器具台账报表查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/tool-ledger/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ToolLedgerVo>> selectToolLedgerRecord(@RequestBody ToolLedgerPO po, BizContext ctx) {
        reportService.selectToolLedgerRecord(ctx);
        PageObjectVO<ToolLedgerVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 工器具台账报表查询导出Excel
     *
     * @param po 入参
     * @param ctx ctx
     * @return 业务凭证vo
     */
    @ApiOperation(value = "报表-工器具台账报表查询导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/tool-ledger/result/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportToolLedgerRecord(@RequestBody ToolLedgerPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportToolLedgerRecord(ctx);
        return BaseResult.success();
    }
    /**
     * 寿期台账查询 详情
     * @param po 入参
     * @param ctx ctx
     * @return 返回寿期台账vo
     */
    @ApiOperation(value = "报表-寿期台账详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/lifetime/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<LifetimeVO>> selectLifetimeDetail(@RequestBody LifetimeSearchPO po, BizContext ctx) {
        reportService.selectLifetimeDetail(ctx);
        PageObjectVO<LifetimeVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 寿期台账导出Excel
     * @param po 入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-寿期台账导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/lifetime/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportLifetimeDetail(@RequestBody LifetimeSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportLifetimeDetail(ctx);
        return BaseResult.success();
    }
    /**
     * 维保台账查询 详情
     * @param po 入参
     * @param ctx ctx
     * @return 返回维保台账vo
     */
    @ApiOperation(value = "报表-维保台账详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/maintain/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<MaintainVO>> selectMaintainDetail(@RequestBody MaintainSearchPO po, BizContext ctx) {
        reportService.selectMaintainDetail(ctx);
        PageObjectVO<MaintainVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 维保台账导出Excel
     * @param po 入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-维保台账导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/maintain/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportMaintainDetail(@RequestBody MaintainSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportMaintainDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 退旧台账查询 详情
     * @param po 入参
     * @param ctx ctx
     * @return 返回退旧台账vo
     */
    @ApiOperation(value = "报表-退旧台账详情", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/return-old/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<MaintainVO>> selectReturnOldDetail(@RequestBody ReturnOldSearchPO po, BizContext ctx) {
        reportService.selectReturnOldDetail(ctx);
        PageObjectVO<MaintainVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 退旧台账导出Excel
     * @param po 入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-退旧台账导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/return-old/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportReturnOldDetail(@RequestBody ReturnOldSearchPO po, BizContext ctx) {
        po.setPageSize(5000000);
        reportService.exportReturnOldDetail(ctx);
        return BaseResult.success();
    }

    /**
     * 库存对比结果查询
     *
     * @param po
     * @param ctx
     * @return
     */
    @ApiOperation(value = "报表-库存对比结果", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-compare/detail/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockCompareVO>> stockCompare(@RequestBody StockCompareSearchPO po, BizContext ctx) {
        reportService.stockCompare(ctx);
        PageObjectVO<StockCompareVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 库存对比结果导出Excel
     *
     * @param po  入参
     * @param ctx ctx
     */
    @ApiOperation(value = "报表-库存对比结果导出Excel", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-compare/detail/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockCompareDetail(@RequestBody StockCompareSearchPO po, BizContext ctx) {
        reportService.exportStockCompareDetail(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "报表-项目数据分类统计", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/project-data/statistics/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<ProjectDataStatisticsVO> selectProjectDataStatistics(@RequestBody ProjectDataStatisticsSearchPO po, BizContext ctx) {
        reportService.selectProjectDataStatistics(ctx);
        ProjectDataStatisticsVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-物资滞留库存统计", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/mat-retention/statistics/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectWithTotalVO<MatRetentionStockDetailVO, MatRetentionStockVO>> selectMatRetentionStock(@RequestBody MatRetentionStockSearchPO po, BizContext ctx) {
        reportService.selectMatRetentionStock(ctx);
        PageObjectWithTotalVO<MatRetentionStockDetailVO, MatRetentionStockVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-物资滞留库存统计导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/mat-retention/statistics/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportMatRetentionStock(@RequestBody MatRetentionStockSearchPO po, BizContext ctx) {
        reportService.exportMatRetentionStock(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "报表-库存有效期预警", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-validity/warning/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockValidityPeriodWarningVO>> selectStockValidityPeriod(@RequestBody StockValidityPeriodSearchPO po, BizContext ctx) {
        reportService.selectStockValidityPeriod(ctx);
        PageObjectVO<StockValidityPeriodWarningVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-库存有效期预警导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stock-validity/warning/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportStockValidityPeriod(@RequestBody StockValidityPeriodSearchPO po, BizContext ctx) {
        reportService.exportStockValidityPeriod(ctx);
        return BaseResult.success();
    }


    @ApiOperation(value = "报表-物项状态跟踪", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/mat-state/track/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<MatStateTrackVO>> selectMatStateTrack(@RequestBody StockValidityPeriodSearchPO po, BizContext ctx) {
        reportService.selectMatStateTrack(ctx);
        PageObjectVO<MatStateTrackVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-物项状态跟踪导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/mat-state/track/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportMatStateTrack(@RequestBody StockValidityPeriodSearchPO po, BizContext ctx) {
        reportService.exportMatStateTrack(ctx);
        return BaseResult.success();
    }


    @ApiOperation(value = "报表-待预留无库存", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/reserved-stock/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ReservedEmptyStockVO>> selectReservedStock(@RequestBody StockValidityPeriodSearchPO po, BizContext ctx) {
        reportService.selectReservedStock(ctx);
        PageObjectVO<ReservedEmptyStockVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-待预留无库存导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/reserved-stock/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportReservedStock(@RequestBody StockValidityPeriodSearchPO po, BizContext ctx) {
        reportService.exportReservedStock(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "报表-专用工器具统计", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/tool-statistics/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<ToolBorrowStatisticsVO>> selectToolBorrowReport(@RequestBody ToolBorrowStatisticsSearchPO po, BizContext ctx) {
        reportService.selectToolBorrowReport(ctx);
        PageObjectVO<ToolBorrowStatisticsVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-专用工器具统计导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/tool-statistics/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportToolBorrowReport(@RequestBody ToolBorrowStatisticsSearchPO po, BizContext ctx) {
        reportService.exportToolBorrowReport(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "报表-库存凭证查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/stockInsDocBin/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<StockInsDocBinVO>> getStockInsDocBinPage(@RequestBody StockInsDocBinPagePO po, BizContext ctx) {
        reportService.getStockInsDocBinPage(ctx);
        PageObjectVO<StockInsDocBinVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-送货类型下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/delivery-waybill-ledger/delivery-type/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<DeliveryTypeMapVO>> getDeliveryTypeList(BizContext ctx) {
        reportService.getDeliveryTypeList(ctx);
        MultiResultVO<DeliveryTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-运输方式下拉", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/delivery-waybill-ledger/shipping-type/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<ShippingTypeMapVO>> getShippingTypeList(BizContext ctx) {
        reportService.getShippingTypeList(ctx);
        MultiResultVO<ShippingTypeMapVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-运单台账查询", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/delivery-waybill-ledger/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DeliveryWaybillLedgerVO>> getDeliveryWaybillLedgerPage(@RequestBody DeliveryWaybillLedgerPO po, BizContext ctx) {
        reportService.getDeliveryWaybillLedgerPage(ctx);
        PageObjectVO<DeliveryWaybillLedgerVO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "报表-运单台账导出", tags = {"仓储管理-报表"})
    @PostMapping(path = "/report/delivery-waybill-ledger/excel", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportDeliveryWaybillLedger(@RequestBody DeliveryWaybillLedgerPO po, BizContext ctx) {
        reportService.exportDeliveryWaybillLedger(ctx);
        return BaseResult.success();
    }

}
