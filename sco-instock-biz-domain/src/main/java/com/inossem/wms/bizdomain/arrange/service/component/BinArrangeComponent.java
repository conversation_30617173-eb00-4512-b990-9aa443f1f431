package com.inossem.wms.bizdomain.arrange.service.component;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.WhStorageBinService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelDataDataWrap;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelReceiptRelDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.arrange.service.datawrap.BizReceiptArrangeHeadDataWrap;
import com.inossem.wms.bizdomain.arrange.service.datawrap.BizReceiptArrangeItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.constant.task.TaskConst;
import com.inossem.wms.common.enums.EnumDbDefaultValueInteger;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.arrange.dto.BizReceiptArrangeHeadDTO;
import com.inossem.wms.common.model.bizdomain.arrange.dto.BizReceiptArrangeItemDTO;
import com.inossem.wms.common.model.bizdomain.arrange.entity.BizReceiptArrangeHead;
import com.inossem.wms.common.model.bizdomain.arrange.entity.BizReceiptArrangeItem;
import com.inossem.wms.common.model.bizdomain.arrange.po.BizReceiptArrangeQueryPO;
import com.inossem.wms.common.model.bizdomain.arrange.po.BizReceiptArrangeSavePo;
import com.inossem.wms.common.model.bizdomain.arrange.po.BizReceiptArrangeSearchPo;
import com.inossem.wms.common.model.bizdomain.arrange.vo.BizReceiptArrangeItemExportVO;
import com.inossem.wms.common.model.bizdomain.arrange.vo.BizReceiptArrangeItemImportVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.masterdata.storagebin.dto.DicWhStorageBinDTO;
import com.inossem.wms.common.model.masterdata.storagebin.entity.DicWhStorageBin;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.org.wh.entity.DicWh;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockBin;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.model.stock.po.StockBinPO;
import com.inossem.wms.common.model.stock.po.StockInsDocBinPo;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 仓位整理组件
 *
 * <AUTHOR>
 */

@Component
@Slf4j
public class BinArrangeComponent {

    @Autowired
    private BizReceiptArrangeHeadDataWrap bizReceiptArrangeHeadDataWrap;

    @Autowired
    private BizReceiptArrangeItemDataWrap bizReceiptArrangeItemDataWrap;

    @Autowired
    private DataFillService dataFillService;

    @Autowired
    private BizReceiptArrangeHeadDataWrap arrangeHeadDataWrap;

    @Autowired
    private BatchImgService bizBatchImgService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private BatchInfoService batchInfoService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private LabelReceiptRelService labelReceiptRelService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private MaterialService materialService;

    @Autowired
    private WhStorageBinService whStorageBinService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private BizLabelReceiptRelDataWrap bizLabelReceiptRelDataWrap;
    @Autowired
    private BizLabelDataDataWrap bizLabelDataDataWrap;
    @Autowired
    private DicMaterialDataWrap dicMaterialDataWrap;
    @Autowired
    private BizReceiptAssembleDataWrap bizReceiptAssembleDataWrap;
    @Autowired
    protected I18nTextCommonService i18nTextCommonService;
    /**
     * 获取仓位整理列表页-分页
     *
     * @param ctx 系统上下文
     */
    public void getBinArrangePageList(BizContext ctx) {
        log.info("仓位整理列表查询-分页 ctx：{}", JSONObject.toJSONString(ctx, SerializerFeature.IgnoreNonFieldGetter));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptArrangeSearchPo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
//        Date createTimeStart = null;
//        Date createTimeEnd = null;
//        if (Objects.nonNull(po.getStartTime()) && Objects.nonNull(po.getEndTime())) {
//            createTimeStart = UtilLocalDateTime.getStartTime(po.getStartTime());
//            createTimeEnd = UtilLocalDateTime.getEndTime(po.getEndTime());
//        }
//        // 查询条件设置
//        WmsQueryWrapper<BizReceiptArrangeHead> wrapper = new WmsQueryWrapper<>();
//        // 拼装参数
//        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptArrangeHead::getReceiptCode, po.getReceiptCode())
//                .between((Objects.nonNull(createTimeStart)), BizReceiptArrangeHead::getCreateTime, BizReceiptArrangeHead.class, createTimeStart, createTimeEnd)
//                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptArrangeHead::getReceiptStatus, BizReceiptArrangeHead.class, po.getReceiptStatusList())
//                .orderByDesc(BizReceiptArrangeHead::getCreateTime);
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        po.setLocationIdList(locationIdList);
//        // 分页处理
        IPage<BizReceiptArrangeHead> page = new Page<>(po.getPageIndex(), po.getPageSize());
        arrangeHeadDataWrap.getArrangeHeadList(page, po);
        
        List<BizReceiptArrangeHead> arrangeHeadList = page.getRecords();
        List<BizReceiptArrangeHeadDTO> arrangeHeadDtoList = UtilCollection.toList(arrangeHeadList, BizReceiptArrangeHeadDTO.class);
        log.info("获取仓位整理列表 arrangeHeadDtoList：{}", JSONObject.toJSONString(arrangeHeadDtoList));
        // 数据填充
        dataFillService.fillAttr(arrangeHeadDtoList);
        // 回填数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(arrangeHeadDtoList, page.getTotal()));
    }

    /**
     * 根据仓位整理headId获取仓位整理详情
     *
     * @param ctx 系统上下文
     */
    public void getBinArrange(BizContext ctx) {
        log.info("仓位整理查询 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        /* ***************************仓位整理抬头信息 **********************************/
        // 请求头信息
        BizReceiptArrangeHead bizReceiptArrangeHead = arrangeHeadDataWrap.getById(id);
        // 转DTO
        BizReceiptArrangeHeadDTO bizReceiptArrangeHeadDTO =
            UtilBean.newInstance(bizReceiptArrangeHead, BizReceiptArrangeHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(bizReceiptArrangeHeadDTO);
        /* ***************************操作权限信息 **********************************/
        // 按钮组权限
        ButtonVO buttonVO = this.setButton(bizReceiptArrangeHeadDTO.getReceiptStatus());
        // 扩展功能权限
        ExtendVO extendVO = this.setExtend(bizReceiptArrangeHeadDTO.getReceiptStatus());
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptArrangeHeadDTO, extendVO, buttonVO));
    }

    /**
     * 设置行项目批次图片信息
     *
     * @in ctx 入参 {@link BizResultVO ("head":"仓位整理详情")}
     * @out ctx 出参 {@link BizResultVO ("head":"仓位整理详情及行项目批次图片")}
     */
    public void setBatchImg(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        BizResultVO<BizReceiptArrangeHeadDTO> headVo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(headVo.getHead()) || UtilCollection.isEmpty(headVo.getHead().getItemList())) {
            return;
        }
        // 获取仓位整理head
        BizReceiptArrangeHeadDTO headDTO = headVo.getHead();
        Set<Long> batchIdSet =
            headDTO.getItemList().stream().map(BizReceiptArrangeItemDTO::getBatchId).collect(Collectors.toSet());
        // 获取批次图片
        Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(batchIdSet, 4);
        if (imgMap.isEmpty()) {
            return;
        }
        // 赋值批次图片
//        headDTO.getItemList().forEach(item -> {
//            if (UtilNumber.isNotEmpty(item.getBatchId()) && !imgMap.get(item.getBatchId()).isEmpty()
//                && UtilObject.isNotNull(item.getBatchInfo())) {
//                item.setBatchImgList(imgMap.get(item.getBatchId()));
//            }
//        });
        for (BizReceiptArrangeItemDTO item : headDTO.getItemList()) {
            List<BizBatchImgDTO> bizBatchImgDTOS = imgMap.get(item.getBatchId());
            if (UtilNumber.isNotEmpty(item.getBatchId()) && UtilCollection.isNotEmpty(bizBatchImgDTOS)
                    && UtilObject.isNotNull(item.getBatchInfo())) {
                item.setBatchImgList(imgMap.get(item.getBatchId()));
            }
        }

        // 设置仓位整理详情批次图片到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headVo);
    }

    /**
     * 设置批次特性
     *
     * @in ctx 入参 {@link BizResultVO ("head":"仓位整理详情")}
     * @out ctx 出参 {@link BizResultVO ("head":"仓位整理详情及批次特性")}
     */
    public void setSpecFeature(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        BizResultVO<BizReceiptArrangeHeadDTO> headVo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(headVo.getHead()) || UtilCollection.isEmpty(headVo.getHead().getItemList())) {
            return;
        }
        // // 获取验收特性
        // bizSpecFeatureValueService.getSpecList(headVo.getHead().getItemList(), BizReceiptArrangeItemDTO.class, 1);
        // // 获取物料特性
        // bizSpecFeatureValueService.getSpecList(headVo.getHead().getItemList(), BizReceiptArrangeItemDTO.class, 2);
        // 设置采购验收单详情批次特性到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headVo);
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptArrangeHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.setExtend(new ExtendVO());
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 获取扫描码的仓位库存
     * 
     * @param ctx 系统上下文
     */
    public void getBinArrangeStockBinList(BizContext ctx) {
        log.info("获取扫描码的仓位库存 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptArrangeSearchPo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po || UtilString.isNullOrEmpty(po.getLabelType()) || UtilString.isNullOrEmpty(po.getLabelSequence())
            || UtilString.isNullOrEmpty(po.getResType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 库存搜索条件
        StockBinPO stockBinPo = new StockBinPO();
        // 查询非限制库存
        stockBinPo.setStockStatusSet(new HashSet<String>() {

            {
                add(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue().toString());
            }
        });
        BizReceiptArrangeHeadDTO head = new BizReceiptArrangeHeadDTO();
        // 草稿状态
        head.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        // 行项目
        BizReceiptArrangeItemDTO arrangeItemDTO;
        List<BizReceiptArrangeItemDTO> itemList = new ArrayList<>();
        // 按钮组权限
        ButtonVO buttonVO = this.setButton(head.getReceiptStatus());
        // 扩展功能权限
        ExtendVO extendVO = this.setExtend(head.getReceiptStatus());
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(head, extendVO, buttonVO));
        // 标签类型
        switch (po.getLabelType()) {
            // 物料标签
            case Const.LABEL_SORT_MAT:
                if (UtilString.isNullOrEmpty(po.getMaterialArgs())) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
                }
                // 扫描：01【按id查询】
                if (TaskConst.TYPE_SCAN.equals(po.getResType())) {
                    switch (po.getMaterialArgs()) {
                        case TaskConst.BATCH:
                            stockBinPo.setBatchId(Long.valueOf(po.getLabelSequence()));
                            break;
                        case TaskConst.MATERIAL:
                            stockBinPo.setMatId(Long.valueOf(po.getLabelSequence()));
                            break;
                        case TaskConst.LABEL:
                            List<BizLabelDataDTO> labelDataDTOList = labelDataService.listById(new ArrayList<Long>() {

                                {
                                    add(Long.valueOf(po.getLabelSequence()));
                                }
                            });
                            if (UtilCollection.isEmpty(labelDataDTOList)) {
                                return;
                            }
                            if (labelDataDTOList.size() > 1) {
                                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                            }
                            BizLabelDataDTO labelDataDTO = labelDataDTOList.get(0);
                            stockBinPo.setFtyId(labelDataDTO.getFtyId());
                            stockBinPo.setLocationId(labelDataDTO.getLocationId());
                            stockBinPo.setWhId(labelDataDTO.getWhId());
                            stockBinPo.setMatId(labelDataDTO.getMatId());
                            stockBinPo.setTypeId(labelDataDTO.getTypeId());
                            stockBinPo.setBatchId(labelDataDTO.getBatchId());
                            stockBinPo.setBinId(labelDataDTO.getBinId());
                            break;
                    }
                }
                // 手输：02【按code或name查询】
                if (TaskConst.TYPE_MANUAL.equals(po.getResType())) {
                    switch (po.getMaterialArgs()) {
                        case TaskConst.BATCH:
                            // 按批次code查询批次
                            BizBatchInfoDTO batchInfoDTO =
                                batchInfoService.getBatchInfoDtoByCode(po.getLabelSequence());
                            if (null == batchInfoDTO) {
                                return;
                            }
                            stockBinPo.setBatchId(batchInfoDTO.getId());
                            break;
                        case TaskConst.MATERIAL:
                            // 按物料code或name查询
                            QueryWrapper<DicMaterial> queryWrapper = new QueryWrapper<>();
                            queryWrapper.lambda().like(DicMaterial::getMatCode, po.getLabelSequence()).or()
                                .like(DicMaterial::getMatName, po.getLabelSequence());
                            // 物料查询
                            List<DicMaterial> materialList = materialService.getMaterialList(queryWrapper);
                            if (UtilCollection.isEmpty(materialList)) {
                                return;
                            }
                            stockBinPo.setMatIdList(new ArrayList<>(
                                materialList.parallelStream().map(DicMaterial::getId).collect(Collectors.toSet())));
                            break;
                        case TaskConst.LABEL:
                            // 按标签code查询
                            List<BizLabelDataDTO> labelDataDTOList =
                                labelDataService.LabelDataListByCode(new ArrayList<String>() {

                                    {
                                        add(po.getLabelSequence());
                                    }
                                });
                            if (UtilCollection.isEmpty(labelDataDTOList)) {
                                return;
                            }
                            if (labelDataDTOList.size() > 1) {
                                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
                            }
                            BizLabelDataDTO labelDataDTO = labelDataDTOList.get(0);
                            stockBinPo.setFtyId(labelDataDTO.getFtyId());
                            stockBinPo.setLocationId(labelDataDTO.getLocationId());
                            stockBinPo.setWhId(labelDataDTO.getWhId());
                            stockBinPo.setMatId(labelDataDTO.getMatId());
                            stockBinPo.setTypeId(labelDataDTO.getTypeId());
                            stockBinPo.setBatchId(labelDataDTO.getBatchId());
                            stockBinPo.setBinId(labelDataDTO.getBinId());
                            break;
                    }
                }
                break;
            // 仓位标签
            case Const.LABEL_SORT_BIN:
                // 扫描：01【按id查询】
                if (TaskConst.TYPE_SCAN.equals(po.getResType())) {
                    stockBinPo.setBinId(Long.valueOf(po.getLabelSequence()));
                }
                // 手输：02【按code查询】
                if (TaskConst.TYPE_MANUAL.equals(po.getResType())) {
                    // 按仓位code查询
                    QueryWrapper<DicWhStorageBin> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().like(DicWhStorageBin::getBinCode, po.getLabelSequence());
                    // 物料查询
                    List<DicWhStorageBin> storageBinList = whStorageBinService.getWhStorageBinList(queryWrapper);
                    if (UtilCollection.isEmpty(storageBinList)) {
                        return;
                    }
                    stockBinPo.setBinIdList(new ArrayList<>(
                        storageBinList.parallelStream().map(DicWhStorageBin::getId).collect(Collectors.toSet())));
                }
                break;
        }
        // 非限制库存
        stockBinPo.setStockStatusSet(new HashSet<String>() {

            {
                add(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue().toString());
            }
        });
        List<StockBinDTO> stockBinDTOList = stockCommonService.getStockBinByStockBinPo(stockBinPo);
        log.info("获取扫描码的仓位库存-标签的库存信息 stockBinDTOList：{}", JSONObject.toJSONString(stockBinDTOList));
        if (UtilCollection.isNotEmpty(stockBinDTOList)) {
            // 仓位库存批次id集合
            Set<Long> batchIdSet = stockBinDTOList.stream().map(StockBinDTO::getBatchId).collect(Collectors.toSet());
            // 获取批次图片
            Map<Long, List<BizBatchImgDTO>> imgMap = bizBatchImgService.getBatchImgListByBatchIdList(batchIdSet, 4);
            // 标签数据 根据batchId binId cellId 查询 对应标签
            List<BizLabelData> labelDataList = labelDataService.selectByStockBinList(stockBinDTOList);
            log.info("获取扫描码的仓位库存-标签的labelData信息 labelDataList：{}",
                JSONObject.toJSONString(labelDataList, SerializerFeature.IgnoreNonFieldGetter));
            Map<String, List<BizLabelData>> labelDataMap = null;
            if (UtilCollection.isNotEmpty(labelDataList)) {
                labelDataMap = labelDataList.stream()
                    .collect(Collectors.groupingBy(label -> label.getMatId() + Const.HYPHEN + label.getFtyId()
                        + Const.HYPHEN + label.getLocationId() + Const.HYPHEN + label.getBatchId() + Const.HYPHEN
                        + label.getWhId() + Const.HYPHEN + label.getTypeId() + Const.HYPHEN + label.getBinId()
                        + Const.HYPHEN + label.getCellId()));
            }
            // 遍历物料仓位库存
            for (StockBinDTO stockBinDTO : stockBinDTOList) {
                arrangeItemDTO = UtilBean.newInstance(stockBinDTO, BizReceiptArrangeItemDTO.class);
                // 置空时间
                arrangeItemDTO.setCreateTime(null);
                arrangeItemDTO.setModifyTime(null);
                arrangeItemDTO.setFtyId(stockBinDTO.getFtyId());
                arrangeItemDTO.setWhId(stockBinDTO.getWhId());
                arrangeItemDTO.setSourceTypeId(stockBinDTO.getTypeId());
                arrangeItemDTO.setSourceTypeCode(stockBinDTO.getTypeCode());
                arrangeItemDTO.setSourceTypeName(stockBinDTO.getTypeName());
                arrangeItemDTO.setSourceBinId(stockBinDTO.getBinId());
                arrangeItemDTO.setSourceBinCode(stockBinDTO.getBinCode());
                arrangeItemDTO.setSourceCellId(stockBinDTO.getCellId());
                arrangeItemDTO.setCreateUserId(ctx.getCurrentUser().getId());
                arrangeItemDTO.setModifyUserId(ctx.getCurrentUser().getId());
                // 批次图片
                arrangeItemDTO.setBatchImgList(imgMap.get(stockBinDTO.getBatchId()));
                // 标签列表
                if (null != labelDataMap) {
                    String key = stockBinDTO.getMatId() + Const.HYPHEN + stockBinDTO.getFtyId() + Const.HYPHEN
                        + stockBinDTO.getLocationId() + Const.HYPHEN + stockBinDTO.getBatchId() + Const.HYPHEN
                        + stockBinDTO.getWhId() + Const.HYPHEN + stockBinDTO.getTypeId() + Const.HYPHEN
                        + stockBinDTO.getBinId() + Const.HYPHEN + stockBinDTO.getCellId();
                    arrangeItemDTO.setLabelDataList(labelDataMap.get(key));
                }
                itemList.add(arrangeItemDTO);
            }
        }
        head.setItemList(itemList);
        log.info("获取扫描码的仓位库存返回结果 head：{}", JSONObject.toJSONString(head));
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(head, extendVO, buttonVO));
    }

    /**
     * 保存仓位整理
     * 
     * @param ctx 系统上下文
     */
    public void saveBinArrange(BizContext ctx) {
        log.info("保存仓位整理 ctx：{}", JSONObject.toJSONString(ctx));
        /* ***************** 获取上下文参数 *****************/
        BizReceiptArrangeSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        /* ***************** 保存仓位整理 *****************/
        BizReceiptArrangeHeadDTO arrangeHeadDTO = po.getArrangeHeadDTO();
        // 整理单编码
        String receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TASK.getValue());
        /* ******* 整理单head *******/
        BizReceiptArrangeHeadDTO saveArrangeHeadDTO = new BizReceiptArrangeHeadDTO();
        saveArrangeHeadDTO.setReceiptCode(receiptCode);
        // 仓位整理
        saveArrangeHeadDTO.setReceiptType(EnumReceiptType.STOCK_BIN_ARRANGE.getValue());
        // 已完成状态
        saveArrangeHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        // 备注
        saveArrangeHeadDTO.setRemark(arrangeHeadDTO.getRemark());
        saveArrangeHeadDTO.setCreateUserId(currentUser.getId());
        // 保存整理单head
        bizReceiptArrangeHeadDataWrap.saveDto(saveArrangeHeadDTO);
        /* ******* 整理单item *******/
        List<BizReceiptArrangeItemDTO> saveArrangItemDTOList = new ArrayList<>();
        DicWhStorageBinDTO dicWhStorageBinDTO = po.getDicWhStorageBinDTO();
        // 请求行项目RFID
        Map<Long, List<BizLabelData>> reqItemLabelMap = new HashMap<>();
        AtomicInteger rid = new AtomicInteger(1);
        List<BizReceiptArrangeItemDTO> arrangeItemDTOList = arrangeHeadDTO.getItemList();
        arrangeItemDTOList.forEach(arrangeItem -> {
            BizReceiptArrangeItemDTO saveArrangItemDTO =
                UtilBean.deepCopyNewInstance(arrangeItem, BizReceiptArrangeItemDTO.class);
            saveArrangItemDTO.setId(null);
            saveArrangItemDTO.setHeadId(saveArrangeHeadDTO.getId());
            saveArrangItemDTO.setRid(String.valueOf(rid.getAndIncrement()));
            saveArrangItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            saveArrangItemDTO.setTargetTypeId(dicWhStorageBinDTO.getTypeId());
            saveArrangItemDTO.setTargetBinId(dicWhStorageBinDTO.getId());
            saveArrangItemDTO.setTargetCellId(null == po.getCellId() ? 0L : po.getCellId());
            saveArrangItemDTO.setQty(saveArrangItemDTO.getOperationQty());
            saveArrangItemDTOList.add(saveArrangItemDTO);
            reqItemLabelMap.put(arrangeItem.getId(), arrangeItem.getLabelDataList());
        });
        if (UtilCollection.isEmpty(saveArrangItemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_TASK_QTY_ERROR);
        }
        // 保存整理单item
        bizReceiptArrangeItemDataWrap.saveBatchDto(saveArrangItemDTOList);
        // 添加单据流
        this.addArrangeReceiptTree(saveArrangeHeadDTO.getReceiptType(), 0, saveArrangItemDTOList);
        // 标签关联关系集合
        List<BizLabelReceiptRel> labelReceiptRelList = new ArrayList<>();
        // 保存RFID&作业单关联关系
        if (!reqItemLabelMap.isEmpty()) {
            saveArrangItemDTOList.forEach(item -> {
                if (reqItemLabelMap.containsKey(item.getId())) {
                    for (BizLabelData label : reqItemLabelMap.get(item.getId())) {
                        BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                        labelReceiptRel.setLabelId(label.getId());
                        labelReceiptRel.setReceiptType(saveArrangeHeadDTO.getReceiptType());
                        labelReceiptRel.setReceiptHeadId(saveArrangeHeadDTO.getId());
                        labelReceiptRel.setReceiptItemId(item.getId());
                        labelReceiptRel.setPreReceiptHeadId(null);
                        labelReceiptRel.setPreReceiptItemId(null);
                        labelReceiptRel.setStatus(null);
                        labelReceiptRel.setCreateUserId(saveArrangeHeadDTO.getCreateUserId());
                        labelReceiptRelList.add(labelReceiptRel);
                    }
                }
            });
        }
        if (UtilCollection.isNotEmpty(labelReceiptRelList)) {
            labelReceiptRelService.saveBatch(labelReceiptRelList);
        }
        // 上下文添加参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, saveArrangeHeadDTO.getReceiptCode());
        // 组装headDTO
        saveArrangeHeadDTO.setItemList(saveArrangItemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, saveArrangeHeadDTO);
    }

    /* *************************************************组件*************************************************************/
    /**
     * 按钮组
     *
     * @param receiptStatus 单据状态
     * @return 按钮组对象
     */
    private ButtonVO setButton(Integer receiptStatus) {
        ButtonVO buttonVO = new ButtonVO();
        if (receiptStatus.equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())) {
            // 草稿状态,按钮保存、提交、删除
            buttonVO.setButtonSave(true);
            buttonVO.setButtonSubmit(true);
            buttonVO.setButtonDelete(true);
        }
        return buttonVO;
    }

    /**
     * 扩展功能配置
     *
     * @param receiptStatus 单据状态
     * @return 扩展功能配置对象
     */
    private ExtendVO setExtend(Integer receiptStatus) {
        // TODO: 2021/3/23 extend 显示逻辑
        return null;
    }

    /* *************************************************校验*************************************************************/
    /**
     * 仓位整理数据校验
     *
     * @param ctx 系统上下文
     */
    public void CheckSaveData(BizContext ctx) {
        log.info("仓位整理数据校验 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptArrangeSavePo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ************************ 空行项目校验 **********************************/
        if (null == po) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        if (UtilCollection.isEmpty(po.getArrangeHeadDTO().getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        /* ************************ 行项目操作数量为0校验，去除操作数量为0的行项目 **********************************/
        // 行项目
        List<BizReceiptArrangeItemDTO> arrangeItemDTOList = po.getArrangeHeadDTO().getItemList();
        // 过滤出整理数量为0 的行项目，整理数量为0，则提示“数量不能为0”
        List<BizReceiptArrangeItemDTO> checkNullQtyList =
            arrangeItemDTOList.stream().filter(arrangeItem -> arrangeItem.getOperationQty() == null
                || arrangeItem.getOperationQty().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(checkNullQtyList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_NOT_ZERO);
        }
        /* ************************ 行项目与仓位仓库是否一致 **********************************/
        DicWhStorageBinDTO binDTO = po.getDicWhStorageBinDTO();
        // 空仓位
        if (null == binDTO) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_ERROR);
        }
        // 异常仓库
        Set<String> errorWhBinSet = new HashSet<>();
        // 异常存储类型
        List<DicWhStorageBinDTO> errorDefaultBinList = new ArrayList<>();
        po.getArrangeHeadDTO().getItemList().forEach(item -> {
            // 行项目的仓库和提交仓位的仓库是否一致
            if (!item.getWhId().equals(binDTO.getWhId())) {
                errorWhBinSet.add(item.getRid());
            }
            // 提交仓位是否为默认仓位-根据存储类型是否为临时存储类型
            if (UtilConst.getInstance().getDefaultStorageTypeCodeSet().contains(binDTO.getTypeCode())) {
                errorDefaultBinList.add(binDTO);
            }
        });
        if (UtilCollection.isNotEmpty(errorWhBinSet)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_WH_NOT_EQUALS_BIN_WH, errorWhBinSet.toString());
        }
        if (UtilCollection.isNotEmpty(errorDefaultBinList)) {
            Set<String> whCodeSet = new HashSet<>(), typeCodeSet = new HashSet<>(), binCodeSet = new HashSet<>();
            errorDefaultBinList.forEach(bin -> {
                whCodeSet.add(bin.getWhCode());
                typeCodeSet.add(bin.getTypeCode());
                binCodeSet.add(bin.getBinCode());
            });
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NOT_CONFIGURATION_BIN_CODE, whCodeSet.toString(),
                typeCodeSet.toString(), binCodeSet.toString());
        }
    }

    /* *************************************************通用*************************************************************/
    /**
     * 保存整理单单单据流
     *
     * @param receiptType 单据类型
     * @param preReceiptType 前续单据类型
     * @param itemList 行项目集合
     */
    private void addArrangeReceiptTree(Integer receiptType, Integer preReceiptType,
        List<BizReceiptArrangeItemDTO> itemList) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptArrangeItemDTO item : itemList) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation();
            dto.setReceiptType(receiptType);
            dto.setReceiptHeadId(item.getHeadId());
            dto.setReceiptItemId(item.getId());
            dto.setPreReceiptType(preReceiptType);
            dto.setPreReceiptHeadId(0L);
            dto.setPreReceiptItemId(0L);
            dtoList.add(dto);
        }
        receiptRelationService.multiSaveReceiptTree(dtoList);
    }

    /**
     * 生成作业凭证
     * 
     * @param ctx 系统上下文
     */
    public void generateTaskInsDoc(BizContext ctx) {
        log.info("生成ins凭证 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptArrangeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        if (null == headDTO) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        StockInsMoveTypeDTO stockInsMoveTypeDTO = new StockInsMoveTypeDTO();
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            // 凭证code
            String code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_DOC.getValue());
            // rid
            AtomicInteger insDocRid = new AtomicInteger(1);
            List<StockInsDocBatch> insDocBatchList = new ArrayList<>();
            List<StockInsDocBin> insDocBinList = new ArrayList<>();
            List<StockInsDocBinPo> insDocBinPoList = new ArrayList<>();
            headDTO.getItemList().forEach(item -> {
                // 增加
                StockInsDocBin insDocBinS = new StockInsDocBin();
                insDocBinS.setInsDocCode(code);
                insDocBinS.setInsDocRid(String.valueOf(insDocRid.getAndIncrement()));
                insDocBinS.setMatId(item.getMatId());
                insDocBinS.setBatchId(item.getBatchId());
                insDocBinS.setFtyId(item.getFtyId());
                insDocBinS.setLocationId(item.getLocationId());
                insDocBinS.setWhId(item.getWhId());
                insDocBinS.setTypeId(item.getTargetTypeId());
                insDocBinS.setBinId(item.getTargetBinId());
                insDocBinS.setCellId(item.getTargetCellId());
                insDocBinS.setUnitId(item.getUnitId());
                insDocBinS.setMoveQty(item.getOperationQty());
                insDocBinS.setDebitCredit(Const.DEBIT_S_ADD);
                insDocBinS.setStockStatus(stockCommonService.getStockStatus(headDTO.getReceiptType()));
                // 前续单据作业单
                insDocBinS.setPreReceiptHeadId(item.getHeadId());
                insDocBinS.setPreReceiptItemId(item.getId());
                insDocBinS.setPreReceiptType(headDTO.getReceiptType());
                insDocBinS.setPreReceiptBinId(null);
                // 参考单据入库单
                insDocBinS.setReferReceiptHeadId(null);
                insDocBinS.setReferReceiptItemId(null);
                insDocBinS.setReferReceiptType(null);
                insDocBinS.setReferReceiptBinId(null);
                insDocBinS.setMatDocCode(null);
                insDocBinS.setMatDocRid(null);
                // 扣减
                StockInsDocBin insDocBinH = new StockInsDocBin();
                insDocBinH.setInsDocCode(code);
                insDocBinH.setInsDocRid(String.valueOf(insDocRid.getAndIncrement()));
                insDocBinH.setMatId(item.getMatId());
                insDocBinH.setBatchId(item.getBatchId());
                insDocBinH.setFtyId(item.getFtyId());
                insDocBinH.setLocationId(item.getLocationId());
                insDocBinH.setWhId(item.getWhId());
                insDocBinH.setTypeId(item.getSourceTypeId());
                insDocBinH.setBinId(item.getSourceBinId());
                insDocBinH.setCellId(item.getSourceCellId());
                insDocBinH.setUnitId(item.getUnitId());
                insDocBinH.setMoveQty(item.getOperationQty());
                insDocBinH.setDebitCredit(Const.CREDIT_H_SUBTRACT);
                insDocBinH.setStockStatus(stockCommonService.getStockStatus(headDTO.getReceiptType()));
                // 前续单据作业单
                insDocBinH.setPreReceiptHeadId(item.getHeadId());
                insDocBinH.setPreReceiptItemId(item.getId());
                insDocBinH.setPreReceiptType(headDTO.getReceiptType());
                insDocBinH.setPreReceiptBinId(null);
                // 参考单据入库单
                insDocBinH.setReferReceiptHeadId(null);
                insDocBinH.setReferReceiptItemId(null);
                insDocBinH.setReferReceiptType(null);
                insDocBinH.setReferReceiptBinId(null);
                insDocBinH.setMatDocCode(null);
                insDocBinH.setMatDocRid(null);
                insDocBinList.add(insDocBinH);
                insDocBinList.add(insDocBinS);
            });
            stockInsMoveTypeDTO.setInsDocBatchList(insDocBatchList);
            stockInsMoveTypeDTO.setInsDocBinList(insDocBinList);
            stockInsMoveTypeDTO.setInsDocBinPoList(insDocBinPoList);
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, stockInsMoveTypeDTO);
    }

    /**
     * 库存校验和数量计算
     *
     * @param ctx 系统上下文
     */
    public void checkAndComputeForModifyStock(BizContext ctx) {
        log.info("库存校验和数量计算 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
    }

    /**
     * 修改库存
     *
     * @param ctx 系统上下文
     */
    public void modifyStock(BizContext ctx) {
        log.info("修改库存 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        stockCommonService.modifyStock(insMoveTypeDTO);
    }

    /**
     * 保存操作日志
     *
     * @param ctx 系统上下文
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 保存的验收单
        BizReceiptArrangeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT, "", ctx.getCurrentUser().getId());
    }

    /**
     * 设置初始化信息
     *
     * @param ctx 上下文
     */
    public void setInit(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        ButtonVO buttonVO = new ButtonVO().setButtonSave(true).setButtonSubmit(true);
        BizResultVO<BizReceiptArrangeHeadDTO> resultVO =
                new BizResultVO<>(new BizReceiptArrangeHeadDTO().setReceiptType(EnumReceiptType.STOCK_BIN_ARRANGE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(user.getUserName()),
                        new ExtendVO(), buttonVO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 获取物料特性库存【非同时模式】
     *
     * @param ctx 上下文
     */
    public void getMatFeatureStock(BizContext ctx) {
        BizReceiptArrangeQueryPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Integer stockStatus = EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue();
        // 根据特性code查询特性库存
        BizReceiptAssembleRuleSearchPO pos = UtilBean.newInstance(po, BizReceiptAssembleRuleSearchPO.class);
        if (UtilString.isNotNullOrEmpty(po.getMatCode())) {
            Long matId = dictionaryService.getMatIdByMatCode(po.getMatCode());
            if (Objects.isNull(matId)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                return;
            }
            pos.setMatId(matId);
        }else if (UtilCollection.isNotEmpty(po.getMatCodeList())){
            Collection<Long > matIdList = dictionaryService.getMatIdListByMatCodeList(po.getMatCodeList());
            Set<Long> matIdSet = matIdList.stream().collect(Collectors.toSet());
            pos.setMatIdSet(matIdSet);
        }
        String matName = po.getMatName();
        if (StringUtils.isNotBlank(matName)) {
            List<DicMaterial> dicMaterialList = dicMaterialDataWrap.findByName(matName);
            if (CollectionUtils.isEmpty(dicMaterialList)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                return;
            }
            Set<Long> matIdSet = new HashSet<>(dicMaterialList.size());
            for (DicMaterial dicMaterial : dicMaterialList) {
                Long matId = dicMaterial.getId();
                matIdSet.add(matId);
            }
            Long matIdSearch = pos.getMatId();
            if (matIdSearch != null) {
                if (!matIdSet.contains(matIdSearch)) {
                    ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                    return;
                }
            } else {
                pos.setMatIdSet(matIdSet);
            }
        }
        String binCode = po.getBinCode();
        Long whId = po.getWhId();
        if (StringUtils.isNotBlank(binCode) && !UtilNumber.isEmpty(whId)) {
            DicWh dicWh = dictionaryService.getWhCacheById(po.getWhId());
            if (dicWh == null) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                return;
            }
            DicWhStorageBinDTO binDTO = dictionaryService.getBinCacheByBinCode(dicWh.getWhCode(), binCode);
            if (binDTO == null) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(new BizReceiptAssembleRuleDTO()));
                return;
            }
            pos.setBinId(binDTO.getId());
        }
        pos.setStockStatus(stockStatus);
        String specStockCode = pos.getSpecStockCode();
        if (StringUtils.isBlank(specStockCode))
            pos.setSpecStockCode(null);
        BizReceiptAssembleRuleDTO assembleRuleDTO = stockCommonService.getStockByFeatureCodeAndValueBySdw(null, null, po.getReceiptType(), pos);
        List<BizReceiptAssembleDTO> assembleDTOList = assembleRuleDTO.getAssembleDTOList();
        if (UtilCollection.isNotEmpty(assembleDTOList)) {
            for (BizReceiptAssembleDTO dto : assembleDTOList) {
                dto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue());
            }
            if (UtilCollection.isNotEmpty(po.getItemList())) {
                // 添加物料时, 过滤已选配货
                for (BizReceiptArrangeItemDTO itemDTO : po.getItemList()) {
                    for (BizReceiptAssembleDTO dto : assembleDTOList) {
                        if (dto.getBinIdTemp().equals(itemDTO.getSourceBinId())) {
                            dto.setStockQty(dto.getStockQty().subtract(itemDTO.getQty()));
                        }
                    }
                }
            }
            // 取表名,字段名
            String tableName = StockBin.class.getAnnotation(TableName.class).value();
            String tableFieldNameBinId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBinId);
            String tableFieldNameBatchId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getBatchId);
            String tableFieldNameTypeId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getTypeId);
            String tableFieldNameCellId =
                    tableName + Const.POINT + UtilMybatisPlus.getColumnByFunction(StockBin::getCellId);
            // 包含仓位批次时
            if (null != assembleRuleDTO.getFeatureCode()
                    && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBinId)
                    && assembleRuleDTO.getFeatureCode().contains(tableFieldNameBatchId)) {
                List<StockBinDTO> stockBinDTOList = new ArrayList<>();
                for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                    StockBinDTO stockBinDTO = new StockBinDTO();
                    // 工厂
                    stockBinDTO.setFtyId(assembleDTO.getFtyId());
                    // 库存地点
                    stockBinDTO.setLocationId(assembleDTO.getLocationId());
                    // 仓库
                    stockBinDTO.setWhId(po.getWhId());
                    // 物料
                    stockBinDTO.setMatId(assembleDTO.getMatId());
                    // 批次
                    Long batchInfoId = null;
                    List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                    List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                    for (int i = 0; i < codeList.size(); i++) {
                        if (codeList.get(i).equals(tableFieldNameBatchId)) {
                            // 批次
                            batchInfoId = Long.parseLong(valueList.get(i));
                            stockBinDTO.setBatchId(batchInfoId);
                        } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                            // 存储类型
                            stockBinDTO.setTypeId(Long.parseLong(valueList.get(i)));
                        } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                            // 仓位
                            stockBinDTO.setBinId(Long.parseLong(valueList.get(i)));
                        }
                    }
                    // 取批次信息中的标签类型, 若是非普通的批次标签, 则取标签列表
                    BizBatchInfoDTO batchInfoDTO = batchInfoService.getBatchInfoDto(batchInfoId);
                    if (!(batchInfoDTO.getTagType().equals(EnumTagType.GENERAL.getValue())
                            && batchInfoDTO.getIsSingle().equals(EnumRealYn.FALSE.getIntValue()))) {
                        stockBinDTOList.add(stockBinDTO);
                    }
                }
                // 批量查询标签列表
                if (UtilCollection.isNotEmpty(stockBinDTOList)) {
                    List<BizLabelData> labelDataVOList = labelDataService.getNonCellList(stockBinDTOList);
                    for (BizReceiptAssembleDTO assembleDTO : assembleRuleDTO.getAssembleDTOList()) {
                        Long batchInfoId = null, typeId = null, cellId = null, binId = null;
                        List<String> codeList = UtilString.split(assembleDTO.getSpecCode(), Const.COMMA_CHAR);
                        List<String> valueList = UtilString.split(assembleDTO.getSpecValue(), Const.COMMA_CHAR);
                        for (int i = 0; i < codeList.size(); i++) {
                            if (codeList.get(i).equals(tableFieldNameBatchId)) {
                                // 批次
                                batchInfoId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameTypeId)) {
                                // 存储类型
                                typeId = Long.parseLong(valueList.get(i));
                            } else if (codeList.get(i).equals(tableFieldNameBinId)) {
                                // 仓位
                                binId = Long.parseLong(valueList.get(i));
                            }
                        }
                        List<BizLabelReceiptRelDTO> labelDataList = new ArrayList<>();
                        for (BizLabelData labelData : labelDataVOList) {
                            if (labelData.getFtyId().equals(assembleDTO.getFtyId())
                                    && labelData.getMatId().equals(assembleDTO.getMatId())
                                    && labelData.getLocationId().equals(assembleDTO.getLocationId())
                                    && labelData.getBatchId().equals(batchInfoId) && labelData.getTypeId().equals(typeId)
                                    && labelData.getBinId().equals(binId)) {
                                // 唯一键相同时,匹配
                                BizLabelReceiptRelDTO labelReceiptRelDTO = new BizLabelReceiptRelDTO();
                                labelReceiptRelDTO.setLabelId(labelData.getId());
                                labelReceiptRelDTO.setLabelCode(labelData.getLabelCode());
                                labelReceiptRelDTO.setQty(labelData.getQty());
                                labelDataList.add(labelReceiptRelDTO);
                            }
                        }
                        assembleDTO.setLabelDataList(labelDataList);
                    }
                }
            }
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new SingleResultVO(assembleRuleDTO));
    }

    /**
     * 保存仓位整理
     *
     * @param ctx 系统上下文
     */
    public void saveBinArrangeNew(BizContext ctx, boolean submitFlag) {
        log.info("保存仓位整理 ctx：{}", JSONObject.toJSONString(ctx));
        /* ***************** 获取上下文参数 *****************/
        BizReceiptArrangeHeadDTO arrangeHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser currentUser = ctx.getCurrentUser();
        /* ***************** 保存仓位整理 *****************/
        arrangeHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        if (submitFlag) {
            arrangeHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        }
        BizReceiptArrangeHead arrangeHead = null;
        Long createUserId = currentUser.getId();
        if (UtilNumber.isNotEmpty(arrangeHeadDTO.getId())) {
            arrangeHead = bizReceiptArrangeHeadDataWrap.getById(arrangeHeadDTO.getId());
            createUserId = arrangeHead.getCreateUserId();
            // 根据id更新
            bizReceiptArrangeHeadDataWrap.updateDtoById(arrangeHeadDTO);
            QueryWrapper<BizReceiptArrangeItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(BizReceiptArrangeItem::getHeadId, arrangeHeadDTO.getId());
            bizReceiptArrangeItemDataWrap.physicalDelete(itemQueryWrapper);
            QueryWrapper<BizLabelReceiptRel> queryWrapperAssemble = new QueryWrapper<>();
            queryWrapperAssemble.lambda().eq(BizLabelReceiptRel::getReceiptHeadId, arrangeHeadDTO.getId());
            bizLabelReceiptRelDataWrap.physicalDelete(queryWrapperAssemble);
            // 特征物理删除
            bizReceiptAssembleDataWrap.physicalDeleteByHeadId(arrangeHeadDTO.getId());
        } else {
            // 整理单编码
            String receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TASK.getValue());
            /* ******* 整理单head *******/
            arrangeHeadDTO.setReceiptCode(receiptCode);
            // 仓位整理
            arrangeHeadDTO.setReceiptType(EnumReceiptType.STOCK_BIN_ARRANGE.getValue());
            // 备注
            arrangeHeadDTO.setRemark(arrangeHeadDTO.getRemark());
            arrangeHeadDTO.setCreateUserId(currentUser.getId());
            Date now = UtilDate.getNow();
            arrangeHeadDTO.setCreateTime(now);
            arrangeHeadDTO.setModifyTime(now);
            // 保存整理单head
            bizReceiptArrangeHeadDataWrap.saveDto(arrangeHeadDTO);
        }
        /* ******* 整理单item *******/
        List<BizReceiptArrangeItemDTO> saveArrangItemDTOList = new ArrayList<>();
        Long headId = arrangeHeadDTO.getId();
        // 请求行项目RFID
        AtomicInteger rid = new AtomicInteger(1);
        List<BizReceiptArrangeItemDTO> arrangeItemDTOList = arrangeHeadDTO.getItemList();
        arrangeItemDTOList.forEach(arrangeItem -> {
            BizReceiptArrangeItemDTO saveArrangItemDTO =
                    UtilBean.deepCopyNewInstance(arrangeItem, BizReceiptArrangeItemDTO.class);
            saveArrangItemDTO.setId(null);
            saveArrangItemDTO.setHeadId(headId);
            saveArrangItemDTO.setRid(String.valueOf(rid.getAndIncrement()));
            saveArrangItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            saveArrangItemDTO.setTargetCellId(0L);
            saveArrangItemDTO.setQty(saveArrangItemDTO.getOperationQty());
            saveArrangItemDTOList.add(saveArrangItemDTO);
        });
        if (UtilCollection.isEmpty(saveArrangItemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_TASK_QTY_ERROR);
        }
        // 保存整理单item
        bizReceiptArrangeItemDataWrap.saveBatchDto(saveArrangItemDTOList);
        // 特征表处理
        List<BizReceiptAssembleDTO> assembleDTOList = new ArrayList<>();
        for (BizReceiptArrangeItemDTO itemDTO : saveArrangItemDTOList) {
            Long itemId = itemDTO.getId();
            List<BizReceiptAssembleDTO> assembleList = itemDTO.getAssembleDTOList();
            if (!CollectionUtils.isEmpty(assembleList)) {
                for (BizReceiptAssembleDTO bizReceiptAssembleDTO : assembleList) {
                    bizReceiptAssembleDTO.setReceiptType(arrangeHeadDTO.getReceiptType());
                    bizReceiptAssembleDTO.setReceiptHeadId(arrangeHeadDTO.getId());
                    bizReceiptAssembleDTO.setReceiptItemId(itemId);
                    bizReceiptAssembleDTO.setId(null);
                    bizReceiptAssembleDTO.setSpecType(bizReceiptAssembleDTO.getSpecType() == null
                            ? EnumDbDefaultValueInteger.BIZ_RECEIPT_ASSEMBLE_SPEC_TYPE.getValue()
                            : bizReceiptAssembleDTO.getSpecType());
                    assembleDTOList.add(bizReceiptAssembleDTO);
                }
            }
        }
        bizReceiptAssembleDataWrap.saveBatchDto(assembleDTOList);
        if (submitFlag) {
            // 标签关联关系集合
            handleLabel(saveArrangItemDTOList, arrangeHeadDTO, assembleDTOList);
        } else {
            // 特征表配货处理
            List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
            for (BizReceiptAssembleDTO assembleDTO : assembleDTOList) {
                Long labelId = assembleDTO.getLabelId();
                Long itemId = assembleDTO.getReceiptItemId();
                if (UtilNumber.isNotEmpty(labelId)) {
                    BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                    labelReceiptRel.setLabelId(labelId);
                    labelReceiptRel.setReceiptType(arrangeHeadDTO.getReceiptType());
                    labelReceiptRel.setReceiptHeadId(assembleDTO.getReceiptHeadId());
                    labelReceiptRel.setReceiptItemId(itemId);
                    labelReceiptRel.setReceiptBinId(assembleDTO.getId());
                    bizLabelReceiptRelList.add(labelReceiptRel);
                }
            }
            if (UtilCollection.isNotEmpty(bizLabelReceiptRelList)) {
                labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
            }
        }
        // 上下文添加参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, arrangeHeadDTO.getReceiptCode());
        // 组装headDTO
        arrangeHeadDTO.setItemList(saveArrangItemDTOList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, arrangeHeadDTO);
    }

    private void handleLabel(List<BizReceiptArrangeItemDTO> itemDTOList, BizReceiptArrangeHeadDTO arrangeHeadDTO, List<BizReceiptAssembleDTO> assembleDTOList) {
        // 将所有标签操作数量大于0的按标签ID进行聚合，聚合时判断如果操作数量总和大于标签数量则异常
        // 逐个遍历行项目，如果某个行项目的存在标签，并判断这个标签的所有操作数量小于标签数量则拆分标签，拆分标签包含生成新标签数据和更新已有标签数量(扣除新标签数量)
        // 生成新的标签关联单据数据，并复制源标签关联单据到新标签上
        AtomicInteger rfidRid = new AtomicInteger(1);
        List<BizLabelData> labelNewList = new ArrayList<>();
        List<BizLabelData> labelUpdateList = new ArrayList<>();
        List<BizLabelReceiptRel> relNewList = new ArrayList<>();
        Map<Long, BizLabelData> labelEntityMap = new HashMap();
        Map<Long, BizReceiptArrangeItemDTO> itemDTOMap = new HashMap<>(itemDTOList.size());
        for (BizReceiptArrangeItemDTO itemDTO : itemDTOList) {
            Long itemId = itemDTO.getId();
            if (UtilNumber.isNotEmpty(itemId))
                itemDTOMap.put(itemId, itemDTO);
        }
        Integer receiptType = arrangeHeadDTO.getReceiptType();
        Long headId = arrangeHeadDTO.getId();
        Long userId = arrangeHeadDTO.getCreateUserId();
        for (BizReceiptAssembleDTO assembleDTO : assembleDTOList) {
            BizReceiptArrangeItemDTO itemDTO = itemDTOMap.get(assembleDTO.getReceiptItemId());
            Long labelId = assembleDTO.getLabelId();
            if (UtilNumber.isEmpty(labelId))
                continue;
            List<BizLabelReceiptRelDTO> relDTOList = assembleDTO.getLabelDataList();
            if (CollectionUtils.isEmpty(relDTOList)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            List<BizLabelReceiptRelDTO> opLabelList = relDTOList.stream().filter(e -> e.getLabelId() != null && e.getLabelId().equals(labelId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(opLabelList)) {
                continue;
            }
            BigDecimal qty = assembleDTO.getQty();
            Map<Long, BizLabelReceiptRelDTO> labelDataMap = new HashMap<>();
            for (BizLabelReceiptRelDTO bizLabelDataDTO : opLabelList) {
                BizLabelReceiptRelDTO temp = labelDataMap.get(labelId);
                if (temp == null) {
                    bizLabelDataDTO.setQty(qty);
                    labelDataMap.put(labelId, bizLabelDataDTO);
                    continue;
                }
                temp.setQty(temp.getQty().add(qty));
                labelDataMap.put(labelId, temp);
            }
            for (Long sourceLabelId : labelDataMap.keySet()) {
                BizLabelReceiptRelDTO bizLabelData = labelDataMap.get(sourceLabelId);
                BizLabelData sourceLabelData = labelEntityMap.get(sourceLabelId);
                if (sourceLabelData == null) {
                    sourceLabelData = bizLabelDataDataWrap.getById(sourceLabelId);
                    if (sourceLabelData == null)
                        throw new WmsException(EnumReturnMsg.LABEL_NOT_EXIST);
                    labelEntityMap.put(sourceLabelId, sourceLabelData);
                }
                BizLabelData rel = null ;
                BigDecimal operationQty = bizLabelData.getQty();
                if (operationQty.compareTo(sourceLabelData.getQty()) < 0) {
                    rel = UtilBean.newInstance(sourceLabelData, BizLabelData.class);
                    rel.setQty(operationQty);
                    rel.setId(null);
                    rel.setLabelCode(null);
                    rel.setSourceLabelId(sourceLabelId);
                    rel.setRid(rfidRid.getAndIncrement());
                    rel.setSnCode(null);
                    rel.setWhId(itemDTO.getWhId());
                    rel.setTypeId(itemDTO.getTargetTypeId());
                    rel.setBinId(itemDTO.getTargetBinId());
                    rel.setCellId(0L);
                    labelNewList.add(rel);
                    sourceLabelData.setQty(sourceLabelData.getQty().subtract(operationQty));
                    labelUpdateList.add(sourceLabelData);
                } else if (operationQty.compareTo(sourceLabelData.getQty()) == 0) {
                    rel = sourceLabelData;
                    sourceLabelData.setTypeId(itemDTO.getTargetTypeId());
                    sourceLabelData.setBinId(itemDTO.getTargetBinId());
                    labelUpdateList.add(sourceLabelData);
                } else {
                    throw new WmsException(EnumReturnMsg.LABEL_QTY_NOT_ENOUGH);
                }
                BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                labelReceiptRel.setLabelId(rel.getId());
                labelReceiptRel.setReceiptType(receiptType);
                labelReceiptRel.setReceiptHeadId(headId);
                labelReceiptRel.setReceiptItemId(itemDTO.getId());
                labelReceiptRel.setReceiptBinId(assembleDTO.getId());
                labelReceiptRel.setPreReceiptHeadId(null);
                labelReceiptRel.setPreReceiptItemId(null);
                labelReceiptRel.setPreReceiptBinId(null);
                labelReceiptRel.setRid(rel.getRid());
                labelReceiptRel.setStatus(null);
                labelReceiptRel.setCreateUserId(userId);
                relNewList.add(labelReceiptRel);
            }
        }
        Map<Integer, BizLabelDataDTO> labelDataDTOMap = new HashMap<>();
        if (UtilCollection.isNotEmpty(labelNewList)) {
            // 插入批次标签
            List<String> labelCodeList = bizCommonService.getNextNValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue(), labelNewList.size());
            int idx = 0;
            for (BizLabelData labelData : labelNewList){
                String code = labelCodeList.get(idx);
                labelData.setLabelCode(code);
                labelData.setSnCode(code);
                idx++;
            }
            List<BizLabelDataDTO> labelDataDTOList = UtilCollection.toList(labelNewList, BizLabelDataDTO.class);
            labelDataService.saveBatchDto(labelDataDTOList);
            // 复制关联关系
            labelReceiptRelService.copyRel(labelNewList);
            labelDataDTOList.forEach(e -> {
                labelDataDTOMap.put(e.getRid(), e);
            });
        }
        if (!CollectionUtils.isEmpty(labelUpdateList)){
            labelDataService.multiUpdateLabelData(labelUpdateList);
        }
        if (UtilCollection.isNotEmpty(relNewList)) {
            // 设置新标签id
            for (BizLabelReceiptRel rel : relNewList) {
                BizLabelDataDTO labelDataDTO = labelDataDTOMap.get(rel.getRid());
                if (labelDataDTO != null) {
                    rel.setLabelId(labelDataDTO.getId());
                }
            }
            labelReceiptRelService.saveBatch(relNewList);
        }
    }

    public void saveReceiptTree(BizContext ctx) {
        BizReceiptArrangeHeadDTO arrangeHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 添加单据流
        this.addArrangeReceiptTree(arrangeHeadDTO.getReceiptType(), 0, arrangeHeadDTO.getItemList());
    }

    /**
     * 删除
     * @param ctx
     */
    public void deleteReceipt(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptArrangeHead arrangeHead = bizReceiptArrangeHeadDataWrap.getById(id);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, arrangeHead);
        bizReceiptArrangeHeadDataWrap.removeById(id);
        bizReceiptArrangeItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptArrangeItem>() {
            {
                eq(BizReceiptArrangeItem::getHeadId, id);
            }
        });
        bizLabelReceiptRelDataWrap.remove(new LambdaQueryWrapper<BizLabelReceiptRel>() {
            {
                eq(BizLabelReceiptRel::getReceiptHeadId, id);
            }
        });
    }

    /**
     * 仓位整理数据校验
     *
     * @param ctx 系统上下文
     */
    public void checkSaveDataNew(BizContext ctx) {
        log.info("仓位整理数据校验 ctx：{}", JSONObject.toJSONString(ctx));
        /* ************************ 获取上下文参数 **********************************/
        BizReceiptArrangeHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ************************ 空行项目校验 **********************************/
        if (null == headDTO) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        List<BizReceiptArrangeItemDTO> itemList = headDTO.getItemList();
        if (UtilCollection.isEmpty(itemList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        /* ************************ 行项目操作数量为0校验，去除操作数量为0的行项目 **********************************/
        // 行项目
        // 过滤出整理数量为0 的行项目，整理数量为0，则提示“数量不能为0”
        List<BizReceiptArrangeItemDTO> checkNullQtyList =
                itemList.stream().filter(arrangeItem -> arrangeItem.getQty() == null
                        || arrangeItem.getQty().compareTo(BigDecimal.ZERO) <= 0).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(checkNullQtyList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_QTY_NOT_ZERO);
        }
        // 异常仓库
        Set<String> errorWhBinSet = new HashSet<>();
        Set<String> typeSet = UtilConst.getInstance().getDefaultStorageTypeCodeSet();
        // 异常存储类型
        List<DicWhStorageBinDTO> errorDefaultBinList = new ArrayList<>();
        Map<String, DicWhStorageBinDTO> whBinMap = new HashMap<>();
        Integer trueFlag = EnumRealYn.TRUE.getIntValue();
        Set<Long> typeIdSet = new HashSet<>();
        itemList.forEach(item -> {
            // 提交仓位是否为默认仓位-根据存储类型是否为临时存储类型
            String sourceTypeCode = item.getSourceTypeCode();
            if (typeSet.contains(sourceTypeCode)) {
                DicWhStorageBinDTO binDTO = new DicWhStorageBinDTO();
                binDTO.setWhCode(item.getWhCode());
                binDTO.setTypeCode(sourceTypeCode);
                binDTO.setBinCode(item.getSourceBinCode());
                errorDefaultBinList.add(binDTO);
            }
            String targetTypeCode = item.getTargetTypeCode();
            if (typeSet.contains(item.getTargetTypeCode())) {
                DicWhStorageBinDTO binDTO = new DicWhStorageBinDTO();
                binDTO.setWhCode(item.getWhCode());
                binDTO.setTypeCode(targetTypeCode);
                binDTO.setBinCode(item.getTargetBinCode());
                errorDefaultBinList.add(binDTO);
            }
            // 校验目标仓位是否在同一仓库和存储类型下
            Long sourceWhId = item.getWhId();
            Long sourceTypeId = item.getSourceTypeId();
            String sourceBinCode = item.getSourceBinCode();
            String existKeySource = String.format("%d-%d-%s", sourceWhId, sourceTypeId, sourceBinCode);
            String targetBinCode = item.getTargetBinCode();
            Long targetTypeId = item.getTargetTypeId();
            String existKeyTarget = String.format("%d-%d-%s", sourceWhId, targetTypeId, targetBinCode);
            if (!typeIdSet.contains(sourceTypeId)) {
                Collection<DicWhStorageBinDTO> binDTOCollection = dictionaryService.getBinCacheByTypeId(sourceTypeId);
                if (binDTOCollection == null) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_WCS_THE_REQUIRED_POSITION_IS_EMPTY);
                }
                for (DicWhStorageBinDTO binDTO : binDTOCollection) {
                    Integer deleteFlag = binDTO.getIsDelete();
                    if (trueFlag.equals(deleteFlag)) {
                        continue;
                    }
                    Long whId = binDTO.getWhId();
                    Long typeId = binDTO.getTypeId();
                    String binCode = binDTO.getBinCode();
                    String key = String.format("%d-%d-%s", whId, typeId, binCode);
                    whBinMap.put(key, binDTO);
                }
                typeIdSet.add(sourceTypeId);
            }
            if (!typeIdSet.contains(targetTypeId)) {
                Collection<DicWhStorageBinDTO> binDTOCollection = dictionaryService.getBinCacheByTypeId(targetTypeId);
                if (binDTOCollection == null) {
                    throw new WmsException(EnumReturnMsg.RETURN_CODE_WCS_THE_REQUIRED_POSITION_IS_EMPTY);
                }
                for (DicWhStorageBinDTO binDTO : binDTOCollection) {
                    Integer deleteFlag = binDTO.getIsDelete();
                    if (trueFlag.equals(deleteFlag)) {
                        continue;
                    }
                    Long whId = binDTO.getWhId();
                    Long typeId = binDTO.getTypeId();
                    String binCode = binDTO.getBinCode();
                    String key = String.format("%d-%d-%s", whId, typeId, binCode);
                    whBinMap.put(key, binDTO);
                }
                typeIdSet.add(sourceTypeId);
            }
            DicWhStorageBinDTO binDTOSource = whBinMap.get(existKeySource);
            DicWhStorageBinDTO binDTOTarget = whBinMap.get(existKeyTarget);
            if (binDTOTarget == null) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_WCS_THE_REQUIRED_POSITION_IS_EMPTY, targetBinCode);
            }
            if (trueFlag.equals(binDTOSource.getFreezeOutput())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_FREEZING, sourceBinCode);
            }
            if (trueFlag.equals(binDTOTarget.getFreezeInput())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_BIN_FREEZING, targetBinCode);
            }
            item.setTargetBinId(binDTOTarget.getId());
            item.setSourceCellId(0L);
            item.setTargetCellId(0L);
        });
        if (UtilCollection.isNotEmpty(errorWhBinSet)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_WH_NOT_EQUALS_BIN_WH, errorWhBinSet.toString());
        }
        if (UtilCollection.isNotEmpty(errorDefaultBinList)) {
            Set<String> whCodeSet = new HashSet<>(), typeCodeSet = new HashSet<>(), binCodeSet = new HashSet<>();
            errorDefaultBinList.forEach(bin -> {
                whCodeSet.add(bin.getWhCode());
                typeCodeSet.add(bin.getTypeCode());
                binCodeSet.add(bin.getBinCode());
            });
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NOT_CONFIGURATION_BIN_CODE, whCodeSet.toString(),
                    typeCodeSet.toString(), binCodeSet.toString());
        }

    }

    /**
     * 导出
     * @param ctx
     */
    public void export(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("仓位整理"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);

        // 上下文入参
        BizReceiptArrangeSearchPo po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        List<BizReceiptArrangeItemExportVO> list = bizReceiptArrangeItemDataWrap.selectExportItemList(po);
        dataFillService.fillAttr(list);

        String langCode = this.getLangCodeFromRequest();

        for (BizReceiptArrangeItemExportVO exportVo : list) {
            exportVo.setReceiptStatusI18n(i18nTextCommonService.getNameMessage(langCode, "receiptStatus", exportVo.getReceiptStatus().toString()));
        }
        UtilExcel.writeExcel(BizReceiptArrangeItemExportVO.class, list, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }


    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }

    private String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }


    private List<BizReceiptArrangeItemImportVO> readExcelWithValidation(InputStream inputStream) {
        try {
            return (List<BizReceiptArrangeItemImportVO>) UtilExcel.readExcelData(inputStream, BizReceiptArrangeItemImportVO.class, 1);
        } catch (ExcelAnalysisException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
        } catch (Exception e) {
            log.error("Excel解析异常", e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    public void importBinArrange(BizContext ctx, MultipartFile file, Long ftyId, Long locationId, Long whId) {
        try (InputStream inputStream = file.getInputStream()) {
            List<BizReceiptArrangeItemDTO> itemDTOList = new ArrayList<>();
            List<BizReceiptArrangeItemImportVO> importList = readExcelWithValidation(inputStream);
            DicStockLocationDTO location = dictionaryService.getLocationCacheById(locationId);

            for (BizReceiptArrangeItemImportVO vo : importList) {
                this.validateRequiredFields(vo);

                DicMaterialDTO mat = this.getMaterial(vo.getMatCode());
                DicWhStorageBinDTO sourceBin = dictionaryService.getBinCacheByCode(vo.getSourceBinCode());
                DicWhStorageBinDTO targetBin = dictionaryService.getBinCacheByCode(vo.getTargetBinCode());

                this.validateBins(sourceBin, targetBin, vo);
                this.validateLocation(location, vo);

                BizReceiptArrangeQueryPO po = this.buildQueryPO(mat, sourceBin, vo, ftyId, locationId, whId);
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
                // 查询库存
                this.getMatFeatureStock(ctx);
                BizReceiptAssembleRuleDTO assemble = validateAssembleResult(ctx);

                this.validateStockQty(assemble, vo, location);

                BizReceiptArrangeItemDTO itemDTO = this.buildArrangeItem(vo, assemble, sourceBin, targetBin);
                itemDTOList.add(itemDTO);
            }

            MultiResultVO<BizReceiptArrangeItemDTO> bizResultVO = new MultiResultVO<>();
            bizResultVO.setResultList(itemDTOList);
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, bizResultVO);
        } catch (IOException e) {
            log.error("导入失败，文件：{}", file.getOriginalFilename(), e);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
    }

    private void validateLocation(DicStockLocationDTO location, BizReceiptArrangeItemImportVO vo) {
        // 校验库存地点
        if (!location.getLocationCode().equals(vo.getLocationCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IMPORT_LOCATION_NOT_EQUAL_HEAD_LOCATION, vo.getLocationCode());
        }
    }

    /**
     * 校验必填项
     *
     * @param vo
     */
    private void validateRequiredFields(BizReceiptArrangeItemImportVO vo) {
        if (Stream.of(vo.getMatCode(), vo.getFtyCode(), vo.getLocationCode(),
                        vo.getSourceBinCode(), vo.getBatchCode(), vo.getTargetBinCode())
                .anyMatch(UtilString::isNullOrEmpty) || UtilNumber.isEmpty(vo.getQty())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
    }

    private DicMaterialDTO getMaterial(String matCode) {
        Long matId = dictionaryService.getMatIdByMatCode(matCode);
        DicMaterialDTO mat = dictionaryService.getMatCacheById(matId);
        if (UtilObject.isNull(mat)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_ALERT_NOT_EXIST, matCode);
        }
        return mat;
    }

    private void validateBins(DicWhStorageBinDTO sourceBin, DicWhStorageBinDTO targetBin, BizReceiptArrangeItemImportVO vo) {
        if (UtilObject.isNull(sourceBin)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_NOT_EXIST, "源库位:" + vo.getSourceBinCode());
        }
        if (UtilObject.isNull(targetBin)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_STORAGE_BIN_NOT_EXIST, "目标库位:" + vo.getTargetBinCode());
        }
    }

    private BizReceiptArrangeQueryPO buildQueryPO(DicMaterialDTO mat, DicWhStorageBinDTO sourceBin,
                                                  BizReceiptArrangeItemImportVO vo, Long ftyId,
                                                  Long locationId, Long whId) {
        BizReceiptArrangeQueryPO po = new BizReceiptArrangeQueryPO();
        po.setMatId(mat.getId());
        po.setBatchCode(vo.getBatchCode());
        po.setBinId(sourceBin.getId());
        po.setFtyId(ftyId);
        po.setLocationId(locationId);
        po.setWhId(whId);
        po.setReceiptType(EnumReceiptType.STOCK_BIN_ARRANGE.getValue());
        return po;
    }

    private BizReceiptAssembleRuleDTO validateAssembleResult(BizContext ctx) {
        SingleResultVO<BizReceiptAssembleRuleDTO> singleResultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptAssembleRuleDTO assemble = singleResultVO.getResult();
        if (UtilCollection.isEmpty(assemble.getAssembleDTOList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        return assemble;
    }

    private void validateStockQty(BizReceiptAssembleRuleDTO assemble, BizReceiptArrangeItemImportVO vo,
                                  DicStockLocationDTO location) {
        BizReceiptAssembleDTO dto = assemble.getAssembleDTOList().get(0);
        if (dto.getStockQty().compareTo(vo.getQty()) < 0) {
            List<String> args = Lists.newArrayList(
                    location.getFtyCode(),
                    location.getLocationCode(),
                    vo.getMatCode(),
                    vo.getBatchCode()
            );
            throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_UNRESTRICTED,
                    args.toArray(new String[0]));
        }
        dto.setQty(vo.getQty());
    }

    private BizReceiptArrangeItemDTO buildArrangeItem(BizReceiptArrangeItemImportVO vo, BizReceiptAssembleRuleDTO assemble,
                                                      DicWhStorageBinDTO sourceBin,
                                                      DicWhStorageBinDTO targetBin) {
        BizReceiptArrangeItemDTO itemDTO = new BizReceiptArrangeItemDTO();
        UtilBean.copy(assemble.getAssembleDTOList().get(0), itemDTO);
        itemDTO.setId(null);
        itemDTO.setBatchCode(vo.getBatchCode());
        itemDTO.setSourceBinId(sourceBin.getId());
        itemDTO.setBinIdTemp(sourceBin.getId());
        itemDTO.setSourceBinCode(sourceBin.getBinCode());
        itemDTO.setSourceTypeId(sourceBin.getTypeId());
        itemDTO.setTargetBinId(targetBin.getId());
        itemDTO.setTargetTypeId(targetBin.getTypeId());
        itemDTO.setTargetBinCode(targetBin.getBinCode());
        itemDTO.setOperationQty(vo.getQty());
        for (BizReceiptAssembleDTO bizReceiptAssembleDTO : assemble.getAssembleDTOList()) {
            bizReceiptAssembleDTO.setQty(vo.getQty());
        }
        itemDTO.setAssembleDTOList(assemble.getAssembleDTOList());
        return itemDTO;
    }





}