package com.inossem.wms.bizdomain.inspect.service.component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialFactoryService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicStockLocationDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.biz.UserService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeCaseRelDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeItemDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectUserDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.inconformity.EnumDifferentType;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeCaseRel;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeItem;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityItemDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectHeadDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectUserDTO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectUser;
import com.inossem.wms.common.model.bizdomain.inspect.po.BizReceiptInspectSearchPO;
import com.inossem.wms.common.model.bizdomain.inspect.vo.BizReceiptInspectHeadVO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnBinDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnHeadDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnItemDTO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.fty.entity.DicMaterialFacotryWbs;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.entity.DicStockLocation;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 质检会签 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-04-26
 */
@Slf4j
@Service
public class SignInspectComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected BizCommonService bizCommonService;

    @Autowired
    protected InspectCommonComponent inspectCommonComponent;

    @Autowired
    protected DicStockLocationDataWrap dicStockLocationDataWrap;

    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    protected BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    @Autowired
    protected BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    protected BatchInfoService batchInfoService;

    @Autowired
    protected MaterialFactoryService materialFactoryService;

    @Autowired
    protected BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;

    @Autowired
    protected BizReceiptInspectUserDataWrap bizReceiptInspectUserDataWrap;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private ErpPurchaseReceiptItemDataWrap purchaseReceiptItemDataWrap;

    @Autowired
    private UserService userService;

    @Autowired
    private BizReceiptContractHeadDataWrap contractHeadDataWrap;

    @Autowired
    private BizReceiptDeliveryNoticeItemDataWrap deliveryNoticeItemDataWrap;
    @Autowired
    private BizReceiptDeliveryNoticeHeadDataWrap deliveryNoticeHeadDataWrap;
    @Autowired
    private BizReceiptDeliveryNoticeCaseRelDataWrap deliveryNoticeCaseRelDataWrap;
    @Autowired
    private BizReceiptInputHeadDataWrap inputHeadDataWrap;
    @Autowired
    private BizReceiptInputItemDataWrap inputItemDataWrap;

    /**
     * 查询库存地点-根据选中的工厂id获取
     *
     * @out ctx 出参 {@link MultiResultVO <> ("dicCarTypeDataWrap.list()":"库存地点下拉框")}
     */
    public void getLocationList(BizContext ctx) {
        // 从上下文获取工厂id
        Long ftyId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 查询库存地点下拉
        QueryWrapper<DicStockLocation> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DicStockLocation::getFtyId, ftyId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new MultiResultVO<>(dicStockLocationDataWrap.list(queryWrapper)));
    }

    /**
     * 查询质检会签单列表-分页
     *
     * @in ctx 入参 {@link BizReceiptInspectSearchPO :"质检会签单分页查询入参"}
     * @out ctx 出参 {@link PageObjectVO <> ("page.getRecords()":"列表数据","page.getTotal()":"总条数")}
     */
    public void setPage(BizContext ctx) {
        // 从上下文获取查询条件对象
        BizReceiptInspectSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 分页查询处理
        IPage<BizReceiptInspectHeadVO> page = po.getPageObj(BizReceiptInspectHeadVO.class);
//        CurrentUser user = ctx.getCurrentUser();
//        List<Long> locationIdList =null;
//        if(user!=null &&user.getLocationList()!=null){
//            List<DicStockLocationDTO> locationList =user.getLocationList();
//            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
//        }
//        po.setLocationIdList(locationIdList);
        if(UtilObject.isNotNull(po.getSubmitStartTime())){
            po.setSubmitStartTime(UtilDate.getStartOfDay(po.getSubmitStartTime()));
            po.setSubmitEndTime(UtilDate.getEndOfDay(po.getSubmitEndTime()));
        }
        bizReceiptInspectHeadDataWrap.getPageVOList(page, po);
        // 分页结果信息放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 质检会签单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"质检会签单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx,boolean isPage) {
        final BizReceiptInspectHeadDTO headDTO = new BizReceiptInspectHeadDTO();
        Long headId = null;
        if(isPage) {
            BizReceiptInspectSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
            headId = po.getId();
            // 获取质检会签单详情
            final BizReceiptInspectHeadDTO tempDTO = UtilBean.newInstance(bizReceiptInspectHeadDataWrap.getById(headId), BizReceiptInspectHeadDTO.class);
            dataFillService.fillAttr(tempDTO);
            tempDTO.setTotalCount(tempDTO.getItemList().size());
            // 对itemList进行分页处理
            if(tempDTO != null && !CollectionUtils.isEmpty(tempDTO.getItemList())) {
                List<BizReceiptInspectItemDTO> itemList = tempDTO.getItemList();
                int pageIndex = po.getPageIndex();
                int pageSize = po.getPageSize();
                
                // 计算分页起始和结束索引
                int fromIndex = (pageIndex - 1) * pageSize;
                int toIndex = Math.min(fromIndex + pageSize, itemList.size());
                
                // 截取分页数据
                if(fromIndex < itemList.size()) {
                    List<BizReceiptInspectItemDTO> pagedItemList = itemList.subList(fromIndex, toIndex);
                    tempDTO.setItemList(pagedItemList);
                } else {
                    tempDTO.setItemList(new ArrayList<>());
                }
            }
            // 将tempDTO的所有属性复制到headDTO
            UtilBean.copy(tempDTO, headDTO);
            
        } else {
            headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
            if (UtilNumber.isEmpty(headId)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
            }
            // 获取质检会签单详情
            final BizReceiptInspectHeadDTO tempDTO = UtilBean.newInstance(bizReceiptInspectHeadDataWrap.getById(headId), BizReceiptInspectHeadDTO.class);
            // 填充关联属性和父子属性
            dataFillService.fillAttr(tempDTO);
            // 将tempDTO的所有属性复制到headDTO
            UtilBean.copy(tempDTO, headDTO);
        }

       
        headDTO.setReceiptApplyCode(this.getReceiptApplyCode(headId, headDTO.getReceiptType()));
        headDTO.setSupplierName(headDTO.getItemList().get(0).getSupplierName());
        headDTO.setSign1(UtilPrint.SIGNATURE);
        headDTO.setSign2(UtilPrint.SIGNATURE);
        headDTO.setSign3(UtilPrint.SIGNATURE);
        headDTO.setSign4(UtilPrint.SIGNATURE);
        headDTO.setSign5(UtilPrint.SIGNATURE);
        headDTO.setSign6(UtilPrint.SIGNATURE);
        headDTO.setSign7(UtilPrint.SIGNATURE);
        headDTO.setSign8(UtilPrint.SIGNATURE);
        headDTO.setSign9(UtilPrint.SIGNATURE);
        headDTO.setSign10(UtilPrint.SIGNATURE);
        List<BizReceiptInspectUserDTO> userDTOList = headDTO.getInspectUserList();
        if (!CollectionUtils.isEmpty(userDTOList)) {
            for (int i = 0; i < userDTOList.size(); i++) {
                BizReceiptInspectUserDTO userDTO = userDTOList.get(i);
                String inspectUserName = userDTO.getInspectUserName();
                String inspectCompany = userDTO.getInspectCompany();
                if (StringUtils.isNoneBlank(inspectUserName) && inspectUserName.startsWith("data:image/svg")) {
                    if (i == 0) {
                        headDTO.setSign1(inspectUserName);
                        headDTO.setSignCompany1(inspectCompany);
                        continue;
                    }
                    if (i == 1) {
                        headDTO.setSign2(inspectUserName);
                        headDTO.setSignCompany2(inspectCompany);
                        continue;
                    }
                    if (i == 2) {
                        headDTO.setSign3(inspectUserName);
                        headDTO.setSignCompany3(inspectCompany);
                        continue;
                    }
                    if (i == 3) {
                        headDTO.setSign4(inspectUserName);
                        headDTO.setSignCompany4(inspectCompany);
                        continue;
                    }
                    if (i == 4) {
                        headDTO.setSign5(inspectUserName);
                        headDTO.setSignCompany5(inspectCompany);
                        continue;
                    }
                    if (i == 5) {
                        headDTO.setSign6(inspectUserName);
                        headDTO.setSignCompany6(inspectCompany);
                        continue;
                    }
                    if (i == 6) {
                        headDTO.setSign7(inspectUserName);
                        headDTO.setSignCompany7(inspectCompany);
                        continue;
                    }
                    if (i == 7) {
                        headDTO.setSign8(inspectUserName);
                        headDTO.setSignCompany8(inspectCompany);
                        continue;
                    }
                    if (i == 8) {
                        headDTO.setSign9(inspectUserName);
                        headDTO.setSignCompany9(inspectCompany);
                        continue;
                    }
                    if (i == 9) {
                        headDTO.setSign10(inspectUserName);
                        headDTO.setSignCompany10(inspectCompany);
                        break;
                    }
                }
            }
        }

        // 配置打印相关属性字段 实到数量
        headDTO.getItemList().forEach(itemDTO -> {
            // 合格数量+不合格数量-未到货数量） = 实到数量
            itemDTO.setActualQty(itemDTO.getQty().add(itemDTO.getUnqualifiedQty()).subtract(itemDTO.getUnarrivalQty()));
            if (itemDTO.getUnqualifiedQty().compareTo(BigDecimal.ZERO)!=0||itemDTO.getUnarrivalQty().compareTo(BigDecimal.ZERO)!=0){
                itemDTO.setIsDiff(1);
                itemDTO.setIsDiffI18n("是");
            }else {
                itemDTO.setIsDiff(0);
                itemDTO.setIsDiffI18n("否");
            }

            // 填充申请单号（退库&退转库打印时需要）
            if (UtilObject.isNotEmpty(headDTO.getReceiptApplyCode())) {
                itemDTO.setReceiptApplyCode(headDTO.getReceiptApplyCode());
            }

            // 从项目工厂物料主数据(dic_material_factory_wbs)中得到移动平均价，设置到行项目中，以便打印时使用
            ErpPurchaseReceiptItem purchaseReceiptItem = purchaseReceiptItemDataWrap.getById(itemDTO.getReferReceiptItemId());
            itemDTO.setMoveAvgPrice(BigDecimal.ZERO);
            itemDTO.setPrice(BigDecimal.ZERO);
            if (UtilObject.isNotEmpty(purchaseReceiptItem)) {
                headDTO.setApplyUserCode(purchaseReceiptItem.getApplyUserCode());
                headDTO.setApplyUserName(purchaseReceiptItem.getApplyUserName());
                itemDTO.setPrice(purchaseReceiptItem.getPrice());
                if (EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue().equals(purchaseReceiptItem.getSpecStock())) {
                    String materialFactoryWbsKey = itemDTO.getMatId() + "##" + itemDTO.getFtyId() + "##" + purchaseReceiptItem.getSpecStock() + "##" + purchaseReceiptItem.getSpecStockCode();
                    DicMaterialFacotryWbs materialFacotryWbsByKey = dictionaryService.getMaterialFacotryWbsByKey(materialFactoryWbsKey);
                    if (UtilObject.isNotNull(materialFacotryWbsByKey)) {
                        itemDTO.setMoveAvgPrice(materialFacotryWbsByKey.getMoveAvgPrice());
                    } else {
                        log.warn("质检会签-单据{}，行项目{}，物料{}-{}，在设置移动平均价时，未能在物料工厂WBS{}下获取到有效数据", headDTO.getReceiptCode(), itemDTO.getRid(), itemDTO.getMatId(), itemDTO.getMatName(), itemDTO.getFtyCode());
                    }
                } else {
                    DicMaterialFactoryDTO materialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(itemDTO.getMatId(), itemDTO.getFtyId());
                    if (UtilObject.isNotNull(materialFactoryDTO)) {
                        itemDTO.setMoveAvgPrice(materialFactoryDTO.getMoveAvgPrice());
                    } else {
                        log.warn("质检会签-单据{}，行项目{}，物料{}-{}，在设置移动平均价时，未能在物料工厂{}下获取到有效数据", headDTO.getReceiptCode(), itemDTO.getRid(), itemDTO.getMatId(), itemDTO.getMatName(), itemDTO.getFtyCode());
                    }
                }
            }

            // 填充抬头 公司名称信息 打印时需要 取行项目工厂对应的公司信息
            if (UtilString.isNullOrEmpty(headDTO.getCorpName())) {
                DicFactoryDTO factoryDTO = dictionaryService.getFtyCacheById(itemDTO.getFtyId());
                if (factoryDTO == null) {
                    // 如果缓存没有获取到，默认一个公司名称（这种情况为异常数据，行项目上缺少了工厂有效的工厂id）
                    log.warn("质检会签单{} 行项目缺少有效的工厂数据，请检查", headDTO.getReceiptCode());
                    headDTO.setCorpName(Const.FACTORY_J046_DEFAULT_CORP_NAME);
                } else {
                    headDTO.setCorpName(factoryDTO.getCorpName());
                }
            }

            // 从工厂物料主数据(dic_material_factory)缓存数据中读取包装方式，覆盖行项目中的包装方式数据
            DicMaterialFactoryDTO materialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(itemDTO.getMatId(), itemDTO.getFtyId());
            if (materialFactoryDTO != null) {
                itemDTO.setPackageType(materialFactoryDTO.getPackageType());
            } else {
                log.warn("质检会签单{} 行项目缺少有效的工厂物料数据，请检查，行项目rid={}, 物料id={}, 工厂Id={}", headDTO.getReceiptCode(), itemDTO.getRid(), itemDTO.getMatId(), itemDTO.getFtyId());
            }
        });
        // 设置需求人科室和部门信息
        SysUser sysUser = dictionaryService.getSysUserCacheByuserCode(headDTO.getApplyUserCode());
        if (UtilObject.isNotNull(sysUser)) {
            SysUserDTO userDTO = userService.getSysUserInfoById(sysUser.getId());
            headDTO.setApplyUserDept(userDTO.getDeptDTOList().stream().map(DicDeptDTO::getDeptName).distinct().collect(Collectors.joining(",")));
            headDTO.setApplyUserOffice(userDTO.getDeptOfficeNames());
        }

        this.setPrintData(headDTO);

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置质检会签单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 设置打印数据
     */
    private void setPrintData(BizReceiptInspectHeadDTO headDTO) {
        if (UtilCollection.isEmpty(headDTO.getItemList())){
            return;
        }

        List<BizReceiptDeliveryNoticeItem> deliveryNoticeItemList = deliveryNoticeItemDataWrap.listByIds(headDTO.getItemList().stream().map(BizReceiptInspectItemDTO::getDeliveryItemId).collect(Collectors.toSet()));
        if (UtilCollection.isNotEmpty(deliveryNoticeItemList)) {
            BizReceiptDeliveryNoticeHead deliveryNoticeHead = deliveryNoticeHeadDataWrap.getById(deliveryNoticeItemList.get(0).getHeadId());

            // 转dto
            BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO = UtilBean.newInstance(deliveryNoticeHead, BizReceiptDeliveryNoticeHeadDTO.class);
            dataFillService.fillRlatAttrForDataObj(deliveryNoticeHeadDTO);

            List<BizReceiptDeliveryNoticeCaseRel> deliveryNoticeCaseRelList = deliveryNoticeCaseRelDataWrap.list(new QueryWrapper<BizReceiptDeliveryNoticeCaseRel>() {{
                lambda().eq(BizReceiptDeliveryNoticeCaseRel::getHeadId, deliveryNoticeHeadDTO.getId());
            }});

            if(UtilCollection.isNotEmpty(deliveryNoticeCaseRelList)) {
                headDTO.setCaseNumber(deliveryNoticeCaseRelList.size());
            }
            headDTO.setDeliveryNoticeCode(deliveryNoticeHeadDTO.getReceiptCode());
            headDTO.setRealDeliveryTime(headDTO.getItemList().get(0).getReceiveDate());
            headDTO.setBatchCode(deliveryNoticeHeadDTO.getBatchCode());
            headDTO.setSupplierName(deliveryNoticeHeadDTO.getSupplierName());

            for (BizReceiptInspectItemDTO bizReceiptInspectItemDTO : headDTO.getItemList()) {
                for (BizReceiptDeliveryNoticeItem bizReceiptDeliveryNoticeItem : deliveryNoticeItemList) {
                    if (bizReceiptDeliveryNoticeItem.getId().equals(bizReceiptInspectItemDTO.getDeliveryItemId())){
                        bizReceiptInspectItemDTO.setCaseCode(bizReceiptDeliveryNoticeItem.getCaseCode());
                    }
                }
            }
        }

        List<BizReceiptInputItem> inputItemList = inputItemDataWrap.list(new QueryWrapper<BizReceiptInputItem>() {{
            lambda().eq(BizReceiptInputItem::getPreReceiptHeadId, headDTO.getId());
        }});

        if (UtilCollection.isNotEmpty(inputItemList)){
            headDTO.setIsRelease(1);
        } else {
            headDTO.setIsRelease(0);
        }
    }

    /**
     * 按钮组
     *
     * @param headDTO 登记单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptInspectHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            if(!headDTO.getReceiptType().equals(EnumReceiptType.SIGN_INSPECTION_RETURN.getValue())){
                // [30752]【退库质检会签】隐藏退库质检会签单的打印按钮
                buttonVO.setButtonPrint(true);
            }
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        } else if(EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        } else if(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)
                && !headDTO.getReceiptType().equals(EnumReceiptType.SIGN_INSPECTION_RETURN.getValue())) {
            // 已完成 -【打印】
            return buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 质检会签单保存效验
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要提交的质检会签单"}
     */
    public void checkQty(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 判断 待质检数量 >= 合格数量+不合格数量
        List<String> errorRidList = new ArrayList<>();
        // 如果为子单 不允许二次拆单
        List<String> canSubmitRidList = new ArrayList<>();
        headDTO.getItemList().forEach(p -> {
            if (p.getArrivalQty().compareTo(p.getQty().add(p.getUnqualifiedQty())) < 0) {
                errorRidList.add(p.getRid());
            }
            if (EnumRealYn.TRUE.getIntValue().equals(headDTO.getIsSonReceipt()) && p.getArrivalQty().compareTo(p.getQty().add(p.getUnqualifiedQty())) != 0) {
                canSubmitRidList.add(p.getRid());
            }
        });
        if(UtilCollection.isNotEmpty(errorRidList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ARRIVAL_MAX_ORDER_QTR, errorRidList.toString());
        }
        if(UtilCollection.isNotEmpty(canSubmitRidList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INSPECT_RECEIPT_CAN_NOT_SUBMIT, canSubmitRidList.toString());
        }

    }

    /**
     * 提交质检会签单
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要提交的质检会签单"}
     * @out ctx 出参 {"receiptCode" : "分配质检单单号"}
     */
    public void submitLoseRegister(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 设置提交时间
        po.setSubmitTime(UtilDate.getNow());

        // 设置提交操作信息
        po.setSubmitUserId(ctx.getCurrentUserId());

        // 保存质检会签单
        inspectCommonComponent.saveInspect(ctx);
    }

    /**
     * 质检会签拆单
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要提交的质检会签单"}
     */
    public void splitSignInspect(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 单据为子单时发起审批
        if(headDTO.getIsSonReceipt().equals(EnumRealYn.TRUE.getIntValue())) {
            // 发起审批
            //this.startWorkFlow(ctx);
            // 模拟审批回调
            BizApprovalReceiptInstanceRelDTO wfReceiptCo = new BizApprovalReceiptInstanceRelDTO();
            wfReceiptCo.setReceiptHeadId(headDTO.getId());
            wfReceiptCo.setApproveStatus(EnumApprovalStatus.FINISH.getValue());
            wfReceiptCo.setInitiator(ctx.getCurrentUser());
            this.approvalCallback(wfReceiptCo);
        }
        // 单据为母单时拆单
        else {
            // 母单已完成行项目列表
            List<BizReceiptInspectItemDTO> itemDTOList = new ArrayList<>(headDTO.getItemList());
            // 拆单-抬头处理
            BizReceiptInspectHeadDTO childHeadDTO = UtilBean.newInstance(headDTO, BizReceiptInspectHeadDTO.class);
            childHeadDTO.setId(null).setIsSonReceipt(EnumRealYn.TRUE.getIntValue());
            // 拆单-行项目处理
            List<BizReceiptInspectItemDTO> childItemList = new ArrayList<>();
            for(BizReceiptInspectItemDTO itemDTO : itemDTOList) {
                // 合格数量/不合格数量/未到货数量有任意一个大于0 便进行拆单
                BigDecimal allQty = itemDTO.getQty().add(itemDTO.getUnqualifiedQty()).add(itemDTO.getUnarrivalQty());
                if(allQty.compareTo(BigDecimal.ZERO) > 0) {
                    BizReceiptInspectItemDTO childItemDTO = UtilBean.newInstance(itemDTO, BizReceiptInspectItemDTO.class);
                    childItemDTO.setArrivalQty(allQty);
                    childItemList.add(childItemDTO);
                }
                // 若存在行项目待质检数量等于0 行项目为已完成
                if(itemDTO.getArrivalQty().compareTo(BigDecimal.ZERO) == 0){
                    itemDTOList.add(itemDTO);
                }
            }
            // 更新母单是否已完成  如果完成不清空行项目数量和签字信息  不进行拆单
            if(itemDTOList.size() == itemDTOList.size()) {
                // 设置母单提交时间
                headDTO.setSubmitTime(UtilDate.getNow());
                bizReceiptInspectHeadDataWrap.updateDtoById(headDTO);
                inspectCommonComponent.updateStatusAndDelete(headDTO, itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue(), false);
                // 模拟审批回调
                BizApprovalReceiptInstanceRelDTO wfReceiptCo = new BizApprovalReceiptInstanceRelDTO();
                wfReceiptCo.setReceiptHeadId(headDTO.getId());
                wfReceiptCo.setApproveStatus(EnumApprovalStatus.FINISH.getValue());
                wfReceiptCo.setInitiator(ctx.getCurrentUser());
                this.approvalCallback(wfReceiptCo);
            }else {
                // 清空母单行项目数量
                UpdateWrapper<BizReceiptInspectItem> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(BizReceiptInspectItem::getHeadId, headDTO.getId())
                        .set(BizReceiptInspectItem::getQty, BigDecimal.ZERO)
                        .set(BizReceiptInspectItem::getUnqualifiedQty, BigDecimal.ZERO)
                        .set(BizReceiptInspectItem::getUnarrivalQty, BigDecimal.ZERO);
                bizReceiptInspectItemDataWrap.update(updateWrapper);

                // 清空母单会签签字信息（该部分信息转移到子单记录）
                UpdateWrapper<BizReceiptInspectUser> uw = new UpdateWrapper<>();
                uw.lambda().eq(BizReceiptInspectUser::getHeadId, headDTO.getId());
                bizReceiptInspectUserDataWrap.physicalDelete(uw);

                inspectCommonComponent.updateStatus(null, itemDTOList, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());

                if(UtilCollection.isNotEmpty(childItemList)) {
                    // 清空母单行项目上的图片和附件
                    inspectCommonComponent.resetImageAndAttachment(childItemList);
                    childHeadDTO.setItemList(childItemList);
                    // 设置子单提交时间
                    childHeadDTO.setSubmitTime(UtilDate.getNow());
                    childHeadDTO.setMainCreateTime(headDTO.getCreateTime());//设置母单创建时间
                    // 封装上下文
                    BizContext ctxInspect = new BizContext();
                    ctxInspect.setCurrentUser(ctx.getCurrentUser());
                    ctxInspect.setContextData(Const.BIZ_CONTEXT_KEY_PO, childHeadDTO);
                    // 保存质检会签单
                    inspectCommonComponent.saveInspect(ctxInspect);
                    // 保存附件
                    inspectCommonComponent.saveBizReceiptAttachment(ctxInspect);
                    // 保存操作日志-创建
                    inspectCommonComponent.saveBizReceiptOperationLog(ctxInspect);
                    // 设置上下文单据日志 - 提交
                    ctxInspect.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
                    // 保存操作日志-提交
                    inspectCommonComponent.saveBizReceiptOperationLog(ctxInspect);
                    // 保存批次图片
                    inspectCommonComponent.saveBizBatchImg(ctxInspect);
                    // 发起审批
                    //this.startWorkFlow(ctxInspect);
                    // 模拟审批回调
                    BizApprovalReceiptInstanceRelDTO wfReceiptCo = new BizApprovalReceiptInstanceRelDTO();
                    wfReceiptCo.setReceiptHeadId(childHeadDTO.getId());
                    wfReceiptCo.setApproveStatus(EnumApprovalStatus.FINISH.getValue());
                    wfReceiptCo.setInitiator(ctx.getCurrentUser());
                    this.approvalCallback(wfReceiptCo);
                }
            }

        }
    }


    /**
     * 质检会签拆单
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要提交的质检会签单"}
     */
    public void splitSignInspectNew(BizContext ctx) {
        BizReceiptInspectHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 模拟审批回调
        BizApprovalReceiptInstanceRelDTO wfReceiptCo = new BizApprovalReceiptInstanceRelDTO();
        wfReceiptCo.setReceiptHeadId(headDTO.getId());
        wfReceiptCo.setApproveStatus(EnumApprovalStatus.FINISH.getValue());
        wfReceiptCo.setInitiator(ctx.getCurrentUser());
        this.approvalCallback(wfReceiptCo);
    }

    /**
     * 发起审批
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检会签单"}
     */
    public void startWorkFlow(BizContext ctx) {
        // 发起流程审批
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        Integer receiptType = po.getReceiptType();
        // 会签人处理
        List<String> approveList = Arrays.asList(po.getInspectUserCode().split(","));
        Map<String, Object> variables = new HashMap<>();
        Long ftyId=po.getItemList().get(0).getFtyId();
        variables.put("ftyId", ftyId);
        variables.put("approveUserList", approveList);
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 更新质检会签单 - 审批中
        inspectCommonComponent.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        // 获取质检会签单信息
        BizReceiptInspectHeadDTO headDTO = UtilBean.newInstance(bizReceiptInspectHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId()), BizReceiptInspectHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(headDTO);
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {
            // 封装上下文
            BizContext ctx = new BizContext();
            ctx.setCurrentUser(wfReceiptCo.getInitiator());
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            // 更新单据已完成
            inspectCommonComponent.updateStatusCompleted(ctx);
            // 生成后续单据   
            this.genNextReceipt(ctx);
        } else {
            // 单据状态已驳回
            inspectCommonComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
        }
    }

    /**
     * 质检会签通过后，生成下一流程的对应单据
     * @param ctx
     * <AUTHOR> <<EMAIL>>
     */
    public void genNextReceipt(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            log.warn("注意：质检会签生成下一级单据流程，入参po(BizReceiptInspectHeadDTO)为null，未生成下一级单据.");
            return;
        }
        if (EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue().equals(po.getReceiptType())) {
            // 当前单据是采购的质检会签单，应生成验收入库单（不合格项需要手工创建"不合格项通知"执行后续流程）
            this.genInspectInput(ctx);
            this.genInconformityNotice(ctx);
        } else if (EnumReceiptType.SIGN_INSPECTION_RETURN.getValue().equals(po.getReceiptType())) {
            // 当前单据是退库的质检会签单，应生成退库单
            this.genReturn(ctx);
            // 退库质检会签后，如果有不合格项，需要生成草稿状态不合格处置单
            this.genMatReqReturnInconformity(ctx);
        } else if (EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN.getValue().equals(po.getReceiptType())) {
            // 当前单据是成套设备退库的质检会签单，应生成退库单
            this.genUnitizedReturn(ctx);
            // 成套设备退库质检会签后，如果有不合格项，需要生成草稿状态不合格处置单
            this.genUnitizedMatReqReturnInconformity(ctx);
        }else if (EnumReceiptType.SIGN_INSPECTION_RETURN_TRANSFER.getValue().equals(po.getReceiptType()) || EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN_TRANSFER.getValue().equals(po.getReceiptType())) {
            // 当前单据是退转库的质检会签单，应生成退转库单
            this.genReturnTransfer(ctx);
            // 退转库质检会签后，如果有不合格项，需要生成草稿状态不合格处置单
            this.genTransferReturnInconformity(ctx);
        } else {
            // 其他类型视为单据类型错误，中断流程
            log.error("质检会签生成下一级单据发生单据类型取值错误，当前的单据类型是：{}，不在有效的质检会签单据类型范围内", po.getReceiptType());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_FALSE);
        }

    }


    /**
     * 生成差异通知
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检会签单"}
     */
    private void genInconformityNotice(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            return;
        }
        log.debug("基于质检会签单{}, receiptId={},组装参数，生成差异通知", po.getReceiptCode(), po.getId());
        // 装载生成差异通知head
        BizReceiptInconformityHeadDTO qualityInconformityHeadDTO = new BizReceiptInconformityHeadDTO();

        /* ******** 入库单head设置 ******** */
        qualityInconformityHeadDTO.setReceiptType(EnumReceiptType.INCONFORMITY_NOTICE.getValue())
                .setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe())
                .setReceiveDate(po.getReceiveDate())
                .setDepositPoint(po.getDepositPoint())
                .setContractId(po.getContractId())
                .setBatchCode(po.getBatchCode())
                .setPurchaseCode(po.getPurchaseCode())
                .setSendType(po.getSendType())
                .setDifferentType(EnumDifferentType.QUALITY_DIFF.getValue());
        ;

        BizReceiptInconformityHeadDTO numberInconformityHeadDTO = UtilBean.newInstance(qualityInconformityHeadDTO,BizReceiptInconformityHeadDTO.class);
        numberInconformityHeadDTO.setDifferentType(EnumDifferentType.NUMBER_DIFF.getValue());

        /* ******** 质量差异 ******** */
        // 质量差异
        List<BizReceiptInconformityItemDTO> qualityInconformityItemList = new ArrayList<>();
        // 数量差异
        List<BizReceiptInconformityItemDTO> numberInconformityItemList = new ArrayList<>();

        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            // 不合格的自动生成质量差异单
            if(itemDTO.getUnqualifiedQty().compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItem = new BizReceiptInconformityItemDTO();
                inconformityItem = UtilBean.newInstance(itemDTO, inconformityItem.getClass());

                inconformityItem.setId(null);
                inconformityItem.setQty(itemDTO.getUnqualifiedQty());
                inconformityItem.setHeadId(null);
                inconformityItem.setPreReceiptHeadId(po.getId());
                inconformityItem.setPreReceiptItemId(itemDTO.getId());
                inconformityItem.setPreReceiptType(po.getReceiptType());
                inconformityItem.setIsSplit(0);
                inconformityItem.setMatDocCode(null);
                inconformityItem.setMatDocRid(null);
                inconformityItem.setMatDocYear(null);
                inconformityItem.setPostingDate(null);
                inconformityItem.setDocDate(null);
                inconformityItem.setIsPost(null);

                qualityInconformityItemList.add(inconformityItem);
            }

            // 未到货的自动生成数量差异单
            if(itemDTO.getUnarrivalQty().compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItem = new BizReceiptInconformityItemDTO();
                inconformityItem = UtilBean.newInstance(itemDTO, inconformityItem.getClass());

                inconformityItem.setId(null);
                inconformityItem.setQty(itemDTO.getUnarrivalQty());
                inconformityItem.setHeadId(null);
                inconformityItem.setPreReceiptHeadId(po.getId());
                inconformityItem.setPreReceiptItemId(itemDTO.getId());
                inconformityItem.setPreReceiptType(po.getReceiptType());
                inconformityItem.setIsSplit(0);
                inconformityItem.setMatDocCode(null);
                inconformityItem.setMatDocRid(null);
                inconformityItem.setMatDocYear(null);
                inconformityItem.setPostingDate(null);
                inconformityItem.setDocDate(null);
                inconformityItem.setIsPost(null);

                numberInconformityItemList.add(inconformityItem);
            }
        }
        if(UtilCollection.isNotEmpty(qualityInconformityItemList)) {
            qualityInconformityHeadDTO.setItemList(qualityInconformityItemList);
            // 设置入参上下文
            BizContext ctxInput = new BizContext();
            ctxInput.setContextData(Const.BIZ_CONTEXT_KEY_PO, qualityInconformityHeadDTO);
            ctxInput.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成 生成差异通知
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.GEN_INCONFORMITY_NOTICE, ctxInput);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
        if(UtilCollection.isNotEmpty(numberInconformityItemList)) {
            numberInconformityHeadDTO.setItemList(numberInconformityItemList);
            // 设置入参上下文
            BizContext ctxInput = new BizContext();
            ctxInput.setContextData(Const.BIZ_CONTEXT_KEY_PO, numberInconformityHeadDTO);
            ctxInput.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成 生成差异通知
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.GEN_INCONFORMITY_NOTICE, ctxInput);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "质检会签单"}
     */
    private void genInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (null == po) {
            return;
        }
        log.debug("基于质检会签单{}, receiptId={},组装参数，生成验收入库单", po.getReceiptCode(), po.getId());
        // 装载验收入库单head
        BizReceiptInputHeadDTO inspectInputHead = new BizReceiptInputHeadDTO();
        // 装载验收入库单item
        List<BizReceiptInputItemDTO> inspectInputItemList = new ArrayList<>(po.getItemList().size());
        /* ******** 入库单head设置 ******** */
        inspectInputHead.setReceiptType(EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                        .setDeliveryNoticeDescribe(po.getDeliveryNoticeDescribe())
                        .setPurchaseCode(po.getPurchaseCode())
                        .setContractId(po.getContractId())
                        .setSendType(po.getSendType())
                .setVirtualOutputApplyReceiptCode(po.getVirtualOutputApplyReceiptCode());
        /* ******** 入库单item设置 ******** */

        // 获取合同信息
        List<BizReceiptContractHead> contractHeadList = contractHeadDataWrap.listByIds(po.getItemList().stream().map(e->e.getReferReceiptHeadId()).distinct().collect(Collectors.toList()));
        List<BizReceiptContractHeadDTO> contractHeadDTOList = UtilCollection.toList(contractHeadList,BizReceiptContractHeadDTO.class);
        dataFillService.fillRlatAttrDataList(contractHeadDTOList);
        Map<Long,BizReceiptContractHeadDTO> contractHeadDTOMap = new HashMap<>();

        if(UtilCollection.isNotEmpty(contractHeadDTOList)){
            contractHeadDTOMap = contractHeadDTOList.stream().collect(Collectors.toMap(e->e.getId(),e->e,(k1,k2)->k2));
        }

        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            // 有合格数量生成验收入库单
            if(itemDTO.getQty().compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInputItemDTO inspectInputItem = new BizReceiptInputItemDTO();
                inspectInputItem = UtilBean.newInstance(itemDTO, inspectInputItem.getClass());
                inspectInputItem.setId(null);
                inspectInputItem.setHeadId(null);
                inspectInputItem.setPreReceiptHeadId(po.getId());
                inspectInputItem.setPreReceiptItemId(itemDTO.getId());
                inspectInputItem.setPreReceiptType(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue());
                inspectInputItem.setPreMatDocCode(itemDTO.getMatDocCode());
                inspectInputItem.setPreMatDocRid(itemDTO.getMatDocRid());
                inspectInputItem.setPreMatDocYear(itemDTO.getMatDocYear());
                inspectInputItem.setMatDocCode(null);
                inspectInputItem.setMatDocRid(null);
                inspectInputItem.setMatDocYear(null);
                inspectInputItem.setPostingDate(null);
                inspectInputItem.setDocDate(null);
                inspectInputItem.setIsPost(null);
                inspectInputItem.setTaxAmount(inspectInputItem.getTaxPrice().multiply(inspectInputItem.getQty()));
                inspectInputItem.setNoTaxAmount(inspectInputItem.getNoTaxPrice().multiply(inspectInputItem.getQty()));
                if (UtilCollection.isNotEmpty(inspectInputItem.getBizBatchImgDTOList())) {
                    // [30433]【质检会签】打开单据草稿，上传多条附件信息，提交后，未生成验收入库。
                    for (BizBatchImgDTO batchImgDTO : inspectInputItem.getBizBatchImgDTOList()) {
                        batchImgDTO.setImgBase64(null);
                    }
                }
                /* ******** 设置批次信息 ******** */

                BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                batchInfoDTO = UtilBean.newInstance(inspectInputItem, batchInfoDTO.getClass());
                batchInfoDTO.setId(null);
                batchInfoDTO.setPurchaseReceiptHeadId(inspectInputItem.getReferReceiptHeadId());
                batchInfoDTO.setPurchaseReceiptItemId(inspectInputItem.getReferReceiptItemId());
                batchInfoDTO.setPurchaseReceiptRid(inspectInputItem.getReferReceiptRid());
                batchInfoDTO.setPurchaseReceiptCode(inspectInputItem.getReferReceiptCode());
                batchInfoDTO.setProductionDate(itemDTO.getProductDate());
                batchInfoDTO.setCounterSignRemark(itemDTO.getItemRemark());
                batchInfoDTO.setMainRequirement(itemDTO.getMainRequirement());
                batchInfoDTO.setIsSafe(po.getIsSafe());

                BizReceiptContractHeadDTO contractHeadDTO = contractHeadDTOMap.get(itemDTO.getReferReceiptHeadId());



                batchInfoDTO.setPurchaseCode(itemDTO.getPurchaseCode());
                batchInfoDTO.setPurchaseRid(itemDTO.getPurchaseRid());
                batchInfoDTO.setDemandPlanRid(itemDTO.getDemandPlanRid());
                batchInfoDTO.setDemandPlanCode(itemDTO.getDemandPlanCode());
                batchInfoDTO.setDemandDept(itemDTO.getDemandDept());
                batchInfoDTO.setDemandUserName(itemDTO.getDemandPerson());
                batchInfoDTO.setContractId(itemDTO.getReferReceiptHeadId());
                batchInfoDTO.setHeadContractCode(po.getContractCode());
                batchInfoDTO.setHeadContractName(po.getContractName());
                batchInfoDTO.setHeadSupplierCode(po.getSupplierCode());
                batchInfoDTO.setHeadSupplierName(po.getSupplierName());
                batchInfoDTO.setHeadPaymentMethod(po.getPaymentMethod());
                if(contractHeadDTO!=null){
                    batchInfoDTO.setContractCode(contractHeadDTO.getReceiptCode());
                    batchInfoDTO.setContractName(contractHeadDTO.getContractName());
                    batchInfoDTO.setSupplierCode(contractHeadDTO.getSupplierCode());
                    batchInfoDTO.setSupplierName(contractHeadDTO.getSupplierName());
                }
                if(EnumSendType.OIL_PROCUREMENT.getValue().equals(inspectInputHead.getSendType())){
                    batchInfoDTO.setPrice(itemDTO.getNoTaxPrice());
                }else{
                    batchInfoDTO.setPrice(itemDTO.getPoNoTaxPrice());
                }
                batchInfoDTO.setCurrency(itemDTO.getCurrency());
                batchInfoDTO.setDeliveryNoticeBatchCode(po.getBatchCode());
                inspectInputItem.setBizBatchInfoDTO(batchInfoDTO);

                inspectInputItemList.add(inspectInputItem);
            }
        }
        if(UtilCollection.isNotEmpty(inspectInputItemList)) {
            inspectInputHead.setItemList(inspectInputItemList);
            // 设置入参上下文
            BizContext ctxInput = new BizContext();
            ctxInput.setContextData(Const.BIZ_CONTEXT_KEY_PO, inspectInputHead);
            ctxInput.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成验收入库单
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.GEN_INSPECT_INPUT_STOCK, ctxInput);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成退库单
     * 当退库质检会签单提交通过会签后，生成退库单
     * 该方法迁移自{@link MatReqReturnInspectComponent#genMatReqReturn(BizContext)}
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "退库质检单"}
     */
    private void genReturn(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 组装参数
        log.debug("基于退库质检会签单{}, receiptId={},组装参数，生成退库单", po.getReceiptCode(), po.getId());
        BizReceiptReturnHeadDTO headDTO = new BizReceiptReturnHeadDTO();
        List<BizReceiptReturnItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue()).setRemark(po.getRemark());
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            if(itemDTO.getQty().compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptReturnItemDTO returnItemDTO = UtilBean.newInstance(itemDTO, BizReceiptReturnItemDTO.class);
                returnItemDTO.setPreReceiptHeadId(po.getId());
                returnItemDTO.setPreReceiptItemId(itemDTO.getId());
                returnItemDTO.setPreReceiptType(po.getReceiptType());
                returnItemDTO.setQualifiedQty(itemDTO.getQty());
                returnItemDTO.setQty(BigDecimal.ZERO);
                itemDTOList.add(returnItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            headDTO.setItemDTOList(itemDTOList);
            // 设置入参上下文
            BizContext ctxReturn = new BizContext();
            ctxReturn.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            ctxReturn.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成领料退库单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_MAT_REQ_RETURN_STOCK, ctxReturn);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成成套设备退库单
     * 当成套设备退库质检会签单提交通过会签后，生成成套设备退库单
     * 该方法迁移自{@link MatReqReturnInspectComponent#genMatReqReturn(BizContext)}
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "成套设备退库质检单"}
     */
    private void genUnitizedReturn(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 组装参数
        log.debug("基于成套设备退库质检会签单{}, receiptId={},组装参数，生成退库单", po.getReceiptCode(), po.getId());
        BizReceiptReturnHeadDTO headDTO = new BizReceiptReturnHeadDTO();
        List<BizReceiptReturnItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ.getValue()).setRemark(po.getRemark());
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            if(itemDTO.getQty().compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptReturnItemDTO returnItemDTO = UtilBean.newInstance(itemDTO, BizReceiptReturnItemDTO.class);
                returnItemDTO.setPreReceiptHeadId(po.getId());
                returnItemDTO.setPreReceiptItemId(itemDTO.getId());
                returnItemDTO.setPreReceiptType(po.getReceiptType());
                returnItemDTO.setQualifiedQty(itemDTO.getQty());
                returnItemDTO.setQty(BigDecimal.ZERO);
                itemDTOList.add(returnItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            headDTO.setBackTransport(po.getBackTransport());
            headDTO.setQualified(1); //合格
            headDTO.setItemDTOList(itemDTOList);
            // 设置入参上下文
            BizContext ctxReturn = new BizContext();
            ctxReturn.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            ctxReturn.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成领料退库单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_MAT_REQ_RETURN_STOCK, ctxReturn);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成退库不合格项维护单
     * 该方法迁移自{@link MatReqReturnInspectComponent#genMatReqReturnInconformity(BizContext)}
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "退库质检单"}
     */
    private void genMatReqReturnInconformity(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装参数
        log.debug("基于退库质检会签单{}, receiptId={},组装参数，生成不合格项处置单", po.getReceiptCode(), po.getId());
        BizReceiptInconformityHeadDTO headDTO = new BizReceiptInconformityHeadDTO();
        List<BizReceiptInconformityItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.STOCK_RETURN_MAT_REQ_INCONFORMITY.getValue()).setRemark(po.getRemark());
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            if(itemDTO.getUnqualifiedQty().compareTo(BigDecimal.ZERO) > 0||itemDTO.getUnarrivalQty().compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItemDTO = UtilBean.newInstance(itemDTO, BizReceiptInconformityItemDTO.class);
                inconformityItemDTO.setPreReceiptHeadId(po.getId());
                inconformityItemDTO.setPreReceiptItemId(itemDTO.getId());
                inconformityItemDTO.setPreReceiptType(po.getReceiptType());
                inconformityItemDTO.setQty(itemDTO.getUnqualifiedQty());
                inconformityItemDTO.setUnarrivalQty(itemDTO.getUnarrivalQty());
                inconformityItemDTO.setIsSplit(0);
                itemDTOList.add(inconformityItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            headDTO.setItemList(itemDTOList);
            headDTO.setDes(po.getDes());
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成退库不合格项维护单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_MAT_REQ_RETURN_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成成套设备退库不合格项维护单
     * 该方法迁移自{@link MatReqReturnInspectComponent#genMatReqReturnInconformity(BizContext)}
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "成套设备退库质检单"}
     */
    private void genUnitizedMatReqReturnInconformity(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装参数
        log.debug("基于成套设备退库质检会签单{}, receiptId={},组装参数，生成不合格项处置单", po.getReceiptCode(), po.getId());
        BizReceiptInconformityHeadDTO headDTO = new BizReceiptInconformityHeadDTO();
        List<BizReceiptInconformityItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_RETURN_MAT_REQ_INCONFORMITY.getValue())
                .setRemark(po.getRemark());
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            if(itemDTO.getUnqualifiedQty().compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItemDTO = UtilBean.newInstance(itemDTO, BizReceiptInconformityItemDTO.class);
                inconformityItemDTO.setPreReceiptHeadId(po.getId());
                inconformityItemDTO.setPreReceiptItemId(itemDTO.getId());
                inconformityItemDTO.setPreReceiptType(po.getReceiptType());
                inconformityItemDTO.setQty(itemDTO.getUnqualifiedQty());
                inconformityItemDTO.setIsSplit(EnumRealYn.FALSE.getIntValue());
                itemDTOList.add(inconformityItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            headDTO.setItemList(itemDTOList);
            headDTO.setDes(po.getDes());
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成退库不合格项维护单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_UNITIZED_MAT_REQ_RETURN_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }


    /**
     * 生成退转库单
     * 当退转库质检会签单提交通过会签后，生成退转库单
     * 该方法迁移自{@link TransferReturnInspectComponent#genTransferReturn(BizContext)}
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "退转库质检单"}
     */
    private void genReturnTransfer(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 组装参数
        log.debug("基于退转库质检会签单{}, receiptId={},组装参数，生成退转库单", po.getReceiptCode(), po.getId());
        BizReceiptReturnHeadDTO headDTO = new BizReceiptReturnHeadDTO();
        List<BizReceiptReturnItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.STOCK_RETURN_TRANSFER.getValue()).setRemark(po.getRemark());
        if (EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN_TRANSFER.getValue().equals(po.getReceiptType())) {
            headDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_RETURN_TRANSFER.getValue());
        }
        // 批次信息
        List<BizBatchInfoDTO> batchInfoDTOList = new ArrayList<>();
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            if(itemDTO.getQty().compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptReturnItemDTO returnItemDTO = UtilBean.newInstance(itemDTO, BizReceiptReturnItemDTO.class);
                returnItemDTO.setPreReceiptHeadId(po.getId());
                returnItemDTO.setPreReceiptItemId(itemDTO.getId());
                returnItemDTO.setPreReceiptType(po.getReceiptType());
                returnItemDTO.setQualifiedQty(itemDTO.getQty());
                returnItemDTO.setQty(BigDecimal.ZERO);
                // bin表处理
                List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
                BizReceiptReturnBinDTO binDTO = new BizReceiptReturnBinDTO();
                // 创建批次
                BizBatchInfoDTO batchInfoDTO = new BizBatchInfoDTO();
                batchInfoDTO.setFtyId(returnItemDTO.getFtyId());
                batchInfoDTO.setMatId(returnItemDTO.getMatId());
                batchInfoDTO.setProductionDate(itemDTO.getProductDate());
                batchInfoDTOList.add(batchInfoDTO);
                binDTO.setBatchInfo(batchInfoDTO);
                DicMaterialFactoryDTO dicMaterialFactoryDTO = dictionaryService.getDicMaterialFactoryByUniqueKey(returnItemDTO.getMatId(), returnItemDTO.getFtyId());
                if(returnItemDTO.getProductDate()!=null && UtilObject.isNotNull(dicMaterialFactoryDTO)){
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(returnItemDTO.getProductDate());
                    calendar.add(Calendar.MONTH, dicMaterialFactoryDTO.getShelfLifeMax());
                    binDTO.setArrivalDate(calendar.getTime());
                }
                binDTOList.add(binDTO);
                returnItemDTO.setItemInfoList(binDTOList);
                itemDTOList.add(returnItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            // 保存批次
            batchInfoService.multiCheckUKSaveBatchInfo(batchInfoDTOList);
            // 回填批次id
            itemDTOList.forEach(p -> {
                batchInfoDTOList.forEach(q -> {
                    if(p.getFtyId().equals(q.getFtyId()) && p.getMatId().equals(q.getMatId())) {
                        p.getItemInfoList().forEach(m -> m.setBatchId(q.getId()));
                    }
                });
            });
            headDTO.setItemDTOList(itemDTOList);
            // 设置入参上下文
            BizContext ctxReturn = new BizContext();
            ctxReturn.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            ctxReturn.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成领料退转库单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_TRANSFER_RETURN_STOCK, ctxReturn);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 生成退转库不合格项维护单
     * 该方法迁移自{@link TransferReturnInspectComponent#genTransferReturnInconformity(BizContext)}
     *
     * @in ctx 入参 {@link BizReceiptInspectHeadDTO : "退转库质检单"}
     */
    private void genTransferReturnInconformity(BizContext ctx) {
        // 入参上下文
        BizReceiptInspectHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 组装参数
        log.debug("基于退转库质检会签单{}, receiptId={},组装参数，生成不合格项处置单", po.getReceiptCode(), po.getId());
        BizReceiptInconformityHeadDTO headDTO = new BizReceiptInconformityHeadDTO();
        List<BizReceiptInconformityItemDTO> itemDTOList = new ArrayList<>();
        headDTO.setReceiptType(EnumReceiptType.STOCK_RETURN_TRANSFER_INCONFORMITY.getValue()).setRemark(po.getRemark());
        if (EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN_TRANSFER.getValue().equals(po.getReceiptType())) {
            headDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_RETURN_TRANSFER_INCONFORMITY.getValue());
        }
        headDTO.setPurchaserManagerName(po.getPurchaserManagerName());
        for (BizReceiptInspectItemDTO itemDTO : po.getItemList()) {
            if(itemDTO.getUnqualifiedQty().compareTo(BigDecimal.ZERO) > 0) {
                BizReceiptInconformityItemDTO inconformityItemDTO = UtilBean.newInstance(itemDTO, BizReceiptInconformityItemDTO.class);
                inconformityItemDTO.setPreReceiptHeadId(po.getId());
                inconformityItemDTO.setPreReceiptItemId(itemDTO.getId());
                inconformityItemDTO.setPreReceiptType(po.getReceiptType());
                inconformityItemDTO.setQty(itemDTO.getUnqualifiedQty());
                inconformityItemDTO.setIsSplit(EnumRealYn.FALSE.getIntValue());
                itemDTOList.add(inconformityItemDTO);
            }
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            headDTO.setItemList(itemDTOList);
            // 设置入参上下文
            BizContext ctxInconformity = new BizContext();
            ctxInconformity.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            ctxInconformity.setCurrentUser(ctx.getCurrentUser());
            // 推送MQ生成退转库不合格项维护单
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GEN_TRANSFER_RETURN_INCONFORMITY_MAINTAIN_STOCK, ctxInconformity);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 获取退库&退转库的前序申请单单号
     * @param receiptId
     * @param receiptType
     * @return
     */
    public String getReceiptApplyCode(Long receiptId, Integer receiptType) {
        if (EnumReceiptType.SIGN_INSPECTION_RETURN.getValue().equals(receiptType)
            || EnumReceiptType.UNITIZED_SIGN_INSPECTION_RETURN.getValue().equals(receiptType)
            || EnumReceiptType.SIGN_INSPECTION_RETURN_TRANSFER.getValue().equals(receiptType)) {
            // 如果当前单据是退库质检会签单或着退转库质检会签单，则通过前序单据向前查找申请单单号
            return this.getReceiptApplyCode(receiptId);
        }
        return null;
    }

    /**
     * 获取退库&退转库的前序申请单单号
     * @param receiptId
     * @return
     */
    private String getReceiptApplyCode(Long receiptId) {
        BizReceiptInspectHead head = bizReceiptInspectHeadDataWrap.getById(receiptId);
        if (UtilObject.isNotEmpty(head)) {

            BizReceiptInspectItem signInspectionReceipt = bizReceiptInspectItemDataWrap.getOne(new QueryWrapper<BizReceiptInspectItem>()
                    .lambda()
                    .eq(BizReceiptInspectItem::getHeadId, receiptId)
                    .last("limit 1"));

            BizReceiptInspectItem distributeSignReceipt = bizReceiptInspectItemDataWrap.getOne(new QueryWrapper<BizReceiptInspectItem>()
                    .lambda()
                    .eq(BizReceiptInspectItem::getHeadId, signInspectionReceipt.getPreReceiptHeadId())
                    .last("limit 1"));

            BizReceiptApplyHead applyReceipt = bizReceiptApplyHeadDataWrap.getById(distributeSignReceipt.getPreReceiptHeadId());
            log.debug("质检单获取前序申请单过程，通过参数receiptId:{} 得到申请单单据号", applyReceipt.getReceiptCode());
            return applyReceipt.getReceiptCode();
        }
        log.warn("质检单获取前序申请单过程，未能通过参数receiptId:{} 得到申请单单据号", receiptId);
        return null;
    }

}
