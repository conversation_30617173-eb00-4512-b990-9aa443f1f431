package com.inossem.wms.bizdomain.inspect.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.erp.po.PreReceiptQueryPO;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 验收公用API
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-05-28
 */
@Service
public class InspectCommonApi {

    @Autowired
    private BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    @Autowired
    private BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;

    @Autowired
    private DataFillService dataFillService;

    /**
     * (采购退货调用)查询采购验收单
     *
     * @param ctx
     */
    public void getPurchaseInspectItemList(BizContext ctx) {


        PreReceiptQueryPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Date startTime = null;
        Date endTime = null;
        if (Objects.nonNull(po.getStartTime()) && Objects.nonNull(po.getEndTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getStartTime());
            endTime = UtilLocalDateTime.getEndTime(po.getEndTime());
        }
        // 符合条件的验收单head
        QueryWrapper<BizReceiptInspectHead> headQueryWrapper = new QueryWrapper<>();
        headQueryWrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getPurchaseReceiptCode()), BizReceiptInspectHead::getReceiptCode, po.getPurchaseReceiptCode())
                .between((Objects.nonNull(startTime)), BizReceiptInspectHead::getCreateTime, startTime, endTime)
                .eq(BizReceiptInspectHead::getIsDelete, EnumRealYn.FALSE.getIntValue())
                .eq(Objects.nonNull(po.getPreReceiptType()),BizReceiptInspectHead::getReceiptType, po.getPreReceiptType())
                .eq(BizReceiptInspectHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        List<BizReceiptInspectHead> headList = bizReceiptInspectHeadDataWrap.list(headQueryWrapper);
        // 符合条件的验收单item
        List<BizReceiptInspectItemDTO> itemDTOList = new ArrayList<>();
        if (UtilCollection.isNotEmpty(headList)){
            QueryWrapper<BizReceiptInspectItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().in(BizReceiptInspectItem::getHeadId, headList.stream().map(BizReceiptInspectHead::getId).collect(Collectors.toList()))
                    .eq(BizReceiptInspectItem::getIsDelete, EnumRealYn.FALSE.getIntValue())
                    .eq(BizReceiptInspectItem::getIsWriteOff, EnumRealYn.FALSE.getIntValue());
            List<BizReceiptInspectItem> purchaseInspectItemList = bizReceiptInspectItemDataWrap.list(itemQueryWrapper);
            itemDTOList = UtilCollection.toList(purchaseInspectItemList, BizReceiptInspectItemDTO.class);
            dataFillService.fillAttr(itemDTOList);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, itemDTOList);
    }
}
