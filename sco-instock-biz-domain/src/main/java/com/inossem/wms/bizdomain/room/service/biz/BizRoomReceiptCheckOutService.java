package com.inossem.wms.bizdomain.room.service.biz;

import com.inossem.wms.bizdomain.room.service.component.BizRoomReceiptCheckOutComponent;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptCheckOutHeadDTO;
import com.inossem.wms.common.model.bizdomain.room.dto.BizRoomReceiptCheckOutRoomDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.util.UtilCollection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/04/20
 *
 * 住房退订服务类
 *
 **/
@Slf4j
@Service
public class BizRoomReceiptCheckOutService {

    @Autowired
    private BizRoomReceiptCheckOutComponent bizRoomReceiptCheckOutComponent;

    /**
     * 页面初始化
     */
    public void init(BizContext ctx) {
        bizRoomReceiptCheckOutComponent.init(ctx);
    }

    /**
     * 分页查询
     */
    public void getPage(BizContext ctx) {
        // 分页查询
        bizRoomReceiptCheckOutComponent.getPage(ctx);
    }

    /**
     * 获取详情数据
     */
    public void getInfo(BizContext ctx) {
        bizRoomReceiptCheckOutComponent.getInfo(ctx);
    }

    /**
     * 根据退订人员信息获取需要退订的房间
     */
    public List<BizRoomReceiptCheckOutRoomDTO> getCheckOutRoomByCheckOutUser(BizContext ctx) {
        return bizRoomReceiptCheckOutComponent.getCheckOutRoomByCheckOutUser(ctx);
    }

    /**
     * 保存单据
     */
    @Transactional
    public void saveReceipt(BizContext ctx) {
        // 保存校验
        bizRoomReceiptCheckOutComponent.checkSaveData(ctx);

        // 保存单据
        bizRoomReceiptCheckOutComponent.saveReceipt(ctx);
    }

    /**
     * 提交单据
     */
    @Transactional
    public void submitReceipt(BizContext ctx) {

        // 入参上下文
        BizRoomReceiptCheckOutHeadDTO po = ctx.getPoContextData();

        // 设置提交信息
        po.setSubmitUserId(ctx.getCurrentUserId());
        po.setSubmitTime(new Date());

        // 保存校验
        bizRoomReceiptCheckOutComponent.checkSaveData(ctx);

        // 提交校验
        bizRoomReceiptCheckOutComponent.checkSubmitData(ctx);

        // 保存单据
        bizRoomReceiptCheckOutComponent.saveReceipt(ctx);

        // 如果是草稿状态，更新房间的使用记录信息
        if(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(po.getReceiptStatus())){
            // 更新房间的使用情况
            bizRoomReceiptCheckOutComponent.updateRoomUsage(po);

            // 不需要退订房间则直接单据变为已完成，需要退订房间则更新状态为待交接
            if(UtilCollection.isEmpty(po.getRoomList())){
                // 更新状态为已完成
                bizRoomReceiptCheckOutComponent.updateStatus(po.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED);
            } else {

                // 更新状态为待交接
                bizRoomReceiptCheckOutComponent.updateStatus(po.getId(), EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDOVER);
            }
        } else if(EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDOVER.getValue().equals(po.getReceiptStatus())){

            // 房间退订
            bizRoomReceiptCheckOutComponent.checkOutRoom(ctx);

            // 更新退房交接时间
            bizRoomReceiptCheckOutComponent.updateCheckOutHandoverTime(ctx);

            // 更新状态为已完成
            bizRoomReceiptCheckOutComponent.updateStatus(po.getId(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED);
        }
    }

    /**
     * 删除单据
     */
    @Transactional
    public void deleteReceipt(BizContext ctx) {
        // 删除单据
        bizRoomReceiptCheckOutComponent.deleteReceipt(ctx);
    }

    /**
     * 自动创建住房退订单
     */
    @Transactional
    public void autoCreateReceipt() {
        // 自动创建住房退订单
        bizRoomReceiptCheckOutComponent.autoCreateReceipt();
    }

}
