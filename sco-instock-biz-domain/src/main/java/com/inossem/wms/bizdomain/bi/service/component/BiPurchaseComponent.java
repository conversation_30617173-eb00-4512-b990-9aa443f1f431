package com.inossem.wms.bizdomain.bi.service.component;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizdomain.bi.dao.BiPurchaseMapper;
import com.inossem.wms.bizdomain.bi.service.datawrap.BiPurchaseBaseDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.purchase.service.datawrap.BizReceiptPurchaseApplyHeadDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.demandplan.EnumDemandPlanType;
import com.inossem.wms.common.model.bizdomain.bi.entity.BiPurchaseBase;
import com.inossem.wms.common.model.bizdomain.bi.po.BiSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractCountVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractSumVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseContractVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseDemandPlanVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchasePurchaseVO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.purchase.entity.BizReceiptPurchaseApplyHead;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * BI-华信资源驾驶舱-采购类指标 代码组件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
@Slf4j
public class BiPurchaseComponent {

    @Autowired
    private BiPurchaseMapper biPurchaseMapper;

    @Autowired
    private BizReceiptContractHeadDataWrap contractHeadDataWrap;

    @Autowired
    private BizReceiptPurchaseApplyHeadDataWrap purchaseApplyHeadDataWrap;

    @Autowired
    private BiPurchaseBaseDataWrap biPurchaseBaseDataWrap;

    // 1万
    private final BigDecimal TEN_THOUSAND = BigDecimal.valueOf(10000);

    // 1百
    private final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);

    /**
     * BI-已完成采购任务的合同数量/金额
     */
    public void getCompletedContractSum(BizContext ctx) {
        BiSearchPO po = ctx.getPoContextData();
        po.setYear(UtilNumber.isNotEmpty(po.getYear()) ? po.getYear() : DateUtil.year(UtilDate.getNow()));

        BiCompletedContractSumVO vo = new BiCompletedContractSumVO();
        vo.setYear(po.getYear());

        // 查询已完成采购任务合同数量
        QueryWrapper<BizReceiptContractHead> contractCountQueryWrapper = new QueryWrapper<>();
        contractCountQueryWrapper.eq("YEAR(create_time)", po.getYear());
        contractCountQueryWrapper.lambda().in(BizReceiptContractHead::getReceiptType, EnumReceiptType.OTHER_CONTRACT.getValue(), EnumReceiptType.FRAMEWORK_CONTRACT.getValue());
        contractCountQueryWrapper.lambda().eq(BizReceiptContractHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        vo.setTotalCount(contractHeadDataWrap.count(contractCountQueryWrapper));

        // 查询已完成采购任务合同金额
        BigDecimal contractAmountSum = biPurchaseMapper.selectContractAmountSum(po);
        if (UtilNumber.isNotEmpty(contractAmountSum)) {
            vo.setTotalAmount(contractAmountSum.divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
        }

        // todo 汇率还没定下来是按每个月的汇率算 还是按整体汇率算

        // 查询完成招投标次数
        QueryWrapper<BizReceiptPurchaseApplyHead> purchaseCountQueryWrapper = new QueryWrapper<>();
        purchaseCountQueryWrapper.eq("YEAR(create_time)", po.getYear());
        purchaseCountQueryWrapper.lambda().in(BizReceiptPurchaseApplyHead::getReceiptType, EnumReceiptType.PURCHASE_APPLY.getValue(), EnumReceiptType.OIL_PO_PURCHASE.getValue(), EnumReceiptType.CONTRACT_CHANGE.getValue(), EnumReceiptType.DIRECT_PURCHASE.getValue());
        purchaseCountQueryWrapper.lambda().eq(BizReceiptPurchaseApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        vo.setPurchaseCount(purchaseApplyHeadDataWrap.count(purchaseCountQueryWrapper));

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * BI-已完成采购任务的合同数量
     */
    public void getCompletedContractCount(BizContext ctx) {
        BiSearchPO po = ctx.getPoContextData();
        po.setYear(UtilNumber.isNotEmpty(po.getYear()) ? po.getYear() : DateUtil.year(UtilDate.getNow()));
        int month = UtilNumber.isNotEmpty(po.getMonth()) ? po.getMonth() : DateUtil.month(UtilDate.getNow()) + 1;

        // 查询当前各个月的已完成采购任务的合同数量
        List<BiCompletedContractCountVO> voList = biPurchaseMapper.getCompletedContractCount(po);

        Map<Integer, BiCompletedContractCountVO> voMonthMap = voList.stream().collect(Collectors.toMap(BiCompletedContractCountVO::getMonth, v -> v));

        // 补充缺失月份数据
        for (int i = 1; i <= month; i++) {
            if (!voMonthMap.containsKey(i)) {
                BiCompletedContractCountVO vo = new BiCompletedContractCountVO();
                vo.setMonth(i);
                voList.add(vo);
            }
        }

        voList.sort(Comparator.comparing(BiCompletedContractCountVO::getMonth));

        // 查询已完成采购任务合同数量
        QueryWrapper<BizReceiptContractHead> contractCountQueryWrapper = new QueryWrapper<>();
        contractCountQueryWrapper.eq("YEAR(create_time)", po.getYear());
        contractCountQueryWrapper.lambda().in(BizReceiptContractHead::getReceiptType, EnumReceiptType.OTHER_CONTRACT.getValue(), EnumReceiptType.FRAMEWORK_CONTRACT.getValue());
        contractCountQueryWrapper.lambda().eq(BizReceiptContractHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        long countTotal = contractHeadDataWrap.count(contractCountQueryWrapper);

        for (BiCompletedContractCountVO vo : voList) {
            // 万元单位
            vo.setMonthStr(vo.getMonth() + "月");
            if (UtilNumber.isNotEmpty(countTotal)) {
                vo.setCountTotalProportion(vo.getCountTotal().divide(BigDecimal.valueOf(countTotal), 1, RoundingMode.HALF_UP).multiply(ONE_HUNDRED));
            }
        }

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, voList);
    }

    /**
     * BI-已完成采购任务的合同金额
     */
    public void getCompletedContractAmount(BizContext ctx) {
        BiSearchPO po = ctx.getPoContextData();
        po.setYear(UtilNumber.isNotEmpty(po.getYear()) ? po.getYear() : DateUtil.year(UtilDate.getNow()));
        int month = UtilNumber.isNotEmpty(po.getMonth()) ? po.getMonth() : DateUtil.month(UtilDate.getNow()) + 1;

        // 查询当前各个月的已完成采购任务的合同金额
        List<BiCompletedContractAmountVO> amount402List = biPurchaseMapper.getCompletedContractAmount402(po);
        // 查询当前各个月的已完成采购任务的合同金额
        List<BiCompletedContractAmountVO> amount403List = biPurchaseMapper.getCompletedContractAmount403(po);

        for (BiCompletedContractAmountVO amount402 : amount402List) {
            for (BiCompletedContractAmountVO amount403 : amount403List) {
                if (amount402.getMonth().equals(amount403.getMonth())) {
                    amount402.setAmount403(amount403.getAmount403());
                    break;
                }
            }
        }

        Map<Integer, BiCompletedContractAmountVO> voMonthMap = amount402List.stream().collect(Collectors.toMap(BiCompletedContractAmountVO::getMonth, v -> v));

        // 补充缺失月份数据
        for (int i = 1; i <= month; i++) {
            if (!voMonthMap.containsKey(i)) {
                BiCompletedContractAmountVO vo = new BiCompletedContractAmountVO();
                vo.setMonth(i);
                amount402List.add(vo);
            }
        }

        amount402List.sort(Comparator.comparing(BiCompletedContractAmountVO::getMonth));

        // 查询已完成采购任务合同金额
        BigDecimal amountTotal = BigDecimal.ZERO;
        BigDecimal contractAmountSum = biPurchaseMapper.selectContractAmountSum(po);
        if (UtilNumber.isNotEmpty(contractAmountSum)) {
            amountTotal = contractAmountSum.divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP);
        }

        for (BiCompletedContractAmountVO vo : amount402List) {
            // 万元单位
            vo.setMonthStr(vo.getMonth() + "月");
            vo.setAmount402(vo.getAmount402().divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            vo.setAmount403(vo.getAmount403().divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            vo.setAmountTotal(vo.getAmount402().add(vo.getAmount403()).divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            if (UtilNumber.isNotEmpty(amountTotal)) {
                vo.setAmountTotalProportion(vo.getAmountTotal().divide(amountTotal, 1, RoundingMode.HALF_UP).multiply(ONE_HUNDRED));
            }
        }

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, amount402List);
    }

    /**
     * BI-定时任务-保存采购类指标基础数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBaseTable() {

        // 查询需求计划单
        List<BiPurchaseBase> biPurchaseBaseDemandPlanList = new ArrayList<>();
        List<BiPurchaseDemandPlanVO> demandPlanList = biPurchaseMapper.selectDemandPlanHead();

        for (BiPurchaseDemandPlanVO demandPlanVO : demandPlanList) {
            BiPurchaseBase biPurchaseBase = new BiPurchaseBase();
            this.setBiPurchaseByDemandPlan(demandPlanVO, biPurchaseBase);
            biPurchaseBaseDemandPlanList.add(biPurchaseBase);
        }

        // 查询采购流程
        List<BiPurchaseBase> biPurchaseBasePurchaseList = new ArrayList<>();
        Map<Long, BiPurchaseBase> demandReceiptHeadIdMap = biPurchaseBaseDemandPlanList.stream().filter(base -> UtilNumber.isNotEmpty(base.getDemandReceiptHeadId())).collect(Collectors.groupingBy(BiPurchaseBase::getDemandReceiptHeadId, Collectors.collectingAndThen(Collectors.toList(), values -> values.get(0))));
        List<BiPurchasePurchaseVO> purchaseList = biPurchaseMapper.selectPurchaseHead();

        for (BiPurchasePurchaseVO purchaseVO : purchaseList) {
            if (UtilNumber.isNotEmpty(purchaseVO.getDemandHeadId()) && demandReceiptHeadIdMap.containsKey(purchaseVO.getDemandHeadId())) {
                BiPurchaseBase biPurchaseBase = UtilBean.deepCopy(demandReceiptHeadIdMap.get(purchaseVO.getDemandHeadId()));
                this.setBiPurchaseBaseByPurchase(purchaseVO, biPurchaseBase);
                biPurchaseBasePurchaseList.add(biPurchaseBase);
            } else {
                BiPurchaseBase biPurchaseBase = new BiPurchaseBase();
                // 采购申请单ID
                this.setBiPurchaseBaseByPurchase(purchaseVO, biPurchaseBase);
                biPurchaseBasePurchaseList.add(biPurchaseBase);
            }
        }
        List<Long> demandReceiptHeadIdList = biPurchaseBasePurchaseList.stream().map(BiPurchaseBase::getDemandReceiptHeadId).collect(Collectors.toList());
        for (BiPurchaseBase biPurchaseBaseDemandPlan : biPurchaseBaseDemandPlanList) {
            if (!demandReceiptHeadIdList.contains(biPurchaseBaseDemandPlan.getDemandReceiptHeadId())) {
                biPurchaseBasePurchaseList.add(biPurchaseBaseDemandPlan);
            }
        }

        // 查询合同管理流程
        List<BiPurchaseBase> biPurchaseBaseContractList = new ArrayList<>();
        Map<String, BiPurchaseBase> demandPurchaseReceiptHeadIdMap = biPurchaseBasePurchaseList.stream().filter(base -> UtilNumber.isNotEmpty(base.getDemandReceiptHeadId()) && UtilNumber.isNotEmpty(base.getPurchaseReceiptHeadId())).collect(Collectors.groupingBy(base -> base.getDemandReceiptHeadId() + Const.HYPHEN + base.getPurchaseReceiptHeadId(), Collectors.collectingAndThen(Collectors.toList(), values -> values.get(0))));
        Map<Long, List<BiPurchaseBase>> purchaseReceiptHeadIdMap = biPurchaseBasePurchaseList.stream().filter(base -> UtilNumber.isNotEmpty(base.getPurchaseReceiptHeadId())).collect(Collectors.groupingBy(BiPurchaseBase::getPurchaseReceiptHeadId));
        List<BiPurchaseContractVO> contractList = biPurchaseMapper.selectContractHead();

        for (BiPurchaseContractVO contractVO : contractList) {
            if (UtilNumber.isNotEmpty(contractVO.getDemandHeadId()) && UtilNumber.isNotEmpty(contractVO.getPurchaseHeadId()) && demandPurchaseReceiptHeadIdMap.containsKey(contractVO.getDemandHeadId() + Const.HYPHEN + contractVO.getPurchaseHeadId())) {
                BiPurchaseBase biPurchaseBase = UtilBean.deepCopy(demandPurchaseReceiptHeadIdMap.get(contractVO.getDemandHeadId() + Const.HYPHEN + contractVO.getPurchaseHeadId()));
                this.setBiPurchaseByContract(contractVO, biPurchaseBase);
                biPurchaseBaseContractList.add(biPurchaseBase);
            } else if (UtilNumber.isNotEmpty(contractVO.getPurchaseHeadId()) && purchaseReceiptHeadIdMap.containsKey(contractVO.getPurchaseHeadId())) {
                for (BiPurchaseBase biPurchaseBase : UtilBean.deepCopy(purchaseReceiptHeadIdMap.get(contractVO.getPurchaseHeadId()))) {
                    this.setBiPurchaseByContract(contractVO, biPurchaseBase);
                    biPurchaseBaseContractList.add(biPurchaseBase);
                }
            } else {
                BiPurchaseBase biPurchaseBase = new BiPurchaseBase();
                this.setBiPurchaseByContract(contractVO, biPurchaseBase);
                biPurchaseBaseContractList.add(biPurchaseBase);
            }
        }
        List<Long> purchaseReceiptHeadIdList = biPurchaseBaseContractList.stream().map(BiPurchaseBase::getPurchaseReceiptHeadId).collect(Collectors.toList());
        for (BiPurchaseBase biPurchaseBasePurchase : biPurchaseBasePurchaseList) {
            if (!purchaseReceiptHeadIdList.contains(biPurchaseBasePurchase.getPurchaseReceiptHeadId())) {
                biPurchaseBaseContractList.add(biPurchaseBasePurchase);
            }
        }

        // 删除原有数据
        biPurchaseBaseDataWrap.remove(new QueryWrapper<>());

        // 保存底表数据
        if (UtilCollection.isNotEmpty(biPurchaseBaseContractList)) {
            biPurchaseBaseDataWrap.saveBatch(biPurchaseBaseContractList);
        }
    }

    /**
     * 设置BI采购基础表传输对象字段
     */
    private void setBiPurchaseByDemandPlan(BiPurchaseDemandPlanVO demandPlanVO, BiPurchaseBase biPurchaseBase) {
        // 需求计划单ID
        biPurchaseBase.setDemandReceiptHeadId(demandPlanVO.getId());
        // 需求计划单号
        biPurchaseBase.setDemandReceiptCode(demandPlanVO.getReceiptCode());
        // 需求计划创建时间
        biPurchaseBase.setDemandCreateTime(demandPlanVO.getCreateTime());
        // 需求计划创建人
        biPurchaseBase.setDemandCreateUserId(demandPlanVO.getCreateUserId());
        // 需求计划单处理人
        biPurchaseBase.setDemandHandleUserId(demandPlanVO.getHandleUserId());
        // 需求计划完成时间
        biPurchaseBase.setDemandModifyTime(demandPlanVO.getModifyTime());
        // 需求计划计划类型
        biPurchaseBase.setDemandPlanType(demandPlanVO.getDemandPlanType());
        // 需求计划需求类型
        biPurchaseBase.setDemandType(demandPlanVO.getDemandType());
    }

    /**
     * 设置BI采购基础表传输对象字段
     */
    private void setBiPurchaseBaseByPurchase(BiPurchasePurchaseVO purchaseVO, BiPurchaseBase biPurchaseBase) {
        // 采购申请单ID
        biPurchaseBase.setPurchaseReceiptHeadId(purchaseVO.getId());
        // 采购申请单号
        biPurchaseBase.setPurchaseReceiptCode(purchaseVO.getReceiptCode());
        // 采购申请单据类型
        biPurchaseBase.setPurchaseReceiptType(purchaseVO.getReceiptType());
        // 采购申请类型
        biPurchaseBase.setPurchaseDemandPlanType(purchaseVO.getDemandPlanType());
        // 采购申请采购方式
        biPurchaseBase.setPurchaseBidMethod(purchaseVO.getBidMethod());
        // 采购申请采购主体
        biPurchaseBase.setPurchaseSubject(purchaseVO.getPurchaseSubject());
        // 采购申请预算出处
        biPurchaseBase.setPurchaseAnnualBudgetId(purchaseVO.getAnnualBudgetId());
        // 采购申请申请预算金额
        biPurchaseBase.setPurchaseBudgetAmount(purchaseVO.getBudgetAmount());
        // 采购申请创建时间
        biPurchaseBase.setPurchaseCreateTime(purchaseVO.getCreateTime());
        // 采购申请创建人
        biPurchaseBase.setPurchaseCreateUserId(purchaseVO.getCreateUserId());
        // 需求计划完成时间
        biPurchaseBase.setDemandModifyTime(purchaseVO.getModifyTime());
        // 寻源时间（天） = 采购申请创建时间 - 需求计划完成时间
        if (UtilObject.isNotNull(biPurchaseBase.getPurchaseCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getDemandModifyTime())) {
            biPurchaseBase.setPurchaseSourcingTime(DateUtil.betweenDay(biPurchaseBase.getPurchaseCreateTime(), biPurchaseBase.getDemandModifyTime(), true));
        }
    }

    /**
     * 设置BI采购基础表传输对象字段
     */
    private void setBiPurchaseByContract(BiPurchaseContractVO contractVO, BiPurchaseBase biPurchaseBase) {
        // 合同单ID
        biPurchaseBase.setContractReceiptHeadId(contractVO.getId());
        // 合同单号
        biPurchaseBase.setContractReceiptCode(contractVO.getReceiptCode());
        // 合同单据类型
        biPurchaseBase.setContractReceiptType(contractVO.getReceiptType());
        // 合同类型
        biPurchaseBase.setContractFirstParty(contractVO.getFirstParty());
        // 合同采购方式
        biPurchaseBase.setContractSupplierId(contractVO.getSupplierId());
        // 合同采购主体
        biPurchaseBase.setContractOilPrice(contractVO.getOilPrice());
        // 合同预算出处
        biPurchaseBase.setContractAmount(contractVO.getAmount());
        // 合同创建时间
        biPurchaseBase.setContractCreateTime(contractVO.getCreateTime());
        // 定价审批时间（天） = 合同创建时间 - 采购申请创建时间
        // 定价审批时间（天）框架协议PO = 合同创建时间 - 需求计划完成时间
        if (UtilNumber.isNotEmpty(biPurchaseBase.getDemandPlanType()) && EnumDemandPlanType.FRAMEWORK_AGREEMENT_PO_PlAN.getCode().equals(biPurchaseBase.getDemandPlanType())) {
            if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getDemandModifyTime())) {
                biPurchaseBase.setPriceApprovalInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getDemandModifyTime(), true));
            }
        } else {
            if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getPurchaseCreateTime())) {
                biPurchaseBase.setPriceApprovalInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getPurchaseCreateTime(), true));
            }
        }
        // 采购阶段时间（天） = 合同创建时间 - 采购申请完成时间
        // 采购阶段时间（天）框架协议PO = 合同创建时间 - 需求计划完成时间
        if (UtilNumber.isNotEmpty(biPurchaseBase.getDemandPlanType()) && EnumDemandPlanType.FRAMEWORK_AGREEMENT_PO_PlAN.getCode().equals(biPurchaseBase.getDemandPlanType())) {
            if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getDemandModifyTime())) {
                biPurchaseBase.setPurchaseStageInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getDemandModifyTime(), true));
            }
        } else {
            if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getPurchaseModifyTime())) {
                biPurchaseBase.setPurchaseStageInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getPurchaseModifyTime(), true));
            }
        }
        // 合同签订时间（天） = 合同创建时间 - 需求计划创建时间
        if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getDemandCreateTime())) {
            biPurchaseBase.setContractSignInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getDemandCreateTime(), true));
        }
    }

}
