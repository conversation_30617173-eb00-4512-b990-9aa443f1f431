package com.inossem.wms.bizdomain.returns.service.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.erp.service.biz.ErpPostingService;
import com.inossem.wms.bizbasis.erp.service.biz.ReserveReceiptService;
import com.inossem.wms.bizbasis.erp.service.biz.SaleReceiptService;
import com.inossem.wms.bizbasis.feign.output.OutputFeignApi;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.biz.PalletSortingService;
import com.inossem.wms.bizbasis.sap.restful.service.HXSapIntegerfaceService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.spec.service.biz.BizSpecFeatureValueService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputInfoDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.bizdomain.returns.service.datawrap.BizReceiptReturnBinDataWrap;
import com.inossem.wms.bizdomain.returns.service.datawrap.BizReceiptReturnHeadDataWrap;
import com.inossem.wms.bizdomain.returns.service.datawrap.BizReceiptReturnItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.apply.EnumReceiveType;
import com.inossem.wms.common.enums.spec.EnumSpecClassifyType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchImgDTO;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputInfo;
import com.inossem.wms.common.model.bizdomain.output.entity.BizReceiptOutputItem;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnBinDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnHeadDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnItemDTO;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnBin;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnHead;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnItem;
import com.inossem.wms.common.model.bizdomain.returns.po.BizReceiptReturnQueryListPO;
import com.inossem.wms.common.model.bizdomain.returns.po.BizReceiptReturnWriteOffPO;
import com.inossem.wms.common.model.bizdomain.returns.vo.BizReceiptReturnPageVO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqItemDTO;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqSavePo;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelReceiptRelDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.mat.fty.dto.DicMaterialFactoryDTO;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelBatch;
import com.inossem.wms.common.model.sap.posting.HXPostingHeader;
import com.inossem.wms.common.model.sap.posting.HXPostingItem;
import com.inossem.wms.common.model.sap.posting.HXPostingReturn;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypePostTaskDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 退库公共类
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-04-25
 */

@Service
@Slf4j
public class ReturnComponent {

    @Autowired
    private BizReceiptReturnHeadDataWrap bizReceiptReturnHeadDataWrap;

    @Autowired
    private BizReceiptReturnItemDataWrap bizReceiptReturnItemDataWrap;

    @Autowired
    private BizReceiptReturnBinDataWrap bizReceiptReturnBinDataWrap;

    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private BatchImgService batchImgService;

    @Autowired
    private DataFillService dataFillService;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private BizSpecFeatureValueService bizSpecFeatureValueService;

    @Autowired
    private BatchInfoService bizBatchInfoService;

    @Autowired
    private ErpPostingService erpPostingService;

    @Autowired
    private SapInterfaceService sapInterfaceService;

    @Autowired
    private LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private PalletSortingService palletSortingService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private ReserveReceiptService reserveReceiptService;

    @Autowired
    private SaleReceiptService saleReceiptService;

    @Autowired
    private OutputFeignApi outputFeignApi;

    @Autowired
    protected BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;
    @Autowired
    private BizBatchInfoDataWrap bizBatchInfoDataWrap;
    @Autowired
    private BizReceiptApplyHeadDataWrap bizReceiptApplyHeadDataWrap;
    @Autowired
    private BizReceiptOutputInfoDataWrap bizReceiptOutputInfoDataWrap;
    @Autowired
    private HXSapIntegerfaceService hxInterfaceService;

    /**
     * 设置单据流
     *
     * @param ctx 上下文
     */
    public void setExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptReturnHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService
                .getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @param ctx 上下文
     */
    public void setExtendAttachment(BizContext ctx) {
        BizResultVO<BizReceiptReturnHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启日志
     *
     * @param ctx 上下文
     */
    public void setExtendOperationLog(BizContext ctx) {
        BizResultVO<BizReceiptReturnHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 获取退库单列表(分页)
     *
     * @param ctx 上下文
     */
    public void getPage(BizContext ctx) {
        BizReceiptReturnQueryListPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Date startTime = null;
        Date endTime = null;
        List<Long> preReceiptHeadIdList = new ArrayList<>();
        if (Objects.nonNull(po.getPostCreateTime()) && Objects.nonNull(po.getPostEndTime())) {
            startTime = UtilLocalDateTime.getStartTime(po.getPostCreateTime());
            endTime = UtilLocalDateTime.getEndTime(po.getPostEndTime());
        }
        // 根据前序单据号查询前序单据id
        if (UtilString.isNotNullOrEmpty(po.getPreReceiptCode())) {
            if (po.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_MAT_REQ.getValue())) {
                preReceiptHeadIdList.add(reserveReceiptService.getIdByReceiptCode(po.getPreReceiptCode()));
            } else if (po.getReceiptType().equals(EnumReceiptType.STOCK_RETURN_SALE.getValue())) {
                preReceiptHeadIdList.add(saleReceiptService.getIdByReceiptCode(po.getPreReceiptCode()));
            }
            Long outputId = outputFeignApi.getOutputIdByCode(po.getPreReceiptCode());
            preReceiptHeadIdList.add(outputId);
            List<Long> filterIds = preReceiptHeadIdList.stream()
                .filter(id -> Objects.nonNull(id) && !(id.longValue() == 0)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterIds)) {
                ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                    new PageObjectVO<BizReceiptReturnPageVO>(new ArrayList<>(), Long.valueOf(0)));
                return;
            }
        }
        CurrentUser user = ctx.getCurrentUser();
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptReturnQueryListPO> pageWrapper = new WmsQueryWrapper<>();
        // 拼装参数(boot根据配置，自动生成查询条件)
        pageWrapper.lambda()
            .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptReturnQueryListPO::getReceiptCode,
                po.getReceiptCode())
            .eq(BizReceiptReturnQueryListPO::getReceiptType, po.getReceiptType())
            .in(!CollectionUtils.isEmpty(preReceiptHeadIdList), BizReceiptReturnQueryListPO::getPreReceiptHeadId,
                BizReceiptReturnItem.class, preReceiptHeadIdList)
            .eq(UtilString.isNotNullOrEmpty(po.getMatDocCode()), BizReceiptReturnQueryListPO::getMatDocCode,
                BizReceiptReturnItem.class, po.getMatDocCode())
            .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptReturnQueryListPO::getReceiptStatus,
                po.getReceiptStatusList())
             .in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptReturnQueryListPO::getLocationId,
                     BizReceiptReturnItem.class,locationIdList)
            .between((Objects.nonNull(startTime)), BizReceiptReturnQueryListPO::getPostingDate,
                BizReceiptReturnItem.class, startTime, endTime)
            .eq(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptReturnQueryListPO::getMatCode, DicMaterial.class,po.getMatCode());
        IPage<BizReceiptReturnPageVO> page = po.getPageObj(BizReceiptReturnPageVO.class);
        bizReceiptReturnHeadDataWrap.getBizReceiptReturnPageVOList(page, pageWrapper);
        long totalCount = page.getTotal();
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), totalCount));
    }

    /**
     * 设置物料工厂信息(是否启用erp批次生产批次启用包装)
     *
     * @param list 列表
     * @param <E> 泛型
     */
    public <E> void setMaterialFactoryInfo(List<E> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (Object obj : list) {
            if (obj instanceof BizReceiptReturnItemDTO) {
                BizReceiptReturnItemDTO item = (BizReceiptReturnItemDTO)obj;
                DicMaterialFactoryDTO mf =
                    dictionaryService.getDicMaterialFactoryByUniqueKey(item.getMatId(), item.getFtyId());
                if (Objects.nonNull(mf)) {
                    item.setMatBatchErpEnabled(mf.getIsBatchErpEnabled());
                    item.setMatBatchProductEnabled(mf.getIsBatchProductEnabled());
                    item.setMatPackageEnabled(mf.getIsPackageEnabled());
                } else {
                    item.setMatBatchErpEnabled(EnumRealYn.FALSE.getIntValue());
                    item.setMatBatchProductEnabled(EnumRealYn.FALSE.getIntValue());
                    item.setMatPackageEnabled(EnumRealYn.FALSE.getIntValue());
                }
            }
        }
    }

    /**
     * 根据headId查询退库单列表
     *
     * @param headId 单据id
     * @return 退库单信息
     */
    public BizReceiptReturnHeadDTO getReceiptReturnHeadById(Long headId) {
        BizReceiptReturnHead bizReceiptReturnHead = bizReceiptReturnHeadDataWrap.getById(headId);
        BizReceiptReturnHeadDTO headDTO = UtilBean.newInstance(bizReceiptReturnHead, BizReceiptReturnHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        for (BizReceiptReturnItemDTO itemDTO : headDTO.getItemDTOList()) {
            if (UtilNumber.isNotNull(itemDTO.getApplyHeadId())) {
                itemDTO.setReceiveType(bizReceiptApplyHeadDataWrap.getById(itemDTO.getApplyHeadId()).getReceiveType());
            }
        }
        return headDTO;
    }

    /**
     * 根据退库单id和行项目id列表查询退库单
     *
     * @param headId 单据id
     * @param itemIdList 行项目id列表
     * @return 退库单信息
     */
    public BizReceiptReturnHeadDTO getItemListAndBinList(Long headId, List<Long> itemIdList) {
        BizReceiptReturnHead head = bizReceiptReturnHeadDataWrap.getById(headId);
        BizReceiptReturnHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptReturnHeadDTO.class);
        QueryWrapper<BizReceiptReturnItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptReturnItem::getHeadId, headId).in(BizReceiptReturnItem::getId, itemIdList);
        List<BizReceiptReturnItem> itemList = bizReceiptReturnItemDataWrap.list(queryWrapper);
        List<BizReceiptReturnItemDTO> itemDTOList = UtilCollection.toList(itemList, BizReceiptReturnItemDTO.class);
        dataFillService.fillAttr(itemDTOList);
        headDTO.setItemDTOList(itemDTOList);
        return headDTO;
    }

    /**
     * 保存批次图片
     *
     * @param ctx 上下文
     */
    public void saveBatchImg(BizContext ctx) {
        // 入参上下文
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizBatchImgDTO> imgDTOList = new ArrayList<>();
        headDTO.getItemDTOList().forEach(itemDTO -> {
            if (UtilCollection.isNotEmpty(itemDTO.getItemInfoList())) {
                itemDTO.getItemInfoList().forEach(binDTO -> {
                    binDTO.getBatchImgList().forEach(imgDTO -> {
                        imgDTO.setMatId(itemDTO.getMatId());
                        imgDTO.setFtyId(itemDTO.getFtyId());
                        imgDTO.setBatchId(binDTO.getBatchId());
                        imgDTOList.add(imgDTO);
                    });
                });
            }
        });
        if (UtilCollection.isNotEmpty(imgDTOList)) {
            // 批量保存退库单批次图片
            batchImgService.multiSaveBizBatchImg(imgDTOList);
            log.debug("批量保存退库单批次图片成功!");
        }
    }

    /**
     * 设置批次图片信息
     *
     * @param ctx 上下文
     */
    public void setBatchImg(BizContext ctx) {
        BizResultVO<BizReceiptReturnHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(resultVO.getHead()) && UtilCollection.isEmpty(resultVO.getHead().getItemDTOList())) {
            return;
        }
        Set<Long> batchIdSet = new HashSet<>();
        resultVO.getHead().getItemDTOList().forEach(itemDTO -> {
            if (UtilCollection.isNotEmpty(itemDTO.getItemInfoList())) {
                batchIdSet.addAll(itemDTO.getItemInfoList().stream().map(BizReceiptReturnBinDTO::getBatchId)
                    .collect(Collectors.toSet()));
            }
        });
        if (UtilCollection.isEmpty(batchIdSet)) {
            return;
        }
        // 获取批次图片
        Map<Long, List<BizBatchImgDTO>> imgMap = batchImgService.getBatchImgListByBatchIdList(batchIdSet, 4);
        if (imgMap.isEmpty()) {
            return;
        }
        // 赋值批次图片
        resultVO.getHead().getItemDTOList().forEach(itemDTO -> {
            itemDTO.getItemInfoList().forEach(binDTO -> {
                if (UtilNumber.isNotEmpty(binDTO.getBatchId())
                    && UtilCollection.isNotEmpty(imgMap.get(binDTO.getBatchId()))) {
                    binDTO.setBatchImgList(imgMap.get(binDTO.getBatchId()));
                }
            });
        });
    }

    /**
     * 删除批次图片
     *
     * @param ctx 上下文
     */
    public void deleteBatchImg(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<BizReceiptReturnBinDTO> binList = new ArrayList<>();
        headDTO.getItemDTOList().stream().filter(itemDTO -> binList.addAll(itemDTO.getItemInfoList()));
        if (UtilCollection.isNotEmpty(binList)) {
            // 批次主键集合
            List<Long> batchIdList =
                binList.stream().map(BizReceiptReturnBinDTO::getBatchId).collect(Collectors.toList());
            // 逻辑删除批批次图片
            batchImgService.multiDeleteByBatchIdList(batchIdList);
        }
    }

    /**
     * 保存批次信息
     *
     * @param ctx 上下文
     */
    public void saveBatchInfo(BizContext ctx) {
        // 入参上下文
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
        headDTO.getItemDTOList().forEach(itemDTO -> {
            if (UtilCollection.isNotEmpty(itemDTO.getItemInfoList())) {
                itemDTO.getItemInfoList().forEach(binDTO -> {
                    if(org.apache.commons.lang3.StringUtils.isNotEmpty(itemDTO.getSpecStockCode())){
                        binDTO.getBatchInfo().setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_PROJECT.getValue());
                    }else{
                        binDTO.getBatchInfo().setSpecStock(EnumSpecStock.SPEC_STOCK_BATCH_STATUS_NORMAL.getValue());
                    }
                    binDTO.getBatchInfo().setBackTransport(headDTO.getBackTransport());
                    binDTOList.add(binDTO);
                });
            }
        });
        if (UtilCollection.isNotEmpty(binDTOList)) {
            // 批量修改退库单批次信息
            bizBatchInfoService.multiUpdateBatchInfo(
                binDTOList.stream().map(BizReceiptReturnBinDTO::getBatchInfo).collect(Collectors.toList()));
            log.debug("批量保存退库单批次信息成功!");
            // 更新退库单bin batchId
            binDTOList.stream().forEach(binDTO -> {
                binDTO.setBatchId(binDTO.getBatchInfo().getId());
            });
            bizReceiptReturnBinDataWrap.updateBatchDtoById(binDTOList);
        }
    }

    /**
     * 删除批次信息
     *
     * @param ctx 上下文
     */
    public void deleteBatchInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<BizReceiptReturnBinDTO> binList = new ArrayList<>();
        headDTO.getItemDTOList().stream().filter(itemDTO -> binList.addAll(itemDTO.getItemInfoList()));
        if (UtilCollection.isNotEmpty(binList)) {
            // 批次主键集合
            List<Long> batchIdList =
                binList.stream().map(BizReceiptReturnBinDTO::getBatchId).collect(Collectors.toList());
            // 逻辑删除批次信息
            bizBatchInfoService.multiDeleteBatchInfo(batchIdList);
        }
    }

    /**
     * 保存批次特性
     *
     * @param ctx 上下文
     */
    public void saveSpecFeature(BizContext ctx) {
        // 入参上下文
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(headDTO) && UtilCollection.isEmpty(headDTO.getItemDTOList())) {
            return;
        }
        List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
        headDTO.getItemDTOList().forEach(itemDTO -> {
            if (UtilCollection.isNotEmpty(itemDTO.getItemInfoList())) {
                itemDTO.getItemInfoList().forEach(binDTO -> {
                    binDTO.setMatId(itemDTO.getMatId());
                    binDTO.setFtyId(itemDTO.getFtyId());
                    // binDTO.setBatchId(binDTO.getBatchId());
                    binDTOList.add(binDTO);
                });
            }
        });
        // 保存物料特性
        bizSpecFeatureValueService.saveSpecList(binDTOList, BizReceiptReturnBinDTO.class,
            EnumSpecClassifyType.QUALITY_TYPE.getValue(), ctx.getCurrentUser());
        log.debug("保存物料特性成功!");
    }

    /**
     * 设置批次特性
     *
     * @param ctx 上下文
     */
    public void setSpecFeature(BizContext ctx) {
        BizResultVO<BizReceiptReturnHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        if (UtilObject.isNull(resultVO.getHead()) && UtilCollection.isEmpty(resultVO.getHead().getItemDTOList())) {
            List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
            resultVO.getHead().getItemDTOList().forEach(itemDTO -> {
                if (UtilCollection.isNotEmpty(itemDTO.getItemInfoList())) {
                    itemDTO.getItemInfoList().forEach(binDTO -> {
                        binDTO.setMatId(itemDTO.getMatId());
                        binDTO.setFtyId(itemDTO.getFtyId());
                        binDTO.setBatchId(binDTO.getBatchId());
                        binDTOList.add(binDTO);
                    });
                }
            });
            // 获取物料特性
            bizSpecFeatureValueService.getSpecList(binDTOList, BizReceiptReturnBinDTO.class,
                EnumSpecClassifyType.QUALITY_TYPE.getValue());
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptReturnItemDTO> itemDTOList = headDTO.getItemDTOList();
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        for (BizReceiptReturnItemDTO item : itemDTOList) {
            BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
            bizCommonReceiptRelation.setReceiptType(headDTO.getReceiptType());
            bizCommonReceiptRelation.setReceiptHeadId(item.getHeadId());
            bizCommonReceiptRelation.setReceiptItemId(item.getId());
            bizCommonReceiptRelation.setPreReceiptType(itemDTOList.get(0).getPreReceiptType());
            bizCommonReceiptRelation.setPreReceiptHeadId(item.getPreReceiptHeadId());
            bizCommonReceiptRelation.setPreReceiptItemId(item.getPreReceiptItemId());
            list.add(bizCommonReceiptRelation);
        }
        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     * 逻辑逻辑单据流
     *
     * @param ctx 上下文
     */
    public void deleteReceiptTree(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        receiptRelationService.deleteReceiptTree(headDTO.getReceiptType(), headDTO.getId());
    }

    /**
     * 保存日志
     *
     * @param ctx 上下文
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
            operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 保存单据附件
     *
     * @param ctx 上下文
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
            headDTO.getReceiptType(), ctx.getCurrentUser().getId());
    }

    /**
     * 删除附件
     *
     * @param ctx 上下文
     */
    public void deleteBizReceiptAttachment(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        receiptAttachmentService.deleteBizReceiptAttachment(headDTO.getId(), headDTO.getReceiptType());
    }

    /**
     * 删除作业请求
     *
     * @param ctx 上下文
     */
    public void cancelTaskRequest(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<Long> itemIdList =
            headDTO.getItemDTOList().stream().map(BizReceiptReturnItemDTO::getId).collect(Collectors.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_IDS, itemIdList);
        RocketMQProducerProcessor.getInstance()
            .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.DEL_RECEIPT_REQ_ITEM, ctx));
    }

    /**
     * 获取按钮权限
     *
     * @param headId 单据id
     * @return 按钮设置
     */
    public ButtonVO setButton(Long headId) {
        ButtonVO button = new ButtonVO();
        QueryWrapper<BizReceiptReturnHead> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptReturnHead::getId, headId).eq(BizReceiptReturnHead::getIsDelete, 0);
        BizReceiptReturnHead one = bizReceiptReturnHeadDataWrap.getOne(queryWrapper);
        button.setButtonSave(this.setButtonSave(headId, one));
        button.setButtonDelete(this.setButtonDelete(headId, one));
        button.setButtonSubmit(this.setButtonSubmit(headId, one));
        button.setButtonPost(this.setButtonPost(headId, one));
        button.setButtonReceiptPrint(this.setButtonReceiptPrint(headId, one));
       // button.setButtonWriteOff(this.setButtonWriteOff(headId, one));
        return button;
    }

    private Boolean setButtonReceiptPrint(Long headId, BizReceiptReturnHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)
                || EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            return true;
        }
        return false;
    }

    /**
     * 退库单能否保存
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonSave(Long headId, BizReceiptReturnHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }

    /**
     * 退库单能否删除
     *
     * @param headId 单据id
     * @return 0 不能、1能
     */
    public Boolean setButtonDelete(Long headId, BizReceiptReturnHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态 可以删除
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 驳回状态 可以删除
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            // 单据已提交状态，且没有开始作业的，允许删除
            byte taskStatus = this.getReceiptTaskStatus(headId);
            return EnumReceiptTaskStatus.NOT_STARTED.getValue().equals(taskStatus);
        }
        return false;
    }

    /**
     * 退库单能否提交 0否、1是
     *
     * @param headId 单据id
     * @return 退库单能否提交 0否、1是
     */
    public Boolean setButtonSubmit(Long headId, BizReceiptReturnHead one) {
        if (Objects.isNull(headId)) {
            return true;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿状态
            return true;
        }
        // 驳回状态
        return EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus);
    }

    /**
     * 退库单能否未同步再次过账 0否、1是
     *
     * @param headId 单据id
     * @return 是否
     */
    public Boolean setButtonPost(Long headId, BizReceiptReturnHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        Integer receiptStatus = one.getReceiptStatus();
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步状态
            return true;
        }
        // 已作业状态
        return EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus);
    }

    /**
     * 退库单能否显示冲销按钮 0否、1是
     *
     * @param headId 单据id
     * @return 退库单能否冲销 0否、1是
     */
    public Boolean setButtonWriteOff(Long headId, BizReceiptReturnHead one) {
        if (Objects.isNull(headId)) {
            return false;
        }
        // 单据当前状态
        Integer receiptStatus = one.getReceiptStatus();
        QueryWrapper<BizReceiptReturnItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptReturnItem::getHeadId, headId);
        List<BizReceiptReturnItem> itemList = bizReceiptReturnItemDataWrap.list(queryWrapper);
        // 未同步状态和已完成
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)
                || EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 如果存在任意一个行项目未冲销，显示冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptReturnItem item : itemList) {
                if (EnumRealYn.FALSE.getIntValue().equals(item.getIsWriteOff())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            return canDisplayWriteOff;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {
            // 已记账
            return true;
        } else if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus)) {
            // 作业中
            // 如果存在任意一个行项目已过账，显示冲销
            boolean canDisplayWriteOff = false;
            for (BizReceiptReturnItem item : itemList) {
                if (EnumRealYn.TRUE.getIntValue().equals(item.getIsPost())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            return canDisplayWriteOff;
        }
        return false;
    }

    /**
     * 查询单据SAP同步状态
     *
     * @param headId 单据id
     * @return 0未同步，1已同步
     */
    public boolean getSapPostSyncStatus(Long headId) {
        BizReceiptReturnHeadDTO headDTO = this.getReceiptReturnHeadById(headId);
        List<BizReceiptReturnItemDTO> itemDTOList = headDTO.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRIMARY_KEY_NOT_EXIST, headId.toString());
        }
        boolean isSapPostSync = true;
        for (BizReceiptReturnItemDTO returnItemVo : itemDTOList) {
            if (!StringUtils.hasText(returnItemVo.getMatDocCode())) {
                isSapPostSync = false;
                break;
            }
        }
        return isSapPostSync;
    }

    /**
     * 获取退库单作业状态
     *
     * @param headId 单据id
     * @return 0未作业,1作业中,2已作业
     */
    public byte getReceiptTaskStatus(Long headId) {
        // 单据作业状态，判断行项目的taskQty
        List<BizReceiptReturnItemDTO> itemList = this.getReceiptReturnHeadById(headId).getItemDTOList();
        boolean allDone = true;
        boolean started = false;
        for (BizReceiptReturnItemDTO item : itemList) {
            BigDecimal taskQty = item.getTaskQty();
            // taskQty大于0
            if (taskQty.compareTo(BigDecimal.ZERO) > 0) {
                started = true;
            }
            // 行项目的退库数量总数
            BigDecimal totalOperatedQty = BigDecimal.ZERO;
            if (UtilCollection.isNotEmpty(item.getItemInfoList())) {
                for (BizReceiptReturnBinDTO bin : item.getItemInfoList()) {
                    totalOperatedQty = totalOperatedQty.add(bin.getQty());
                }
            }
            // 作业数与退库总数相等且不为0，代表行项目已作业
            boolean tasked = !totalOperatedQty.equals(BigDecimal.ZERO) && taskQty.compareTo(totalOperatedQty) == 0;
            if (!tasked) {
                // 存在任意一个行项目不是已作业状态，则修改allDone标识为false
                allDone = false;
            }
        }
        if (!started) {
            // 所有行项目taskQty都不大于0，未开始
            return EnumReceiptTaskStatus.NOT_STARTED.getValue();
        } else {
            if (allDone) {
                // 所有行项目作业数与退库总数相等，已完成作业
                return EnumReceiptTaskStatus.DONE.getValue();
            } else {
                // 任意一个行项目作业数与退库总数不相等，但作业数大于0，作业中
                return EnumReceiptTaskStatus.IN_PROGRESS.getValue();
            }
        }
    }

    /**
     * 保存校验
     *
     * @param ctx 上下文
     */
    public void checkSave(BizContext ctx) {
        this.check(ctx);
    }

    /**
     * 参数基本校验
     *
     * @param ctx 上下文
     */
    public void check(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 主参数是否为空
        if (Objects.isNull(headDTO)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(headDTO.getItemDTOList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 校验行项目qty是否为0
     *
     * @param ctx 上下文
     */
    public void checkItemQtyIsZero(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<String> errorRidList = new ArrayList<>();
        headDTO.getItemDTOList().forEach(itemDTO -> {
            if (itemDTO.getQty().compareTo(BigDecimal.ZERO) == 0) {
                errorRidList.add(itemDTO.getRid());
            }
        });
        if (UtilCollection.isNotEmpty(errorRidList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RETURN_QTY_ZERO, errorRidList.toString());
        }
    }

    /**
     * 退库数量是否不大于对应出库Bin的可退库数量
     *
     * @param headDTO
     */
    public void checkOutputQty(BizReceiptReturnHeadDTO headDTO) {
        List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
        headDTO.getItemDTOList().stream().forEach(item -> binDTOList.addAll(item.getItemInfoList()));
        Map<Long, List<BizReceiptReturnBinDTO>> binMap =
            binDTOList.stream().collect(Collectors.groupingBy(BizReceiptReturnBinDTO::getOutputBinId));
        List<String> errorCodeList = new ArrayList<>();
        for (Long key : binMap.keySet()) {
            List<BizReceiptReturnBinDTO> list = binMap.get(key);
            BizReceiptReturnBinDTO binDTO = list.get(0);
            BigDecimal returnQty =
                list.stream().map(BizReceiptReturnBinDTO::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (returnQty.compareTo(binDTO.getOutputQty().subtract(binDTO.getReturnQty())) > 0) {
                errorCodeList.add(binDTO.getOutputCode());
            }
        }
        if (UtilCollection.isNotEmpty(errorCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_OUTPUT_CAN_RETURN_QTY_NOT_ENOUGH,
                errorCodeList.toString());
        }
    }

    /**
     * 保存单据
     *
     * @param ctx
     */
    public void saveReceipt(BizContext ctx) {
        boolean isNew = true;
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        /* ********************** head处理开始 *************************/
        Long headId = headDTO.getId();
        String code = headDTO.getReceiptCode();
        Integer status = EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue();
        Date createTime = new Date();
        Long createUserId = user.getId();
        BizReceiptReturnHead bizReceiptReturnHead = null;
        // 修改
        if (UtilNumber.isNotEmpty(headId)) {
            bizReceiptReturnHead = bizReceiptReturnHeadDataWrap.getById(headId);
            // 根据id更新
            bizReceiptReturnHeadDataWrap.updateDtoById(headDTO);
            // item物理删除
            QueryWrapper<BizReceiptReturnItem> itemQueryWrapper = new QueryWrapper<>();
            itemQueryWrapper.lambda().eq(BizReceiptReturnItem::getHeadId, headId);
            bizReceiptReturnItemDataWrap.physicalDelete(itemQueryWrapper);
            // bin物理删除
            QueryWrapper<BizReceiptReturnBin> binQueryWrapper = new QueryWrapper<>();
            binQueryWrapper.lambda().eq(BizReceiptReturnBin::getHeadId, headId);
            bizReceiptReturnBinDataWrap.physicalDelete(binQueryWrapper);
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
            isNew = false;
        } else {
            // 新增
            Integer receiptType = headDTO.getReceiptType();
            if (EnumReceiptType.STOCK_RETURN_TRANSFER.getValue().equals(receiptType)) { //退转库入库
                code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TRANSFER_RETURN.getValue());
            }else if (EnumReceiptType.UNITIZED_STOCK_RETURN_TRANSFER.getValue().equals(receiptType)) { //退转库入库
                code = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_UNITIZED_TRANSFER_RETURN_INPUT.getValue());
            }else{
                code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.SEQUENCE_RETURN.getValue());
            }
            headDTO.setReceiptCode(code);
            headDTO.setCreateUserId(createUserId);
            headDTO.setModifyUserId(createUserId);
            headDTO.setReceiptType(headDTO.getReceiptType());
            headDTO.setReceiptStatus(status);
            bizReceiptReturnHeadDataWrap.saveDto(headDTO);
            // 单据日志 - 新增
            if (Objects.isNull(operationLogType)) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                    EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        if (!isNew) {
            status = bizReceiptReturnHead.getReceiptStatus();
            createTime = bizReceiptReturnHead.getCreateTime();
            createUserId = bizReceiptReturnHead.getCreateUserId();
        }
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        List<BizReceiptReturnItemDTO> itemDTOList = headDTO.getItemDTOList();
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            itemDTO.setRid(String.format("%05d",rid.getAndIncrement()*10));
            itemDTO.setId(null);
            itemDTO.setHeadId(headDTO.getId());
            itemDTO.setDocDate(headDTO.getDocDate());
            itemDTO.setPostingDate(headDTO.getPostingDate());
            itemDTO.setItemStatus(status);
            itemDTO.setCreateTime(createTime);
            itemDTO.setCreateUserId(createUserId);
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setQty(this.getItemOperatedQty(itemDTO));
        }
        // 批量保存item
        bizReceiptReturnItemDataWrap.saveOrUpdateBatchDto(itemDTOList);
        /* ********************** item处理结束 *************************/
        /* ********************** bin处理开始 *************************/
        List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            Integer bid = 1;
            if(UtilCollection.isNotEmpty(itemDTO.getItemInfoList())) {
                for (BizReceiptReturnBinDTO binDTO : itemDTO.getItemInfoList()) {
                    binDTO.setId(null);
                    binDTO.setHeadId(headDTO.getId());
                    binDTO.setItemId(itemDTO.getId());
                    binDTO.setBid(Integer.toString(bid++));
                    binDTO.setCreateUserId(createUserId);
                    binDTO.setModifyUserId(user.getId());
                    if (UtilObject.isNotNull(binDTO.getBatchInfo())) {
                        binDTO.setIsSingle(binDTO.getBatchInfo().getIsSingle());
                        binDTO.setTagType(binDTO.getBatchInfo().getTagType());
                    }
                    binDTO.setPrintNum(binDTO.getPrintNum() == null
                            ? EnumDbDefaultValueInteger.BIZ_RECEIPT_RETURN_BIN_PRINT_NUM.getValue() : binDTO.getPrintNum());
                    binDTO.setPrintStatus(
                            binDTO.getPrintStatus() == null ? EnumRealYn.FALSE.getIntValue() : binDTO.getPrintStatus());
                    binDTO.setTagType(binDTO.getTagType() == null ? EnumTagType.GENERAL.getValue() : binDTO.getTagType());
                    binDTOList.add(binDTO);
                }
            }
        }
        bizReceiptReturnBinDataWrap.saveBatchDto(binDTOList);
        /* ********************** bin处理结束 *************************/
        // 上下文返回设置
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * 删除单据
     *
     * @param ctx 上下文
     */
    public void deleteReceipt(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        CurrentUser user = ctx.getCurrentUser();
        // 删除head
        BizReceiptReturnHeadDTO headDTO = this.getReceiptReturnHeadById(id);
        bizReceiptReturnHeadDataWrap.removeById(id);
        // 删除item
        bizReceiptReturnItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptReturnItem>() {

            {
                eq(BizReceiptReturnItem::getHeadId, id);
            }
        });
        // 删除bin
        bizReceiptReturnBinDataWrap.remove(new LambdaQueryWrapper<BizReceiptReturnBin>() {

            {
                eq(BizReceiptReturnBin::getHeadId, id);
            }
        });
        receiptOperationLogService.saveBizReceiptOperationLogList(id, headDTO.getReceiptType(),
            EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "", user.getId());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
    }

    /**
     * 获取行项目退库数
     *
     * @param itemDTO 行项目
     * @return 退库数量
     */
    public BigDecimal getItemOperatedQty(BizReceiptReturnItemDTO itemDTO) {
        BigDecimal operatedQty = BigDecimal.ZERO;
        if (Objects.nonNull(itemDTO) && itemDTO.getItemInfoList() != null) {
            for (BizReceiptReturnBinDTO binDTO : itemDTO.getItemInfoList()) {
                if (binDTO.getQty() != null && binDTO.getQty().compareTo(BigDecimal.ZERO) > 0) {
                    operatedQty = operatedQty.add(binDTO.getQty());
                }
            }
        }
        return operatedQty;
    }

    /**
     * 更新单据,行项目状态 如不更新单据状态，headDTO参数传null
     *
     * @param headDTO headDTO
     * @param itemDTOList 行项目列表
     * @param status 状态
     */
    public void updateStatus(BizReceiptReturnHeadDTO headDTO, List<BizReceiptReturnItemDTO> itemDTOList,
        Integer status) {
        if (Objects.isNull(headDTO)) {
            this.updateItemStatus(itemDTOList, status);
        } else if (CollectionUtils.isEmpty(itemDTOList)) {
            this.updateReceiptStatus(headDTO, status);
        } else if (!CollectionUtils.isEmpty(itemDTOList)) {
            this.updateItemStatus(itemDTOList, status);
            this.updateReceiptStatus(headDTO, status);
        }
    }

    /**
     * 更新行项目状态
     *
     * @param itemDTOList 行项目列表
     * @param status 状态
     */
    private void updateItemStatus(List<BizReceiptReturnItemDTO> itemDTOList, Integer status) {
        if (UtilCollection.isNotEmpty(itemDTOList)) {
            itemDTOList.forEach(item -> item.setItemStatus(status));
            bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
        }
    }

    /**
     * 更新单据状态
     *
     * @param headDTO headDTO
     * @param receiptStatus 单据状态
     */
    public void updateReceiptStatus(BizReceiptReturnHeadDTO headDTO, Integer receiptStatus) {
        if (Objects.nonNull(headDTO)) {
            // 单据状态
            headDTO.setReceiptStatus(receiptStatus);
            bizReceiptReturnHeadDataWrap.updateDtoById(headDTO);
        }
    }

    /**
     * 更新单据状态为已完成
     *
     * @param ctx 上下文
     */
    public void updateReceiptStatusCompleted(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        List<Integer> itemStatusList = this.getItemStatusList(headDTO.getId());
        if (bizCommonService.getCompletedItemStatusSet().containsAll(itemStatusList)) {
            // 所有行项目状态都是【已完成】或【冲销中】或【已冲销】时，修改单据状态为已完成
            this.updateReceiptStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            log.debug("退库单{}单据状态修改为【已完成】，成功", headDTO.getId());
        }
    }

    /**
     * 查询行项目状态itemStatus列表
     *
     * @param headId 单据id
     * @return 状态列表
     */
    public List<Integer> getItemStatusList(Long headId) {
        QueryWrapper<BizReceiptReturnItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptReturnItem::getHeadId, headId);
        List<BizReceiptReturnItem> list = bizReceiptReturnItemDataWrap.list(queryWrapper);
        List<Integer> statusList = new ArrayList<>();
        for (BizReceiptReturnItem bizReceiptReturnItem : list) {
            statusList.add(bizReceiptReturnItem.getItemStatus());
        }
        return statusList;
    }

    /**
     * 普通标签生成上架请求
     *
     * @in ctx 入参 {@link BizReceiptReturnHeadDTO : "退库单"}
     */
    public void generateLoadReq(BizContext ctx) {
        // 入参上下文
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTaskReqItemDTO> taskReqItemList = new ArrayList<>();
        for (BizReceiptReturnItemDTO returnItemDTO : headDTO.getItemDTOList()) {
            for (BizReceiptReturnBinDTO returnBinDTO : returnItemDTO.getItemInfoList()) {

                BizReceiptTaskReqItemDTO itemDTO = new BizReceiptTaskReqItemDTO();
                itemDTO.setPreReceiptType(headDTO.getReceiptType());
                itemDTO.setPreReceiptHeadId(headDTO.getId());
                itemDTO.setPreReceiptItemId(returnItemDTO.getId());
                itemDTO.setPreReceiptBinId(returnBinDTO.getId());
                itemDTO.setFtyId(returnItemDTO.getFtyId());
                itemDTO.setLocationId(returnItemDTO.getLocationId());
                itemDTO.setWhId(returnItemDTO.getWhId());
                itemDTO.setMatId(returnItemDTO.getMatId());
                itemDTO.setUnitId(returnItemDTO.getUnitId());
                itemDTO.setQty(returnBinDTO.getQty());
                itemDTO.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
                itemDTO.setBatchId(returnBinDTO.getBatchId());
                //itemDTO.setLabelReceiptRelDTOList(returnBinDTO.getLabelReceiptRelDTOList());
                taskReqItemList.add(itemDTO);

            }
        }
        BizReceiptTaskReqHeadDTO taskReqHeadDTO = new BizReceiptTaskReqHeadDTO();
        taskReqHeadDTO.setItemList(taskReqItemList);
        taskReqHeadDTO.setDes(headDTO.getRemark());
        taskReqHeadDTO.setReceiptType(EnumReceiptType.STOCK_TASK_REQ_SHELF_LOAD.getValue());
        if (EnumReceiptType.UNITIZED_STOCK_RETURN_TRANSFER.getValue().equals(headDTO.getReceiptType())) {
            taskReqHeadDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_TASK_REQ_SHELF_LOAD.getValue());
        }
        BizReceiptTaskReqSavePo po = new BizReceiptTaskReqSavePo();
        po.setStockTaskReqHeadInfo(taskReqHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, po);
        log.debug("基于退库入库单{}, receiptId={},组装参数，生成上架单", headDTO.getReceiptCode(), headDTO.getId());
        // MQ 生成上架请求
        RocketMQProducerProcessor.getInstance()
            .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.GENERATE_LOAD_REQ_TAGS, ctx));
    }

    /**
     * 再次过账，单据状态校验
     *
     * @param ctx 上下文
     */
    public void checkPost(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 参数基本校验
        this.check(ctx);
        // 再次过账或手动提交过账，单据必须是未同步状态或已作业状态
        BizReceiptReturnHead one = bizReceiptReturnHeadDataWrap.getById(headDTO.getId());
        Set<Integer> canRePostStatusSet = new HashSet<>();
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
        if (!canRePostStatusSet.contains(one.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
    }

    /**
     * 冲销校验
     *
     * @param ctx 上下文
     */
    public void checkWriteOff(BizContext ctx) {
        BizReceiptReturnWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptReturnHeadDTO headDTO = this.getItemListAndBinList(po.getHeadId(), po.getItemIds());
        headDTO.getItemDTOList().forEach(p -> p.setWriteOffPostingDate(po.getWriteOffPostingDate()).setWriteOffReason(po.getWriteOffReason()));
        Set<String> ridSet = new HashSet<>();
        // 冲销标识等于1或者没有物料凭证不能冲销
        for (BizReceiptReturnItemDTO itemDTO : headDTO.getItemDTOList()) {
            if (itemDTO.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())
                || UtilString.isNullOrEmpty(itemDTO.getMatDocCode())) {
                ridSet.add(itemDTO.getRid());
            }
        }
        if (ridSet.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, ridSet.toString());
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, headDTO);
    }

    /**
     * 校验删除
     *
     * @param ctx 上下文
     */
    public void checkDelete(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilObject.isNull(id)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 判断退库单是否已同步SAP
        boolean syncStatus = this.getSapPostSyncStatus(id);
        if (syncStatus) {
            log.warn("退库单{}删除状态校验，失败，退库单已同步SAP，存在SAP过账物料凭证", id);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
        // 判断退库单生成作业请求是否已经开始作业
        byte taskStatus = this.getReceiptTaskStatus(id);
        boolean isTask = !EnumReceiptTaskStatus.NOT_STARTED.getValue().equals(taskStatus);
        if (isTask) {
            log.warn("退库单{}删除状态校验，失败，退库单已有已作业的行项目", id);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_HAS_TASK);
        }
    }

    /**
     * 根据物料凭证计算库存是否可以正常修改
     *
     * @param stockInsMoveTypePostTaskDTO 移动类型
     */
    public void checkAndComputeForModifyStock(StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO) {
        try {
            if (Objects.nonNull(stockInsMoveTypePostTaskDTO)) {
                if (Objects.nonNull(stockInsMoveTypePostTaskDTO.getPostDTO())) {
                    stockCommonService.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO.getPostDTO());
                }
                if (Objects.nonNull(stockInsMoveTypePostTaskDTO.getTaskDTO())) {
                    stockCommonService.checkAndComputeForModifyStock(stockInsMoveTypePostTaskDTO.getTaskDTO());
                }
            }
        } catch (WmsException e) {
            log.warn("库存数量校验失败，错误码：{}，错误码参数：{}", e.getErrorCode(), e.getArgs());
            throw new WmsException(e.getErrorCode());
        }
    }

    /**
     * 设置出库单对应预留单信息
     *
     * @param ctx 上下文
     */
    public void setReserveInfo(BizContext ctx) {
        // 获取上下文
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取出库单信息
        QueryWrapper<BizReceiptOutputItem> outputItemQueryWrapper = new QueryWrapper<>();
        outputItemQueryWrapper.lambda().in(BizReceiptOutputItem::getId, headDTO.getItemDTOList().stream().map(p -> p.getReferReceiptItemId()).collect(Collectors.toList()));
        List<BizReceiptOutputItemDTO> outputItemDTOList = UtilCollection.toList(bizReceiptOutputItemDataWrap.list(outputItemQueryWrapper), BizReceiptOutputItemDTO.class);
        dataFillService.fillRlatAttrDataList(outputItemDTOList);
        Long preReceiptHeadId = outputItemDTOList.get(0).getPreReceiptHeadId();
        // 根据前续单据id查询申请表的领料信息
        BizReceiptApplyHead receiptApplyHead = bizReceiptApplyHeadDataWrap.getById(preReceiptHeadId);
        if (receiptApplyHead!=null  && EnumReceiveType.PRODUCE_REQ_USE.getValue().equals(receiptApplyHead.getReceiveType())) {//需求计划领用
            // 属性填充
            BizReceiptOutputInfo bizReceiptOutputInfo = bizReceiptOutputInfoDataWrap.getById(receiptApplyHead.getOutInfoId());
            headDTO.getItemDTOList().forEach(p -> {
                outputItemDTOList.forEach(q -> {
                    if(p.getReferReceiptItemId().equals(q.getId())) {
                        p.setReserveReceiptCode(UtilObject.getStringOrEmpty(q.getReservedOrderCode()) );
                        p.setReserveReceiptRid(q.getReservedOrderRid());//预留单行项目号
                        p.setReceiptNum(bizReceiptOutputInfo.getReceiptNum()); //领料单号
                    }
                });
            });
        }else {
            headDTO.getItemDTOList().forEach(p -> {
                outputItemDTOList.forEach(q -> {
                    if(p.getReferReceiptItemId().equals(q.getId())) {
                        p.setReserveReceiptCode(q.getReferReceiptCode());
                        p.setReserveReceiptRid(q.getReferReceiptRid());
                    }
                });
            });
        }
    }


    /**
     * sap过账
     *
     * @param ctx 上下文
     */
    public void postToSap(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();

        // 过滤没有sap凭证的行项目
        List<BizReceiptReturnItemDTO> syncList = headDTO.getItemDTOList().stream()
            .filter(item -> !StringUtils.hasText(item.getMatDocCode())).collect(Collectors.toList());
        if (UtilCollection.isEmpty(syncList)) {
            return;
        }
        headDTO.setItemDTOList(syncList);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 更新过账时间
        syncList.forEach(p -> p.setPostingDate(headDTO.getPostingDate()));
        this.updatePostingDate(ctx);

        HXPostingHeader header = this.materialReturnPosting(headDTO, syncList, false);

        HXPostingReturn returnObj = hxInterfaceService.posting(header,false);

        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
            // 过账成功，修改退库单SAP物料凭证字段
            // 调用SAP
            if (UtilConst.getInstance().isErpSyncMode()) {
                this.updateItemAfterSyncSapNew(syncList, returnObj);
            }else {
                this.updateItemAfterNoSyncSapNew(syncList, returnObj);
            }
            // 更新单据行项目状态已记账
            this.updateStatus(headDTO, syncList, EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            List<Long> itemIdList = syncList.stream().map(BizReceiptReturnItemDTO::getId).collect(Collectors.toList());
            log.debug("退库单{}行项目{}过账同步SAP成功", headDTO.getReceiptCode(), itemIdList.toString());
        } else {
            // 失败时，更新退库单及行项目为【未同步】状态
            log.warn("退库单{}过账同步SAP失败", headDTO.getReceiptCode());
            this.updateStatus(headDTO, syncList, EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, returnObj.getReturnMessage());
        }
    }

    /**
     * 领料退库单过账或者冲销入参
     */
    private HXPostingHeader materialReturnPosting(BizReceiptReturnHeadDTO headDTO, List<BizReceiptReturnItemDTO> itemList, boolean isWriteOff) {
        HXPostingHeader header = new HXPostingHeader();
        header.setReceiptId(headDTO.getId());
        header.setReceiptCode(headDTO.getReceiptCode());
        header.setReceiptType(headDTO.getReceiptType());
        if (isWriteOff) {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getWriteOffDocDate()));
        } else {
            header.setPostingDate(UtilDate.convertDateToDateStr(itemList.get(0).getPostingDate()));
            header.setDocDate(UtilDate.convertDateToDateStr(itemList.get(0).getDocDate()));
        }

        List<HXPostingItem> items = itemList.stream().map(returnItem -> {
            HXPostingItem item = new HXPostingItem();
            item.setReceiptRid(returnItem.getRid());
            item.setFtyCode(returnItem.getFtyCode());
            item.setLocationCode1(returnItem.getLocationCode());
            item.setMatCode(returnItem.getMatCode());
            item.setQty(UtilBigDecimal.getString(returnItem.getQty()));
            item.setUnitCode(returnItem.getUnitCode());

            if (isWriteOff && 1 == returnItem.getReceiveType()) {
                item.setMoveType(EnumMoveType.TYPE_COST_CENTER_PICKING.getValue());
            }
            if (isWriteOff && 2 == returnItem.getReceiveType()) {
                item.setMoveType(EnumMoveType.TYPE_COST_WBS_PICKING.getValue());
            }

            if (!isWriteOff && 1 == returnItem.getReceiveType()) {
                item.setMoveType(EnumMoveType.TYPE_COST_CENTER_RETURN.getValue());
            }
            if (!isWriteOff && 2 == returnItem.getReceiveType()) {
                item.setMoveType(EnumMoveType.TYPE_COST_WBS_RETURN.getValue());
            }
            // 资产领用-退库入库过账类型为242
            if (isWriteOff && 4 == returnItem.getReceiveType()) {
                item.setMoveType(EnumMoveType.TYPE_ASSET_WRITE_OFF.getValue());
            }
            // 资产领用-退库入库冲销为241
            if (!isWriteOff && 4 == returnItem.getReceiveType()) {
                item.setMoveType(EnumMoveType.TYPE_ASSET.getValue());
            }

            header.setReceiveType(returnItem.getReceiveType());
            item.setCostCenter(returnItem.getCostCenterCode());
            item.setWbsCode(returnItem.getWbsCode());
            return item;
        }).collect(Collectors.toList());

        header.setItems(items);

        return header;
    }

    /**
     * 更新过账时间
     *
     * @param ctx 上下文
     */
    public void updatePostingDate(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - ins凭证
        StockInsMoveTypePostTaskDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        List<BizReceiptReturnItemDTO> itemDTOList = headDTO.getItemDTOList();
        if (UtilCollection.isEmpty(itemDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ACCOUNT_SET_FAIL);
        }
        Date postingDate = itemDTOList.get(0).getPostingDate();
        Date writeOffPostingDate = itemDTOList.get(0).getWriteOffPostingDate();
        if (UtilObject.isNull(postingDate)) {
            postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        if (UtilObject.isNull(writeOffPostingDate)) {
            writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
        }
        // 判断过账日期是否在帐期内
        postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
        writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
        for (BizReceiptReturnItemDTO returnItemDTO : itemDTOList) {
            returnItemDTO.setDocDate(UtilDate.getNow());
            returnItemDTO.setPostingDate(postingDate);
            if(returnItemDTO.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())) {
                returnItemDTO.setWriteOffDocDate(UtilDate.getNow());
                returnItemDTO.setWriteOffPostingDate(writeOffPostingDate);
            }
            returnItemDTO.setQualified(headDTO.getQualified());
        }
        // 补全凭证的过账时间并更新退库单【sap过账标识、过账日期、凭证日期】
        if (UtilObject.isNotNull(insMoveTypeDTO)) {
            for (BizReceiptReturnItemDTO returnItemDTO : itemDTOList) {
                for (StockInsDocBatch stockInsDocBatch : insMoveTypeDTO.getPostDTO().getInsDocBatchList()) {
                    if (stockInsDocBatch.getPreReceiptItemId().equals(returnItemDTO.getId())) {
                        stockInsDocBatch.setPostingDate(returnItemDTO.getPostingDate());
                        stockInsDocBatch.setDocDate(returnItemDTO.getDocDate());
                        if(returnItemDTO.getIsWriteOff().equals(EnumRealYn.TRUE.getIntValue())) {
                            stockInsDocBatch.setPostingDate(writeOffPostingDate);
                            stockInsDocBatch.setDocDate(UtilDate.getNow());
                        }
                    }
                }
                returnItemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
            }
            bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
        }
        // 上下文返回参数- ins凭证
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 过账成功，修改退库单SAP物料凭证字段,年,凭证行号,凭证时间
     *
     * @param itemDTOList 行项目列表
     * @param returnObj sap返回结果
     */
    public void updateItemAfterSyncSapNew(List<BizReceiptReturnItemDTO> itemDTOList, HXPostingReturn returnObj) {
        List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            itemDTO.setMatDocCode(returnObj.getMatDocCode());
            itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(returnObj.getMatDocYear()));
        }
        bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
        bizReceiptReturnBinDataWrap.updateBatchDtoById(binDTOList);
    }

    /**
     * 过账成功，修改退库单SAP物料凭证字段,年,凭证行号,凭证时间（不调SAP）
     *
     * @param itemDTOList 行项目列表
     * @param retObj sap返回结果
     */
    public void updateItemAfterNoSyncSapNew(List<BizReceiptReturnItemDTO> itemDTOList, HXPostingReturn retObj) {
        int matDocRid = 1;
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            itemDTO.setMatDocCode(retObj.getMatDocCode());
            itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
            String matDocRidStr = UtilObject.getStringOrEmpty(matDocRid);
            itemDTO.setMatDocRid(matDocRidStr);
            itemDTO.setDocDate(new Date());
            itemDTO.setPostingDate(new Date());
            matDocRid++;
        }
        bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
    }

    /**
     * 过账成功，修改退库单SAP物料凭证字段,年,凭证行号,凭证时间
     *
     * @param itemDTOList 行项目列表
     * @param retObj sap返回结果
     */
    public void updateItemAfterSyncSap(List<BizReceiptReturnItemDTO> itemDTOList, ErpReturnObject retObj) {
        List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            for (BizReceiptReturnBinDTO binDTO : itemDTO.getItemInfoList()) {
                for(ErpReturnObjectItem returnObjectItem : retObj.getReturnItemList()) {
                    if(itemDTO.getReceiptCode().equals(returnObjectItem.getReceiptCode())
                            && itemDTO.getRid().equals(returnObjectItem.getReceiptRid())
                            && binDTO.getBid().equals(returnObjectItem.getReceiptBid())) {
                        itemDTO.setMatDocCode(returnObjectItem.getMatDocCode());
                        itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
                        itemDTO.setMatDocRid(returnObjectItem.getMatDocRid());
                        binDTO.setDmbtr(returnObjectItem.getDmbtr());
                        binDTOList.add(binDTO);
                        Date postingDate = itemDTO.getPostingDate();
                        Long batchId = binDTO.getBatchId();
                        if ((postingDate != null) && UtilNumber.isNotEmpty(batchId)) {
                            bizBatchInfoDataWrap.updateReturnDate(batchId, postingDate);
                        }
                    }
                }
            }
        }
        bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
        bizReceiptReturnBinDataWrap.updateBatchDtoById(binDTOList);
    }

    /**
     * 过账成功，修改退库单SAP物料凭证字段,年,凭证行号,凭证时间（不调SAP）
     *
     * @param itemDTOList 行项目列表
     * @param retObj sap返回结果
     */
    public void updateItemAfterNoSyncSap(List<BizReceiptReturnItemDTO> itemDTOList, ErpReturnObject retObj) {
        int matDocRid = 1;
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            itemDTO.setMatDocCode(retObj.getMatDocCode());
            itemDTO.setMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
            String matDocRidStr = UtilObject.getStringOrEmpty(matDocRid);
            itemDTO.setMatDocRid(matDocRidStr);
            itemDTO.setDocDate(new Date());
            itemDTO.setPostingDate(new Date());
            matDocRid++;
        }
        bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
    }

    /**
     * InStock过账
     *
     * @param ctx 上下文
     */
    @Transactional
    public void postToInStock(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        try {
            // 修改库存
            this.modifyStock(stockInsMoveTypePostTaskDTO);
            // 行项目及头状态已记账
            this.updateStatus(headDTO, headDTO.getItemDTOList(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            // 添加单据过账日志
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_POSTING, "", user.getId());
        } catch (WmsException e) {
            // 失败时更新退库单及行项目为未同步
            log.warn("退库单{}过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            this.updateStatus(headDTO, headDTO.getItemDTOList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            throw e;
        }
    }

    /**
     * 更新出库单已退库数量
     *
     * @param ctx
     */
    public void updateOutputReturnAQty(BizContext ctx) {
        ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.UPDATE_OUTPUT_RETURN_QTY, ctx);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }

    /**
     * 根据移动类型对象修改库存
     *
     * @param stockInsMoveTypePostTaskDTO 移动类型
     */
    public void modifyStock(StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO) {
        if (Objects.nonNull(stockInsMoveTypePostTaskDTO)) {
            if (Objects.nonNull(stockInsMoveTypePostTaskDTO.getPostDTO())) {
                stockCommonService.modifyStock(stockInsMoveTypePostTaskDTO.getPostDTO());
            }
            if (Objects.nonNull(stockInsMoveTypePostTaskDTO.getTaskDTO())) {
                stockCommonService.modifyStock(stockInsMoveTypePostTaskDTO.getTaskDTO());
            }
        }
    }

    /**
     * sap冲销
     *
     * @param ctx 上下文
     */
    public void writeOffToSap(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        headDTO.getItemDTOList().forEach(p -> p.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        // 放入上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        this.updatePostingDate(ctx);

        HXPostingHeader header = this.materialReturnPosting(headDTO, headDTO.getItemDTOList(), true);

        HXPostingReturn returnObj = hxInterfaceService.posting(header,true);

        if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
            // 冲销成功，修改退库单SAP物料凭证字段
            // 调用SAP
            if (UtilConst.getInstance().isErpSyncMode()) {
                this.updateItemAfterWriteOffSapNew(headDTO.getItemDTOList(), returnObj);
            }else {
                this.updateItemAfterWriteOffNoSapNew(headDTO.getItemDTOList(), returnObj);
            }
        } else {
            log.warn("退库单{}冲销同步SAP失败，返回信息：{}", headDTO.getId(), returnObj.getReturnMessage());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, returnObj.getReturnMessage());
        }
    }

    /**
     * 冲销成功，修改退库单SAP物料冲销凭证字段,年,冲销凭证行号
     *
     * @param itemDTOList 行项目列表
     * @param retObj sap返回结果
     */
    private void updateItemAfterWriteOffSapNew(List<BizReceiptReturnItemDTO> itemDTOList, HXPostingReturn retObj) {
        List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            itemDTO.setWriteOffMatDocCode(retObj.getMatDocCode());
            itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
        }
        bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
        bizReceiptReturnBinDataWrap.updateBatchDtoById(binDTOList);
    }

    /**
     * 冲销成功，修改退库单SAP物料冲销凭证字段,年,冲销凭证行号
     *
     * @param itemDTOList 行项目列表
     * @param retObj sap返回结果
     */
    private void updateItemAfterWriteOffNoSapNew(List<BizReceiptReturnItemDTO> itemDTOList, HXPostingReturn retObj) {
        int matDocRid = 1;
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            itemDTO.setWriteOffMatDocCode(retObj.getMatDocCode());
            itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
            String matDocRidStr = UtilObject.getStringOrEmpty(matDocRid);
            itemDTO.setWriteOffMatDocRid(matDocRidStr);
            matDocRid++;
        }
        bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
    }

    /**
     * 冲销成功，修改退库单SAP物料冲销凭证字段,年,冲销凭证行号
     *
     * @param itemDTOList 行项目列表
     * @param retObj sap返回结果
     */
    private void updateItemAfterWriteOffSap(List<BizReceiptReturnItemDTO> itemDTOList, ErpReturnObject retObj) {
        List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            for (BizReceiptReturnBinDTO binDTO : itemDTO.getItemInfoList()) {
                for(ErpReturnObjectItem returnObjectItem : retObj.getReturnItemList()) {
                    if(itemDTO.getReceiptCode().equals(returnObjectItem.getReceiptCode())
                            && itemDTO.getRid().equals(returnObjectItem.getReceiptRid())
                            && binDTO.getBid().equals(returnObjectItem.getReceiptBid())) {
                        itemDTO.setWriteOffMatDocCode(returnObjectItem.getMatDocCode());
                        itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
                        itemDTO.setWriteOffMatDocRid(returnObjectItem.getMatDocRid());
                        binDTO.setDmbtr(returnObjectItem.getDmbtr());
                        binDTOList.add(binDTO);
                    }
                }
            }
        }
        bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
        bizReceiptReturnBinDataWrap.updateBatchDtoById(binDTOList);
    }

    /**
     * 冲销成功，修改退库单SAP物料冲销凭证字段,年,冲销凭证行号
     *
     * @param itemDTOList 行项目列表
     * @param retObj sap返回结果
     */
    private void updateItemAfterWriteOffNoSap(List<BizReceiptReturnItemDTO> itemDTOList, ErpReturnObject retObj) {
        int matDocRid = 1;
        for (BizReceiptReturnItemDTO itemDTO : itemDTOList) {
            itemDTO.setWriteOffMatDocCode(retObj.getMatDocCode());
            itemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(retObj.getMatDocYear()));
            String matDocRidStr = UtilObject.getStringOrEmpty(matDocRid);
            itemDTO.setWriteOffMatDocRid(matDocRidStr);
            matDocRid++;
        }
        bizReceiptReturnItemDataWrap.updateBatchDtoById(itemDTOList);
    }

    /**
     * InStock冲销
     *
     * @param ctx 上下文
     */
    public void writeOffToInStock(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        StockInsMoveTypePostTaskDTO stockInsMoveTypePostTaskDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        CurrentUser user = ctx.getCurrentUser();
        List<BizReceiptReturnItemDTO> writeOffItemList = headDTO.getItemDTOList();
        try {
            this.modifyStock(stockInsMoveTypePostTaskDTO);
            // 修改冲销标识
            this.updateItemWriteOffStatus(writeOffItemList);
            // 行项目状态修改为【已冲销】
            this.updateStatus(null, writeOffItemList, EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
            // 单据抬头状态修改为【已完成】
            this.updateReceiptStatusCompleted(ctx);
            // 添加单据冲销日志
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF, "", user.getId());
        } catch (WmsException e) {
            // 更新行项目状态冲销中
            this.updateStatus(null, writeOffItemList, EnumReceiptStatus.RECEIPT_STATUS_WRITING_OFF.getValue());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 修改冲销标识
     *
     * @param writeOffItemList 冲销列表
     */
    private void updateItemWriteOffStatus(List<BizReceiptReturnItemDTO> writeOffItemList) {
        writeOffItemList.forEach(a -> a.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        bizReceiptReturnItemDataWrap.updateBatchDtoById(writeOffItemList);
    }

    /**
     * 推送冲销修改请求
     *
     * @param ctx 上下文
     */
    public void updateTaskRequest(BizContext ctx) {
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<Long> itemIds = headDTO.getItemDTOList().stream().map(BizReceiptReturnItemDTO::getId).collect(Collectors.toList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_IDS, itemIds);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(ProducerMessageContent.messageContent(TagConst.RECEIPT_WRITE_OFF_MODIFY_REQ_ITEM, ctx));
    }

    /**
     * 打印标签
     *
     * @param ctx
     */
    public void print(BizContext ctx) {
        // 入参上下文
        BizReceiptReturnHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if (UtilObject.isNotNull(po) && UtilCollection.isNotEmpty(po.getItemDTOList())) {
            // 装载要更新的配货信息
            List<BizReceiptReturnBinDTO> binDTOList = new ArrayList<>();
            // 装载打印机打印数据
            List<LabelBatch> labelBatchList = new ArrayList<>();
            for (BizReceiptReturnItemDTO itemDTO : po.getItemDTOList()) {
                for (BizReceiptReturnBinDTO binDTO : itemDTO.getItemInfoList()) {
                    // 打印状态
                    Integer printStatus = binDTO.getPrintStatus();
                    // 标签类型 0：普通标签 1：RFID抗金属 2：RFID非抗金属
                    Integer tagType = binDTO.getTagType();
                    // 单品/批次 0批次 1单品
                    Integer isSingle = binDTO.getIsSingle();
                    // bin对应标签关系
                    List<BizLabelReceiptRelDTO> labelReceiptRelDTOList = binDTO.getLabelReceiptRelDTOList();
                    // 更新已打印状态
                    binDTO.setPrintStatus(EnumRealYn.TRUE.getIntValue());
                    // 批次(非单品)+普通标签 不生成标签
                    if (EnumRealYn.FALSE.getIntValue().equals(isSingle)
                        && EnumTagType.GENERAL.getValue().equals(tagType)) {
                        // 设置标签打印数据
                        this.setPrintData(labelBatchList, new ArrayList<>(), itemDTO, binDTO, user);
                    } else {
                        // 设置标签打印数据
                        this.setPrintData(labelBatchList, labelReceiptRelDTOList, itemDTO, binDTO, user);
                    }
                    binDTOList.add(binDTO);
                }
            }
            /* *** 更新退库单【打印状态、打印份数】 *** */
            bizReceiptReturnBinDataWrap.updateBatchDtoById(binDTOList);
            log.info("退库单-打印-更新打印状态成功");
            // 推送MQ - 调用物料标签打印
            if (UtilCollection.isNotEmpty(labelBatchList)) {
                ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.PRINT_MAT_LABEL, labelBatchList);
                RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
            }
        }
    }

    /**
     * 设置标签主数据
     *
     * @param labelDataList
     * @param itemDTO
     * @param binDTO
     */
    private void setLabelData(List<BizLabelDataDTO> labelDataList, BizReceiptReturnItemDTO itemDTO,
        BizReceiptReturnBinDTO binDTO) {
        int num = 1;
        BigDecimal qty = binDTO.getQty();
        // 单品/批次 0批次 1单品
        Integer isSingle = binDTO.getBatchInfo().getIsSingle();
        if (EnumRealYn.TRUE.getIntValue().equals(isSingle)) {
            num = binDTO.getQty().intValue();
            qty = new BigDecimal(1);
        }
        for (int i = 0; i < num; i++) {
            BizLabelDataDTO label = new BizLabelDataDTO();
            // 生成标签编码
            String labelCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());
            // 设置标签数据
            UtilBean.copy(itemDTO, label);
            label.setId(null);
            label.setBatchId(binDTO.getBatchId());
            label.setLabelCode(labelCode);
            label.setSnCode(labelCode);
            label.setQty(qty);
            label.setLabelType(binDTO.getTagType());
            label.setReceiptHeadId(itemDTO.getHeadId());
            label.setReceiptItemId(itemDTO.getId());
            label.setReceiptBinId(binDTO.getId());
            label.setReceiptType(itemDTO.getReceiptType());
            label.setPreReceiptHeadId(itemDTO.getPreReceiptHeadId());
            label.setPreReceiptItemId(itemDTO.getPreReceiptItemId());
            label.setPreReceiptType(itemDTO.getPreReceiptType());
            labelDataList.add(label);
        }
    }

    /**
     * 设置标签打印数据
     *
     * @param labelBatchList 装载打印机打印数据
     * @param labelReceiptRelDTOList 标签关系数据
     * @param itemDTO 要打印的采购验收单行项目
     * @param user 当前用户
     */
    private void setPrintData(List<LabelBatch> labelBatchList, List<BizLabelReceiptRelDTO> labelReceiptRelDTOList,
        BizReceiptReturnItemDTO itemDTO, BizReceiptReturnBinDTO binDTO, CurrentUser user) {
        // 标签类型 0：普通标签 1：RFID抗金属 2：RFID非抗金属
        Integer tagType = binDTO.getTagType();
        // 单品/批次 0批次 1单品
        Integer isSingle = binDTO.getIsSingle();
        if (EnumRealYn.FALSE.getIntValue().equals(isSingle) && EnumTagType.GENERAL.getValue().equals(tagType)) {
            labelReceiptRelDTOList.add(new BizLabelReceiptRelDTO());
        }
        for (BizLabelReceiptRelDTO labelReceiptRelDTO : labelReceiptRelDTOList) {
            LabelBatch labelBatch = new LabelBatch().setMatCode(itemDTO.getMatCode()).setMatName(itemDTO.getMatName())
                .setPurchaseOrder(itemDTO.getReceiptCode()).setMaterialNeeds("采矿部李楼采矿场").setQrCode("1000000001 2")
                .setSupplier(itemDTO.getSupplierName()).setReceiver(user.getUserName())
                .setPrinterIp(binDTO.getPrinterIp()).setPrinterPort(binDTO.getPrinterPort())
                .setPrinterIsDefault(binDTO.getPrinterDefault()).setPrinterIsPortable(binDTO.getPrinterIsPortable());
            if (EnumRealYn.FALSE.getIntValue().equals(isSingle) && EnumTagType.GENERAL.getValue().equals(tagType)) {
                labelBatch.setRfidCode(Const.BATCH_GENERAL_LABEL_TAB);
                labelBatch.setQty(binDTO.getQty());
            } else {
                labelBatch.setRfidCode(labelReceiptRelDTO.getLabelCode());
                labelBatch.setQty(labelReceiptRelDTO.getQty());
            }
            for (int pNum = 0; pNum < binDTO.getPrintNum(); pNum++) {
                labelBatchList.add(labelBatch);
            }
        }
    }

    /**
     * 非普通标签生成码盘
     *
     * @in ctx 入参
     */
    public void savePalletSorting(BizContext ctx) {
        // 入参上下文
        BizReceiptReturnHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 定义是否都是普通标签
        boolean generalFlag = true;
        for (BizReceiptReturnItemDTO itemDTO : po.getItemDTOList()) {
            for (BizReceiptReturnBinDTO binDTO : itemDTO.getItemInfoList()) {
                if (!(EnumTagType.GENERAL.getValue().equals(binDTO.getTagType()))) {
                    generalFlag = false;
                    break;
                }
            }
        }
        // 存在非普通标签 生成码盘数据
        if (!generalFlag) {
            palletSortingService.insertPalletSorting(po);
        }
    }

    /**
     * 生成标签 生成标签单据关系
     *
     * @param ctx
     */
    public void saveLabelData(BizContext ctx) {
        // 入参上下文
        BizReceiptReturnHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(po) && UtilCollection.isNotEmpty(po.getItemDTOList())) {
            // 装载要保存的标签数据集合
            List<BizLabelDataDTO> allLabelDataList = new ArrayList<>();
            for (BizReceiptReturnItemDTO itemDTO : po.getItemDTOList()) {
                for (BizReceiptReturnBinDTO binDTO : itemDTO.getItemInfoList()) {
                    // 标签类型 0：普通标签 1：RFID抗金属 2：RFID非抗金属
                    Integer tagType = binDTO.getTagType();
                    // 单品/批次 0批次 1单品
                    Integer isSingle = binDTO.getIsSingle();
                    // bin对应标签主数据
                    List<BizLabelDataDTO> labelDataList = new ArrayList<>();
                    // 批次(非单品)+普通标签 不生成标签
                    if (!(EnumRealYn.FALSE.getIntValue().equals(isSingle)
                        && EnumTagType.GENERAL.getValue().equals(tagType))) {
                        // 生成标签
                        itemDTO.setReceiptType(po.getReceiptType());
                        this.setLabelData(labelDataList, itemDTO, binDTO);
                        allLabelDataList.addAll(labelDataList);
                    }
                }
            }
            /* *** 插入标签数据及关联属性 *** */
            if (UtilCollection.isNotEmpty(allLabelDataList)) {
                labelDataService.saveBatchDto(allLabelDataList);
                log.info("退库单-提交-插入标签数据成功 " + JSONObject.toJSONString(allLabelDataList));
                List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
                for (BizLabelDataDTO label : allLabelDataList) {
                    BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                    bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                    bizLabelReceiptRel.setId(null);
                    bizLabelReceiptRel.setLabelId(label.getId());
                    bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_CODE.getValue());
                    bizLabelReceiptRelList.add(bizLabelReceiptRel);
                }
                labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
                log.info("退库单-提交-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
                for (BizReceiptReturnItemDTO itemDTO : po.getItemDTOList()) {
                    for (BizReceiptReturnBinDTO binDTO : itemDTO.getItemInfoList()) {
                        List<BizLabelReceiptRel> labelReceiptRelList = bizLabelReceiptRelList.stream()
                            .filter(a -> a.getReceiptBinId().equals(binDTO.getId())).collect(Collectors.toList());
                        binDTO.setLabelReceiptRelDTOList(
                            UtilCollection.toList(labelReceiptRelList, BizLabelReceiptRelDTO.class));
                    }
                }
            }
        }
    }


    /**
     * 更新批次入库时间
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void updateInputDate(BizContext ctx) {
        // 入参上下文 - 退库单
        BizReceiptReturnHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(headDTO) && UtilCollection.isNotEmpty(headDTO.getItemDTOList())) {
            List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
            for (BizReceiptReturnItemDTO returnItemDTO : headDTO.getItemDTOList()) {
                if(UtilCollection.isNotEmpty(returnItemDTO.getItemInfoList())) {
                    for(BizReceiptReturnBinDTO returnBinDTO : returnItemDTO.getItemInfoList()) {
                        /* ******** 更新批次信息的入库时间 ******** */
                        BizBatchInfoDTO bizBatchInfo = returnBinDTO.getBatchInfo();
                        bizBatchInfo.setId(returnBinDTO.getBatchId());
                        bizBatchInfo.setInputDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
                        bizBatchInfo.setMaintenanceDate(returnItemDTO.getDocDate());
                        bizBatchInfoDTOList.add(bizBatchInfo);
                    }
                }
            }
            if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
                bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            }
        }
    }

}
