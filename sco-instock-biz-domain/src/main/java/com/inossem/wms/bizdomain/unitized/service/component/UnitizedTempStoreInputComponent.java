package com.inossem.wms.bizdomain.unitized.service.component;

import cn.hutool.core.date.DateUtil;
//import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptOfficeDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.spec.service.biz.BizSpecFeatureValueService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.apply.service.datawrap.BizReceiptApplyHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.component.inputbase.InputComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.TempStoreInputMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.TempStoreInputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.output.service.datawrap.BizReceiptOutputItemDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.spec.EnumSpecClassifyType;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizdomain.apply.dto.BizReceiptApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.apply.entity.BizReceiptApplyHead;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadCallbackVO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.output.dto.BizReceiptOutputItemDTO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqItemDTO;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqItem;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqSavePo;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.po.BizReceiptPreSearchPO;
import com.inossem.wms.common.model.file.img.entity.BizCommonImage;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptDTO;
import com.inossem.wms.common.model.masterdata.base.entity.DicDept;
import com.inossem.wms.common.model.masterdata.base.entity.DicDeptOffice;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.file.service.datawrap.BizCommonImageDataWrap;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 暂存入库组件库
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */

@Service
@Slf4j
public class UnitizedTempStoreInputComponent {

    @Autowired
    private BizReceiptTaskReqItemDataWrap bizReceiptTaskReqItemDataWrap;
    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private InputComponent inputComponent;

    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;

    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    @Autowired
    private BizReceiptOutputItemDataWrap bizReceiptOutputItemDataWrap;

    @Autowired
    private TempStoreInputMoveTypeComponent tempStoreInputMoveTypeComponent;

    @Autowired
    private TempStoreInputWriteOffMoveTypeComponent tempStoreInputWriteOffMoveTypeComponent;

    @Autowired
    protected BatchInfoService bizBatchInfoService;
    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;
    @Autowired
    private LabelDataService labelDataService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private BizSpecFeatureValueService bizSpecFeatureValueService;
    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;
    @Autowired
    private DicDeptOfficeDataWrap dicDeptOfficeDataWrap;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private BizCommonImageDataWrap bizCommonImageDataWrap;


    /**
     * 页面初始化: 1、设置暂存入库【单据类型、创建时间、创建人】 2、设置按钮权限【提交、保存】 3、设置扩展功能【单据流】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"暂存入库","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptInputHeadDTO> resultVO = new BizResultVO<>(
            new BizReceiptInputHeadDTO()
                    .setReceiptType(EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_INPUT.getValue())
                    .setDeptId(ctx.getCurrentUser().getUserDeptList().get(0).getDeptId())
                    .setDeptName(ctx.getCurrentUser().getUserDeptList().get(0).getDeptName())
                    .setDeptOfficeId(ctx.getCurrentUser().getUserDeptList().get(0).getDeptOfficeId())
                    .setDeptOfficeName(ctx.getCurrentUser().getUserDeptList().get(0).getDeptOfficeName())
                    .setCreateTime(UtilDate.getNow())
                    .setCreateUserName(ctx.getCurrentUser().getUserName()),
            new ExtendVO().setRelationRequired(true), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 暂存入库单-分页
     *
     * @in ctx 入参 {@link BizReceiptInputSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO<BizReceiptInputHeadDTO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptInputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        //当前用户的库存地点集合集合
        CurrentUser currentUser = ctx.getCurrentUser();
        if (currentUser == null) {
            return;
        }
        if (UtilCollection.isEmpty(currentUser.getLocationList())) {
            return;
        }
        List<Long> locationIds = currentUser.getLocationList().stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());

        // 组装查询条件
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = this.setQueryWrapper(po, locationIds);
        // 分页处理
        IPage<BizReceiptInputHeadVO> page = po.getPageObj(BizReceiptInputHeadVO.class);
        // 获取暂存入库单
        bizReceiptInputHeadDataWrap.getOtherInputList(page, wrapper, EnumDataFillType.FILL_RLAT);
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 暂存入库单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"暂存入库单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取暂存入库单
        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizReceiptInputHeadDTO =
            UtilBean.newInstance(bizReceiptInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizReceiptInputHeadDTO);

        // 填充暂存申请中的申请部门和申请科室信息
        this.fillStoreDeptAndOffice(bizReceiptInputHeadDTO);

        inputComponent.setPrintInfo(bizReceiptInputHeadDTO);

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(bizReceiptInputHeadDTO);
        // 设置暂存入库单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptInputHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 暂存入库单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"暂存入库单详情","button":"按钮组")}
     */
    public void getPrintInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取暂存入库单
        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizReceiptInputHeadDTO =
            UtilBean.newInstance(bizReceiptInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizReceiptInputHeadDTO);

        // 填充暂存申请中的申请部门和申请科室信息
        this.fillStoreDeptAndOffice(bizReceiptInputHeadDTO);

        inputComponent.setPrintInfo(bizReceiptInputHeadDTO);

        // 仓储承包商：取上架单提交人所属的部门信息、接收：取上架单提交人的电子签名，日期显示上架单完成的日期；
        WmsLambdaQueryWrapper<BizReceiptTaskReqItem> taskReqQueryWrapper = new WmsLambdaQueryWrapper<>();
        taskReqQueryWrapper.eq(BizReceiptTaskReqItem::getPreReceiptHeadId, bizReceiptInputHead.getId());
        taskReqQueryWrapper.orderByDesc(BizReceiptTaskReqItem::getModifyTime).last("limit 1");
        BizReceiptTaskReqItem taskReqItem = bizReceiptTaskReqItemDataWrap.getOne(taskReqQueryWrapper, false);
        if (UtilObject.isNotNull(taskReqItem)) {
            SysUser modifyUser = dictionaryService.getSysUserCacheById(taskReqItem.getModifyUserId());
            if (UtilObject.isNotNull(modifyUser)) {
                // 取上架单提交人所属的部门信息
                MetaDataDeptOfficePO po = new MetaDataDeptOfficePO();
                po.setUserId(modifyUser.getId());
                List<DicDeptDTO> deptOfficeTree = dicDeptDataWrap.getUserDeptOfficeTree(po);
                if (UtilCollection.isNotEmpty(deptOfficeTree)) {
                    bizReceiptInputHeadDTO.setTaskReqSubmitUserDeptName(deptOfficeTree.get(0).getDeptName());
                }
                // 取上架单提交人的电子签名
                BizCommonImage commonImage = bizCommonImageDataWrap.getById(modifyUser.getCommonImgId());
                if (UtilObject.isNotNull(commonImage)) {
                    String imgData = "<img style='width: 90px; height: 45px;object-fit: scale-down;' src=" + commonImage.getImgBase64() + ">";
                    bizReceiptInputHeadDTO.setTaskReqSubmitUserAutograph(imgData);
                    bizReceiptInputHeadDTO.setTaskReqSubmitUserAutographData(commonImage.getImgBase64());
                }
                // 取上架单完成的日期
                bizReceiptInputHeadDTO.setTaskReqModifyTime(taskReqItem.getModifyTime());
            }
        }

        // 设置暂存入库单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptInputHeadDTO, new ExtendVO(), new ButtonVO()));
    }

    /**
     * 开启审批
     *
     * @in ctx 入参 {@link BizResultVO (head":"入库","extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO (head":"入库及单审批信息","extend":"扩展功能开启审批")}
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);

        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);

        boolean wfByReceiptType = EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_INPUT.getValue().equals(bizReceiptInputHead.getReceiptType());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 填充暂存申请中的申请部门和申请科室信息
     * @param BizReceiptInputHeadDTO
     */
    private void fillStoreDeptAndOffice(BizReceiptInputHeadDTO inputHeadDTO) {
        if (UtilCollection.isEmpty(inputHeadDTO.getItemList())) {
            return;
        }
        DicDept dept = dicDeptDataWrap.getById(inputHeadDTO.getDeptId());
        if (UtilObject.isNotNull(dept)) {
            inputHeadDTO.setDeptCode(dept.getDeptCode());
            inputHeadDTO.setDeptName(dept.getDeptName());
        }
        DicDeptOffice deptOffice = dicDeptOfficeDataWrap.getById(inputHeadDTO.getDeptOfficeId());
        if (UtilObject.isNotNull(deptOffice)) {
            inputHeadDTO.setDeptOfficeCode(deptOffice.getDeptOfficeCode());
            inputHeadDTO.setDeptOfficeName(deptOffice.getDeptOfficeName());
        }
    }

    /**
     * 按钮组
     *
     * @param headDTO 入库单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptInputHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(receiptStatus)) {
            // 已提交 -【删除】
            return buttonVO.setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {
            // 已记账 -【打印】
            return buttonVO.setButtonPrint(true).setButtonDeal(checkIsGenerateLoadReq(headDTO));
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus)) {
            // 作业中 -
            return buttonVO;
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 已完成 -【打印】
//            List<BizReceiptInputItemDTO> itemList = this.getItemListById(headDTO.getId()).getItemList();
            return buttonVO.setButtonPrint(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 -【过账】
            return buttonVO.setButtonPost(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus)) {
            // 已作业 -【过账】
            return buttonVO.setButtonPost(true);
        }
        return buttonVO;
    }

    /**
     * 查询是否生成上架请求单
     * @param headDTO
     * @return
     */
    public boolean checkIsGenerateLoadReq(BizReceiptInputHeadDTO headDTO) {
        boolean flag=false ;
        QueryWrapper<BizReceiptTaskReqItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTaskReqItem::getPreReceiptHeadId,headDTO.getId());
        List<BizReceiptTaskReqItem> itemList = bizReceiptTaskReqItemDataWrap.list(queryWrapper);
        if(UtilCollection.isEmpty(itemList)) {
            flag=true;
        }
        return flag;
    }

    /**
     * 保存暂存入库前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存的暂存入库单}
     */
    public void checkSaveOtherInput(BizContext ctx) {
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头字段必填
        if (UtilObject.isNull(po)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        if (UtilNumber.isEmpty(po.getUnit())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, "请选择机组");
        }
        // 校验行项目是都为空
        inputComponent.checkEmptyItem(po);
    }

    /**
     * 提交暂存入库单前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的暂存入库单}
     */
    public void checkSubmitOtherInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头字段必填
        if (UtilObject.isNull(po)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        if (UtilNumber.isEmpty(po.getUnit())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR_WITH_DESC, "请选择机组");
        }
        /* ******** 校验行项目是都为空 ******** */
        inputComponent.checkEmptyItem(po);
        /* ******** 校验暂存入库单行项目相关数量开始 ******** */
        inputComponent.checkEmptyItemQty(po);
        /* ******** 提交前校验物料、库存地点是否冻结 ******** */
        inputComponent.checkFreeze(po);
    }

    /**
     * 提交暂存入库单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的暂存入库单}
     * @out ctx 出参 {"stockInputCode" : "暂存入库单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitTempStoreInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存暂存入库单
        inputComponent.saveInput(ctx);
    }

    /**
     * 暂存入库过账前数据校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要过账的暂存入库单}
     */
    public void checkOtherInputPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取入库单
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getId());
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性及父子属性
        dataFillService.fillAttr(inputHeadDTO);
        // 校验数据
        inputComponent.checkEmptyItem(inputHeadDTO);
        // 校验可过账的状态
        Set<Integer> canRePostStatusSet = new HashSet<>();
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
        if (!canRePostStatusSet.contains(inputHeadDTO.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_POST);
        }
    }

    /**
     * 生成ins凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "暂存入库单}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证}
     */
    public void generateInsDocToPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成ins凭证
            insMoveTypeDTO = tempStoreInputMoveTypeComponent.generateInsDocToPost(headDTO);
            // 过账前的校验和数量计算
            stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        } catch (Exception e) {
            log.error("入库单{}生成ins凭证，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 更新单据 - 未同步
            inputComponent.updateStatus(headDTO, headDTO.getItemList(),
                EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            if (e instanceof WmsException) {
                throw new WmsException(((WmsException)e).getErrorCode(), ((WmsException)e).getArgs());
            } else {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
            }
        }
        // 设置ins凭证到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 暂存入库冲销前校验
     *
     * @in ctx 入参 {@link BizReceiptInputWriteOffPO : "暂存入库冲销入参"}
     * @out ctx 出参 {@link BizReceiptInputHeadDTO : "暂存入库单"}
     */
    public void checkOtherInputWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) && UtilNumber.isEmpty(po.getHeadId()) && UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取入库单行项目
        List<BizReceiptInputItem> inputItemList = bizReceiptInputItemDataWrap.listByIds(po.getItemIds());
        // 转dto
        List<BizReceiptInputItemDTO> inputItemDTOList =
            UtilCollection.toList(inputItemList, BizReceiptInputItemDTO.class);
        // 数据填充
        dataFillService.fillAttr(inputItemDTOList);
        // 校验行项目单据类型及状态
        Set<String> ridSet = inputItemDTOList.stream().filter(e -> !this.itemCanWriteOff(e))
            .map(BizReceiptInputItemDTO::getRid).collect(Collectors.toSet());
        if (ridSet.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, ridSet.toString());
        }
        // 冲销标识等于1或者过账标识等于0
        Set<String> isWriteOff = inputItemDTOList.stream()
            .filter(e -> EnumRealYn.TRUE.getIntValue().equals(e.getIsWriteOff())
                || EnumRealYn.FALSE.getIntValue().equals(e.getIsPost()))
            .map(BizReceiptInputItemDTO::getRid).collect(Collectors.toSet());
        if (isWriteOff.size() > 0) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF, isWriteOff.toString());
        }
        // 获取入库单
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getHeadId());
        // 转dto
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 设置冲销标识
        inputItemDTOList.forEach(itemDTO -> itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
        inputHeadDTO.setItemList(inputItemDTOList);
        // 设置要冲销的暂存入库单到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, inputHeadDTO);
    }

    /**
     * 生成ins冲销过账凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "暂存入库冲销入参"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void generateInsDocToPostWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 装载凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成凭证
            insMoveTypeDTO = tempStoreInputWriteOffMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 暂存入库单上架回调校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO:"入库单上架回调入参"}
     */
    public void checkTaskOtherInputCallback(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 校验入参
        if (null == vo) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验行项目
        if (UtilCollection.isEmpty(vo.getInputItemCallbackVoList())) {
            inputComponent.inputHeadCallbackToFail(vo);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 校验单据类型
        if (!EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_INPUT.getValue().equals(vo.getReceiptType())) {
            inputComponent.inputHeadCallbackToFail(vo);
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_FALSE);
        }
    }

    /**
     * 删除校验
     *
     * @in ctx 入参 {@link Long : "暂存入库单删除入参"}
     * @out ctx 出参 {@link BizReceiptInputDeletePO : "入库单删除上下文对象"}
     */
    public void checkDeleteOtherInput(BizContext ctx) {
        // 从上下文获取抬头表id
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        // 获取入库单信息
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(inputHeadDTO);
        /* ******** 校验暂存入库单head ******** */
        if (UtilObject.isNotNull(inputHead)) {
            if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(inputHead.getReceiptStatus())
                && !EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(inputHead.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        // 设置上下文对象
        BizReceiptInputDeletePO po = new BizReceiptInputDeletePO();
        po.setDeleteAll(Boolean.TRUE);
        po.setHeadId(headId);
        po.setReceiptType(inputHead.getReceiptType());
        po.setItemIds(inputHeadDTO.getItemList().stream().map(BizReceiptInputItemDTO::getId).collect(Collectors.toList()));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, po);
    }

    /**
     * 删除暂存入库单
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "删除入参"}
     */
    public void deleteOtherInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除暂存入库单 ******** */
        if (po.isDeleteAll()) {
            // 删除暂存入库单head
            bizReceiptInputHeadDataWrap.removeById(po.getHeadId());
            // 删除暂存入库单item
            UpdateWrapper<BizReceiptInputItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptInputItem::getHeadId, po.getHeadId());
            bizReceiptInputItemDataWrap.remove(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                po.getReceiptType(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                ctx.getCurrentUser().getId());
        } else {
            // 删除暂存入库单item
            bizReceiptInputItemDataWrap.removeByIds(po.getItemIds());
        }
    }

    /**
     * 冲销行项目校验
     *
     * @param inputItemDTO BizOtherInputItemDTO
     * @return true/false
     */
    public boolean itemCanWriteOff(BizReceiptInputItemDTO inputItemDTO) {
        Integer receiptType = inputItemDTO.getReceiptType();
        Integer receiptStatus = inputItemDTO.getReceiptStatus();
        return (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus))
            && EnumReceiptType.STOCK_INPUT_OTHER.getValue().equals(receiptType);
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return QueryWrapper<BizStockInputHead>
     */
    private WmsQueryWrapper<BizReceiptInputSearchPO> setQueryWrapper(BizReceiptInputSearchPO po,List<Long> locationIds) {
        if (null == po) {
            po = new BizReceiptInputSearchPO();
        }
        Date postCreateTime = null;
        if (UtilObject.isNotNull(po.getPostCreateTime())) {
            postCreateTime = UtilLocalDateTime.getStartTime(po.getPostCreateTime());
        }
        Date postEndTime = null;
        if (UtilObject.isNotNull(po.getPostEndTime())) {
            postEndTime = UtilLocalDateTime.getEndTime(po.getPostEndTime());
        }
        // 查询条件设置
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = new WmsQueryWrapper<>();
        // 入库单据号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
            BizReceiptInputHead.class, po.getReceiptCode());
        // 单据类型
        wrapper.lambda().eq(Boolean.TRUE, BizReceiptInputSearchPO::getReceiptType, BizReceiptInputHead.class,
            EnumReceiptType.UNITIZED_STOCK_TEMP_MAT_INPUT.getValue());
        // 单据状态
        wrapper.lambda().in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),
            BizReceiptInputSearchPO::getReceiptStatus, BizReceiptInputHead.class, po.getReceiptStatusList());
        // 凭证创建时间
        wrapper.lambda().between((UtilObject.isNotNull(postCreateTime)), BizReceiptInputSearchPO::getDocDate,
            BizReceiptInputItem.class, postCreateTime, postEndTime);
        // 物料凭证号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatDocCode()), BizReceiptInputSearchPO::getMatDocCode,
            BizReceiptInputItem.class, po.getMatDocCode());
        // 物料编码
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptInputSearchPO::getMatCode,
                DicMaterial.class, po.getMatCode());
        // 物料描述
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatName()), BizReceiptInputSearchPO::getMatName,
                DicMaterial.class, po.getMatName());
        // 创建人
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptInputSearchPO::getUserName, SysUser.class, po.getCreateUserName());
        // 创建时间
        wrapper.lambda().between(createTimeIsNotNull(po), BizReceiptInputSearchPO::getCreateTime, BizReceiptInputHead.class, po.getStartTime(), po.getEndTime());
        // 库存地点
        wrapper.lambda().in(UtilCollection.isNotEmpty(locationIds), BizReceiptInputSearchPO::getLocationId, BizReceiptInputItem.class, locationIds);
        // 单据描述
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getRemark()), BizReceiptInputSearchPO::getRemark, BizReceiptInputItem.class, po.getRemark());
        return wrapper.setEntity(po);
    }

    private boolean createTimeIsNotNull(BizReceiptInputSearchPO po){
        return Objects.nonNull(po.getStartTime()) && Objects.nonNull(po.getEndTime());
    }

    /**
     * 根据headId查询出库单列表
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptInputHeadDTO getItemListById(Long headId) {
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(headId);
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        if (UtilString.isNullOrEmpty(po.getBizLabelPrintDTO().getPrinterIp())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_IP_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrinterPort())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PORT_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptInputHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }

    /**
     * 填充打印数据
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();
        // 装载打印机打印数据
        List<LabelReceiptInputBox> receiptInputBoxes = new ArrayList<>();
        // 装载要更新的批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptInputHeadDTO headDTO = (BizReceiptInputHeadDTO) po.getHeadDTO();
        // 新建打印实体对象
        List<BizReceiptInputItemDTO> itemDTOList = headDTO.getItemList();

        itemDTOList.forEach(itemDTO->{
            // 生成标签编码
            String labelCode =
                    bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());

            // 标签打印数据
            this.setPrintLabelData(receiptInputBoxes, itemDTO, labelCode);

            // 设置要更新的批次信息数据
            itemDTO.getBizBatchInfoDTO().setInspectHeadId(itemDTO.getHeadId());
            itemDTO.getBizBatchInfoDTO().setInspectItemId(itemDTO.getId());
            itemDTO.getBizBatchInfoDTO()
                    .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
            itemDTO.getBizBatchInfoDTO().setInspectCode(itemDTO.getReceiptCode());
            itemDTO.getBizBatchInfoDTO().setInspectUserId(user.getId());
            itemDTO.getBizBatchInfoDTO().setTagType(2);
            bizBatchInfoDTOList.add(itemDTO.getBizBatchInfoDTO());

            // 设置需要保存的标签数据
            BizLabelDataDTO label = BizLabelDataDTO.builder()
                    .id(null)
                    .matId(itemDTO.getMatId())
                    .ftyId(itemDTO.getFtyId())
                    .locationId(itemDTO.getLocationId())
                    .batchId(itemDTO.getBizBatchInfoDTO().getId())
                    .binId(itemDTO.getBinId())
                    .whId(itemDTO.getWhId())
                    .typeId(itemDTO.getTypeId())
                    .labelCode(labelCode)
                    .snCode(labelCode)
                    .qty(itemDTO.getQty())
                    .labelType(itemDTO.getBizBatchInfoDTO().getTagType())
                    .receiptHeadId(itemDTO.getHeadId())
                    .receiptItemId(itemDTO.getId())
                    .receiptType(itemDTO.getReceiptType())
                    .preReceiptHeadId(itemDTO.getPreReceiptHeadId())
                    .preReceiptItemId(itemDTO.getPreReceiptItemId())
                    .preReceiptType(itemDTO.getPreReceiptType())
                    .build();
            labelDataList.add(label);

        });

        /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("验收入库-PDA端-打印-更新批次信息成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelDataService.saveBatchDto(labelDataList);
            log.info("验收入库-PDA端-打印-插入标签数据成功 " + JSONObject.toJSONString(labelDataList));
            List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(label.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
            log.info("验收入库-PDA端-打印-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }

        // 填充打印信息
        printInfo.setLabelBoxList(receiptInputBoxes);

        if (UtilCollection.isNotEmpty(receiptInputBoxes)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,receiptInputBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.PRINT_INSPECT_INPUT_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 设置标签打印数据
     * @param receiptInputBoxes 装载打印机打印数据
     * @param itemDTO 要打印的验收单行项目
     * @param labelCode rfid 编码
     * @return
     */
    private void setPrintLabelData(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, String labelCode) {
        // 单品打印将行项目拆分
        if (itemDTO.getIsSingle().equals("单品")){
            int qty = itemDTO.getQty().intValue();
            for (int i = 0; i < qty; i++) {
                printCout(receiptInputBoxes, itemDTO, labelCode);
            }
        }else { // 批次打印不需要拆分
            printCout(receiptInputBoxes, itemDTO, labelCode);
        }
    }

    /**
     * 标签打印的数据
     * @param receiptInputBoxes
     * @param itemDTO
     * @param labelCode
     */
    private void printCout(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, String labelCode) {
        // 设置需要打印的数据
        LabelReceiptInputBox labelReceiptInputBox = UtilBean.newInstance(itemDTO, LabelReceiptInputBox.class);
        labelReceiptInputBox.setTagType(itemDTO.getTagType());
        labelReceiptInputBox.setIsSingle(itemDTO.getIsSingle());
        labelReceiptInputBox.setBatchCode(itemDTO.getBizBatchInfoDTO().getBatchCode());
        labelReceiptInputBox.setRfidCode(labelCode);
        labelReceiptInputBox.setLifetimeDate(itemDTO.getBizBatchInfoDTO().getLifetimeDate());
        // 0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵
        switch (itemDTO.getPackageType()) {
            case 0:
                labelReceiptInputBox.setPackageTypeI18n("不需要包装");
                break;
            case 1:
                labelReceiptInputBox.setPackageTypeI18n("防潮、防撞、防尘包装");
                break;
            case 2:
                labelReceiptInputBox.setPackageTypeI18n("避光包装");
                break;
            case 3:
                labelReceiptInputBox.setPackageTypeI18n("防锈包装");
                break;
            case 4:
                labelReceiptInputBox.setPackageTypeI18n("防静电包装");
                break;
            case 5:
                labelReceiptInputBox.setPackageTypeI18n("真空包装");
                break;
            case 6:
                labelReceiptInputBox.setPackageTypeI18n("原包装");
                break;
            case 7:
                labelReceiptInputBox.setPackageTypeI18n("双面保护");
                break;
            case 8:
                labelReceiptInputBox.setPackageTypeI18n("端口封堵");
                break;
            default:
                labelReceiptInputBox.setPackageTypeI18n(" ");
        }
        // 批次 + 普通标签不需要生成rfid,其他生成rfid
//        if (!(EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsSingle()) && EnumTagType.GENERAL.getValue().equals(itemDTO.getTagType()))) {
//            labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
//        } else {
//            labelReceiptInputBox.setRfidCode(Const.BATCH_GENERAL_LABEL_TAB);
//        }
        labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
        receiptInputBoxes.add(labelReceiptInputBox);
    }

    /**
     * 通过head id获取item
     * @param headId head id
     * @return
     */
    private BizReceiptInputHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptInputHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizReceiptInputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 普通标签生成上架请求
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void generateLoadReq(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 2023-08-14 暂存入库仅对qty>0的行项目生成上架请求
        List<BizReceiptInputItemDTO> canTaskItemList = po.getItemList().stream().filter(item -> item.getQty().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

        if (UtilCollection.isNotEmpty(canTaskItemList)) {
            /* ******** 设置作业请求head ******** */
            BizReceiptTaskReqHeadDTO reqHeadDTO = UtilBean.newInstance(po, BizReceiptTaskReqHeadDTO.class);
            reqHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPLETED.getValue());
            reqHeadDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_TASK_REQ_SHELF_LOAD.getValue());
            reqHeadDTO.setId(null);
            if(!UtilString.hasText(reqHeadDTO.getDes())){
                reqHeadDTO.setDes(po.getDeliveryNoticeDescribe());
            }
            if(!UtilString.hasText(reqHeadDTO.getDes())){
                reqHeadDTO.setDes(po.getRemark());
            }
            reqHeadDTO.setCreateTime(UtilDate.getNow());
            reqHeadDTO.setModifyTime(UtilDate.getNow());
            /* ******** 设置作业请求item ******** */
            List<BizReceiptTaskReqItemDTO> repItemListDTO =
                    UtilCollection.toList(canTaskItemList, BizReceiptTaskReqItemDTO.class);
            for (BizReceiptTaskReqItemDTO reqItemDTO : repItemListDTO) {
                reqItemDTO.setPreReceiptType(po.getReceiptType());
                reqItemDTO.setPreReceiptCode(po.getReceiptCode());
                reqItemDTO.setPreReceiptHeadId(po.getId());
                reqItemDTO.setPreReceiptItemId(reqItemDTO.getId());
                reqItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPLETED.getValue());
                reqItemDTO.setId(null);
                reqItemDTO.setHeadId(null);
                reqItemDTO.setCreateTime(UtilDate.getNow());
                reqItemDTO.setModifyTime(UtilDate.getNow());
            }
            reqHeadDTO.setItemList(repItemListDTO);
            log.debug("基于暂存物资入库单{}, receiptId={},组装参数，生成上架单", po.getReceiptCode(), po.getId());
            // 设置上架请求数据到上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO,
                    new BizReceiptTaskReqSavePo().setStockTaskReqHeadInfo(reqHeadDTO));
            // 推送MQ异步生成上架请求
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.GENERATE_LOAD_REQ_TAGS, ctx);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
            //loadTaskService.generateLoadReq(ctx);
        } else {
            // 没有需要作业的行项目说明所有行项目的入库数量均为0，此时应将单据置为已完成，关闭单据后续流程
            bizReceiptInputHeadDataWrap.update(new UpdateWrapper<BizReceiptInputHead>().lambda()
                .eq(BizReceiptInputHead::getId, po.getId())
                .set(BizReceiptInputHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
            );
        }
    }

    /**
     * 暂存入库，更新qty=0的行项目状态为已完成
     * @param ctx
     */
    public void updateZeroItemToCompleted(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 2023-08-14 暂存入库仅对qty=0的行项目直接设置为已完成
        List<BizReceiptInputItemDTO> items = po.getItemList().stream().filter(item -> item.getQty().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        if(UtilCollection.isNotEmpty(items)) {
            bizReceiptInputItemDataWrap.update(new UpdateWrapper<BizReceiptInputItem>().lambda()
                    .in(BizReceiptInputItem::getId, items.stream().map(BizReceiptInputItemDTO::getId).collect(Collectors.toList()))
                    .set(BizReceiptInputItem::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
            );
        }
    }

    /**
     * 校验行项目是否全部完作业
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO : "验收入库上架回调VO"}
     */
    public boolean checkAllItemStatusTask(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 校验行项目是否全已作业
        if (this.checkAllItemStatusSame(vo.getTaskHeadId(), EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
            BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(vo.getTaskHeadId());
            // 转DTO
            BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
            // 填充关联属性和父子属性
            dataFillService.fillAttr(headDTO);
            // 设置入库单信息到上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            return true;
        }
        return false;
    }

    /**
     * 校验行行目状态是否全部相同
     *
     * @param headId 入库单抬头主键
     * @param itemStatus 行项目状态
     * @return true/false
     */
    public boolean checkAllItemStatusSame(Long headId, Integer itemStatus) {
        UpdateWrapper<BizReceiptInputItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptInputItem::getHeadId, headId);
        // 获取全部上架作业的入库单
        List<BizReceiptInputItem> inputItem = bizReceiptInputItemDataWrap.list(wrapper);
        // 转DTO
        List<BizReceiptInputItemDTO> allStockInputDTOList =
                UtilCollection.toList(inputItem, BizReceiptInputItemDTO.class);
        if (UtilCollection.isEmpty(allStockInputDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 过滤行项目
        // 所有行项目均是 已完成或已作业，认为是作业完毕
        List<BizReceiptInputItemDTO> stayInputList = allStockInputDTOList.stream()
                .filter(e -> itemStatus.equals(e.getItemStatus()) || EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(e.getItemStatus())).collect(Collectors.toList());
        return stayInputList.size() == allStockInputDTOList.size();
    }

    /**
     * 开启审批
     *
     * @param ctx 入参上下文
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 属性填充
        dataFillService.fillAttr(po);
        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        Integer receiptType = po.getReceiptType();
        Map<String, Object> variables = new HashMap<>();

        // 校验审批人
        this.approveCheck(ctx);

        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        variables.put("ftyId", po.getItemList().get(0).getFtyId());
        // 用户所属部门
        variables.put("userDept", userDept);

        // receiptType 8151
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getRemark());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);

        // 更新转性单据状态 - 审批中
        inputComponent.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }

    /**
     * 审批校验
     *
     * @param ctx 入参上下文
     */
    private void approveCheck(BizContext ctx) {
        // 校验发起人是否绑定了部门
        CurrentUser currentUser = ctx.getCurrentUser();
        List<MetaDataDeptOfficePO> userDepartment = sysUserDeptOfficeRelDataWrap.getUserDept(currentUser);
        if (org.springframework.util.CollectionUtils.isEmpty(userDepartment)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }

        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList = new ArrayList<>();

        // 一级审批节点 发起人所属部门负责人
        for (MetaDataDeptOfficePO deptOfficePO : userDepartment) {
            String deptCode = deptOfficePO.getDeptCode();
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_4);
            level1UserList.addAll(userList);
        }
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }

        // 二级审批节点 工程部仓储管理人员 固定写死 唐娜 82000240 宋飞 82000247
        level2UserList = Arrays.asList("82000240", "82000247");
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }
    }

    /**
     * 审批回调
     *
     * @param wfReceiptCo 回调参数
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizReceiptInputHead head = bizReceiptInputHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptInputHeadDTO.class);
        // 数据填充
        dataFillService.fillAttr(headDTO);
        // 封装上下文
        BizContext ctx = new BizContext();
        ctx.setCurrentUser(wfReceiptCo.getInitiator());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 判断是否审批通过
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {

            // 生成ins凭证
            this.generateInsDocToPost(ctx);

            // 过账前补全数据(不交互sap时使用)
            inputComponent.setPostData(ctx);

            // ins入库过账
            inputComponent.postInputToIns(ctx);

            // 更新qty=0的暂存入库行项目状态为已完成
            this.updateZeroItemToCompleted(ctx);

            // 普通标签生成上架请求
            this.generateLoadReq(ctx);
        } else {
            // 单据驳回
            inputComponent.updateStatusRejected(ctx);
        }
    }

    /**
     * 按行项目生成批次信息
     *
     * @param ctx 入参上下文
     */
    public void setBatchInfo(BizContext ctx) {
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 按行项目生成批次
        for (BizReceiptInputItemDTO inputItemDTO : headDTO.getItemList()) {
            BizBatchInfoDTO bizBatchInfoDTO = new BizBatchInfoDTO();
            bizBatchInfoDTO = UtilBean.newInstance(inputItemDTO, bizBatchInfoDTO.getClass());
            bizBatchInfoDTO.setId(null);
            // 配置暂存人、暂存部门、暂存科室
            bizBatchInfoDTO.setTempStoreUser(headDTO.getCreateUserName());
            bizBatchInfoDTO.setTempStoreDeptId(headDTO.getDeptId());
            bizBatchInfoDTO.setTempStoreDeptCode(headDTO.getDeptCode());
            bizBatchInfoDTO.setTempStoreDeptName(headDTO.getDeptName());
            bizBatchInfoDTO.setTempStoreDeptOfficeId(headDTO.getDeptOfficeId());
            bizBatchInfoDTO.setTempStoreDeptOfficeCode(headDTO.getDeptOfficeCode());
            bizBatchInfoDTO.setTempStoreDeptOfficeName(headDTO.getDeptOfficeName());
            bizBatchInfoDTO.setTempStoreExpireDate(DateUtil.offsetMonth(UtilDate.getNow(), inputItemDTO.getTempStorePeriod()));
            bizBatchInfoDTO.setTempStoreReason(headDTO.getTempStoreReason());
            // 成套设备暂存入库 SKX-CWH5YYYY-APS-ZCXX
            String receiptCode = "SK" + headDTO.getUnit() + "-CWH5" + UtilDate.getYearMonth(UtilDate.getNow()) + "-APS-ZC" + bizCommonService.getNextSequence("unitized_temp_input_req");
            headDTO.setReceiptCode(receiptCode);
            bizBatchInfoDTO.setTempStorePreReceiptCode(receiptCode);
            bizBatchInfoDTO.setSpecStock(EnumSpecStock.SPEC_STOCK_UNITIZED_BATCH_STATUS_TEMP_STORE.getValue());
            bizBatchInfoDTO.setIsSingle(EnumLabelType.BATCH.getValue());//单品/批次  0批次 1单品
            bizBatchInfoDTO.setTagType(EnumTagType.METAL_UNRESISTANT.getValue());//标签类型 0：普通标签 1：RFID抗金属  2：RFID非抗金属
            bizBatchInfoDTO.setBatchCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.TEMP_STORE_BATCH.getValue()));
            inputItemDTO.setBizBatchInfoDTO(bizBatchInfoDTO);
        }
        // 获取物料特性
        bizSpecFeatureValueService.getSpecList(headDTO.getItemList(), BizReceiptInputItemDTO.class, EnumSpecClassifyType.QUALITY_TYPE.getValue());
    }
}
