package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.common.service.datawrap.BizReceiptAssembleRuleDataWrap;
import com.inossem.wms.bizbasis.erp.service.datawrap.ErpPurchaseReceiptItemDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.rfid.service.datawrap.BizLabelReceiptRelDataWrap;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityItemDataWrap;
import com.inossem.wms.bizdomain.input.service.component.inputbase.InputComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.InspectInputInspectMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.InspectInputInspectWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.InspectInputMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.component.movetype.InspectInputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputBinDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputWaybillDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.unitized.service.component.moveType.UnitizedInspectInputMoveTypeComponent;
import com.inossem.wms.bizdomain.unitized.service.component.moveType.UnitizedInspectInputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.unitized.service.datawrap.BizReceiptWaybillDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumStockStatus;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizbasis.entity.BizReceiptAssembleRule;
import com.inossem.wms.common.model.bizbasis.po.BizReceiptAssembleRuleSearchPO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputBinDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputWaybillDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputBin;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputWaybill;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadCallbackVO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizReceiptWaybill;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptItem;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.entity.BizLabelData;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.factory.dto.DicFactoryDTO;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.model.stock.dto.StockBinDTO;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilMetadata;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilReflect;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 验收入库组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-12
 */

@Service
@Slf4j
public class UnitizedInspectInputWriteOffComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;

    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    @Autowired
    private BizReceiptInputBinDataWrap bizReceiptInputBinDataWrap;

    @Autowired
    private BizReceiptInputWaybillDataWrap bizReceiptInputWaybillDataWrap;

    @Autowired
    private BizReceiptAssembleRuleDataWrap bizReceiptAssembleRuleDataWrap;

    @Autowired
    private InspectInputMoveTypeComponent inspectInputMoveTypeComponent;

    @Autowired
    private InspectInputInspectMoveTypeComponent inspectInputInspectMoveTypeComponent;

    @Autowired
    private InspectInputWriteOffMoveTypeComponent inspectInputWriteOffMoveTypeComponent;

    @Autowired
    private InspectInputInspectWriteOffMoveTypeComponent inspectInputInspectWriteOffMoveTypeComponent;

    @Autowired
    private BizReceiptInconformityItemDataWrap bizReceiptInconformityItemDataWrap;

    @Autowired
    private BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private InputComponent inputComponent;


    @Autowired
    private BizReceiptInspectItemDataWrap inspectItemDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    protected BatchInfoService bizBatchInfoService;

    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private BizLabelReceiptRelDataWrap labelReceiptRelDataWrap;

    @Autowired
    private LabelDataService labelDataService;
    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    protected ErpPurchaseReceiptItemDataWrap erpPurchaseReceiptItemDataWrap;

    @Autowired
    private UnitizedInspectInputComponent unitizedInspectInputComponent;
    @Autowired
    protected BizReceiptWaybillDataWrap bizReceiptWaybillDataWrap;
    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    private UnitizedInspectInputMoveTypeComponent unitizedInspectInputMoveTypeComponent;
    @Autowired
    private UnitizedInspectInputWriteOffMoveTypeComponent unitizedInspectInputWriteOffMoveTypeComponent;
    /**
     * 页面初始化: 1、设置验收入库【单据类型、创建时间、创建人】 2、设置按钮权限【提交、保存】 3、设置扩展功能【单据流】
     *
     * @in ctx 入参
     * @out ctx 出参 {@link BizResultVO (head":"验收入库","extend":"扩展功能","button":"按钮组")}
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptInputHeadDTO> resultVO = new BizResultVO<>(
            new BizReceiptInputHeadDTO().setReceiptType(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue())
                .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
            new ExtendVO().setRelationRequired(true), new ButtonVO().setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 成套设备验收入库冲销单-分页
     *
     * @in ctx 入参 {@link BizReceiptInputSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO<BizReceiptInputHeadDTO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptInputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = this.setQueryWrapper(po,user);
        // 分页处理
        IPage<BizReceiptInputHeadVO> page = po.getPageObj(BizReceiptInputHeadVO.class);
        // 获取成套设备验收入库冲销单
        bizReceiptInputHeadDataWrap.getUnitizedInputWriteOffList(page, wrapper, EnumDataFillType.FILL_RLAT);
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 成套设备验收入库冲销单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"成套设备验收入库冲销单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取成套设备验收入库冲销单
        BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizInspectInputHeadDTO =
            UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizInspectInputHeadDTO);
        // 填充打印数据
        bizInspectInputHeadDTO.setMatDocCode(bizInspectInputHeadDTO.getItemList().get(0).getMatDocCode());
        bizInspectInputHeadDTO.setReferReceiptCode(bizInspectInputHeadDTO.getItemList().get(0).getReferReceiptCode());
        bizInspectInputHeadDTO.setSupplierName(bizInspectInputHeadDTO.getItemList().get(0).getSupplierName());
        bizInspectInputHeadDTO.setDocDate(bizInspectInputHeadDTO.getItemList().get(0).getDocDate());
        bizInspectInputHeadDTO.setFtyName(bizInspectInputHeadDTO.getItemList().get(0).getFtyName());
        bizInspectInputHeadDTO.setSumPrice(bizInspectInputHeadDTO.getItemList().stream().map(BizReceiptInputItemDTO::getDmbtr).reduce(BigDecimal.ZERO, BigDecimal::add));
        bizInspectInputHeadDTO.setPreReceiptCode(bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptCode());
        bizInspectInputHeadDTO.getItemList().stream().forEach(itemDTO -> {
            // 设置运单总价
            if(itemDTO.getInputWaybillList()!=null){
                itemDTO.getInputWaybillList().forEach(
                        p -> p.setTotalPrice((p.getQty().multiply(p.getWaybillDTO().getPrice())))
                );
            }
        });
        // 设置按钮组权限
        ButtonVO buttonVO = inputComponent.setButton(bizInspectInputHeadDTO);
        buttonVO.setButtonDelete(false);
        buttonVO.setButtonPrint(false);
        // 设置成套设备验收入库冲销单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
            new BizResultVO<>(bizInspectInputHeadDTO, new ExtendVO(), buttonVO));
    }




    /**
     * 保存验收入库前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存的成套设备验收入库冲销单}
     */
    public void checkSaveInspectInput(BizContext ctx) {
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 校验行项目是都为空
        inputComponent.checkEmptyItem(po);
    }

    /**
     * 提交成套设备验收入库冲销单前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的成套设备验收入库冲销单}
     */
    public void checkSubmitInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验行项目是都为空 ******** */
        inputComponent.checkEmptyItem(po);
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList =new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                inputWaybillDTOList.add(inputWaybillDTO);
            }
        }
        inputWaybillDTOList.forEach(waybillDTO -> waybillDTO.setWriteOffPostingDate(po.getWriteOffPostingDate()).setWriteOffReason(po.getWriteOffReason())
                .setQty(waybillDTO.getQty()));
        /* ******** 校验成套设备验收入库冲销单行项目相关数量开始 ******** */
       // inputComponent.checkEmptyItemQty(po);
        /* ******** 提交前校验物料、库存地点是否冻结 ******** */
       // inputComponent.checkFreeze(po);
    }

    /**
     * 提交成套设备验收入库冲销单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的成套设备验收入库冲销单}
     * @out ctx 出参 {"stockInputCode" : "成套设备验收入库冲销单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitInspectInput(BizContext ctx) {
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存成套设备验收入库冲销单
        this.saveInput(ctx);
        // 入参上下文 - 要保存的入库单
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList = new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            if(UtilCollection.isNotEmpty(itemDTO.getInputWaybillList())) {
                for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                    inputWaybillDTOList.add(inputWaybillDTO);
                }
            }
        }
        if (UtilCollection.isNotEmpty(inputWaybillDTOList)){
            //生成同时过账凭证，组装input的bin级别
            for (BizReceiptInputWaybillDTO itemDTO : inputWaybillDTOList) {
                AtomicInteger bid = new AtomicInteger();
                List<BizReceiptInputBinDTO> inputBinList = new ArrayList<>();
                for (StockBinDTO stockBinDTO : itemDTO.getStockBinList()) {
                    BizReceiptInputBinDTO inputBin = new BizReceiptInputBinDTO();
                    inputBin.setCellId(stockBinDTO.getCellId());
                    inputBin.setHeadId(po.getId());
                    inputBin.setItemId(itemDTO.getId());
                    inputBin.setBinId(stockBinDTO.getBinId());
                    inputBin.setQty(stockBinDTO.getOperationQty());
                    inputBin.setTypeId(stockBinDTO.getTypeId());
                    inputBin.setBid(String.valueOf(bid.getAndIncrement()));
                    inputBin.setLabelDataList(stockBinDTO.getLabelDataList());
                    inputBinList.add(inputBin);
                }
                itemDTO.setBinList(inputBinList);
            }
            //保存bin级别，详情查看
            List<BizReceiptInputBinDTO> binList = inputWaybillDTOList.stream().flatMap(obj -> obj.getBinList().stream()).collect(Collectors.toList());
            bizReceiptInputBinDataWrap.saveBatchDto(binList);
            //保存标签，详情查看
            List<BizLabelReceiptRel> labelReceiptRelList = new ArrayList<>();
            for (BizReceiptInputBinDTO binDTO : binList) {
                for (BizLabelData bizLabelData : binDTO.getLabelDataList()) {
                    BizLabelReceiptRel labelReceiptRel = new BizLabelReceiptRel();
                    labelReceiptRel.setLabelId(bizLabelData.getId());
                    labelReceiptRel.setReceiptType(po.getReceiptType());
                    labelReceiptRel.setReceiptHeadId(binDTO.getHeadId());
                    labelReceiptRel.setReceiptItemId(binDTO.getItemId());
                    labelReceiptRel.setReceiptBinId(binDTO.getId());
                    labelReceiptRelList.add(labelReceiptRel);
                }
            }
            labelReceiptRelDataWrap.saveBatch(labelReceiptRelList);
        }
    }

    /**
     * 保存入库单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存的入库单}
     * @out ctx 出参 {"stockInputCode" : "入库单号"},{@link BizReceiptInputHeadDTO : "已保存的入库单}
     */
    public void saveInput(BizContext ctx) {
        // 入参上下文 - 要保存的入库单
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String stockInputCode = po.getReceiptCode();
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        po.setCreateUserId(user.getId());
        po.setModifyUserId(user.getId());
        po.setCreateTime(null);
        po.setModifyTime(UtilDate.getNow());
        po.setReceiptType(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新入库单
            bizReceiptInputHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteInputItem(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            stockInputCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.INPUT_WRITE_OFF.getValue());
            po.setReceiptCode(stockInputCode);
            bizReceiptInputHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存入库单head成功!单号{},主键{}", stockInputCode, po.getId());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        AtomicInteger rid = new AtomicInteger(1);
        List<BizBatchInfoDTO> saveBatchInfoDtoList = new ArrayList<>();
        List<BizBatchInfoDTO> updateBatchInfoDtoList = new ArrayList<>();
        for (com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(po.getId());
            itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
            itemDTO.setReceiptCode(po.getReceiptCode());
            itemDTO.setReceiptType(po.getReceiptType());
            if(UtilCollection.isNotEmpty(itemDTO.getInputWaybillList())) {
                BigDecimal qty = BigDecimal.ZERO;
                for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                    qty=qty.add(inputWaybillDTO.getQty().multiply(inputWaybillDTO.getWaybillDTO().getPrice()));
                }
                itemDTO.setQty(qty);
            }
        }
        // 批量保存item
        po.getItemList().forEach(item -> {
            item.setId(null);
            if(UtilObject.isNotNull(item.getBizBatchInfoDTO())) {
                item.setBatchId(item.getBizBatchInfoDTO().getId());
            }
        });
        bizReceiptInputItemDataWrap.saveBatchDto(po.getItemList());
        log.debug("保存入库单item成功!单号{},headId{}", stockInputCode, po.getId());
        /* ********************** item处理结束 *************************/
        // 保存运单信息
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList = new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            Integer bid = 0;
            if(UtilCollection.isNotEmpty(itemDTO.getInputWaybillList())) {
                for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                    inputWaybillDTO.setPreInputWaybillId(inputWaybillDTO.getId());
                    inputWaybillDTO.setPreReceipBid(inputWaybillDTO.getBid());
                    inputWaybillDTO.setPreReceiptCode(inputWaybillDTO.getReceiptCode());
                    inputWaybillDTO.setPreReceiptHeadId(inputWaybillDTO.getHeadId());
                    inputWaybillDTO.setPreReceiptItemId(inputWaybillDTO.getItemId());
                    inputWaybillDTO.setPreReceiptType(inputWaybillDTO.getReceiptType());
                    inputWaybillDTO.setId(null);
                    inputWaybillDTO.setHeadId(po.getId());
                    inputWaybillDTO.setItemId(itemDTO.getId());
                    inputWaybillDTO.setBillId(inputWaybillDTO.getBillId());
                    inputWaybillDTO.setBid(String.valueOf(++bid));
                    inputWaybillDTO.setCreateUserId(user.getId());
                    inputWaybillDTO.setModifyUserId(user.getId());
                    inputWaybillDTO.setCreateTime(UtilDate.getNow());
                    inputWaybillDTO.setModifyTime(UtilDate.getNow());
                    inputWaybillDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue());
                    inputWaybillDTOList.add(inputWaybillDTO);
                }
            }
        }
        if(UtilCollection.isNotEmpty(inputWaybillDTOList)){
            bizReceiptInputWaybillDataWrap.saveBatchDto(inputWaybillDTOList);
            log.debug("保存入库单成套运单表waybill成功!单号{},headId{}", stockInputCode, po.getId());
        }

        /* ********************** waybill处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(po);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, stockInputCode);
        // 返回保存的入库单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, po);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, po.getId());
    }
    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的入库单
     */
    public void saveReceiptTree(BizReceiptInputHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO item : headDTO.getItemList()) {
            if (UtilNumber.isNotEmpty(item.getPreReceiptType()) && UtilNumber.isNotEmpty(item.getPreReceiptHeadId())
                    && UtilNumber.isNotEmpty(item.getPreReceiptItemId())) {
                BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                        .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                        .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                        .setPreReceiptItemId(item.getPreReceiptItemId());
                dtoList.add(dto);
            }
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }
    /**
     * 删除入库单行项目
     *
     * @param po 要删除的入库信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInputItem(BizReceiptInputHeadDTO po) {
        UpdateWrapper<BizReceiptInputItem> wrapperItem = new UpdateWrapper<>();
        wrapperItem.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputItem::getHeadId, po.getId());
        bizReceiptInputItemDataWrap.physicalDelete(wrapperItem);
        UpdateWrapper<BizReceiptInputBin> wrapperBin = new UpdateWrapper<>();
        wrapperBin.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputBin::getHeadId, po.getId());
        bizReceiptInputBinDataWrap.physicalDelete(wrapperBin);
        UpdateWrapper<BizReceiptInputWaybill> wrapperWaybill = new UpdateWrapper<>();
        wrapperWaybill.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputWaybill::getHeadId, po.getId());
        bizReceiptInputWaybillDataWrap.physicalDelete(wrapperWaybill);
    }
    /**
     * 验收入库过账前数据校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要过账的成套设备验收入库冲销单}
     */
    public void checkInspectInputPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取入库单
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getId());
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性及父子属性
        dataFillService.fillAttr(inputHeadDTO);
        // 校验数据
        inputComponent.checkEmptyItem(inputHeadDTO);
        // 校验可过账的状态
        Set<Integer> canRePostStatusSet = new HashSet<>();
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
        if (!canRePostStatusSet.contains(inputHeadDTO.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_POST);
        }
    }

    /**
     * 入库单过账处理
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "成套设备验收入库冲销单}
     */
    public void handleInputReceiptPost(BizContext ctx) {
        List<BizReceiptInputItemDTO> operDataItemList = new ArrayList<>();
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptInputItemDTO> bizReceiptInputItemDTOS = headDTO.getItemList();
        List<BizReceiptInputItemDTO> fixedList = bizReceiptInputItemDTOS.stream().filter(obj -> "1".equals(obj.getSubjectType()) && "标准".equals(obj.getProjectType())).collect(Collectors.toList());

        for (BizReceiptInputItemDTO bizReceiptInputItemDTO : bizReceiptInputItemDTOS) {
            operDataItemList.clear();
            operDataItemList.add(bizReceiptInputItemDTO);
            headDTO.setItemList(operDataItemList);
            // 项目类别
            String projectType = bizReceiptInputItemDTO.getProjectType();
            // 科目类别
            String subjectType = bizReceiptInputItemDTO.getSubjectType();
            // 科目分配类别为1，项目类型为空的零星消耗采购订单时，需支持无物料编码收货，且入库后无库存
            // 当成套设备验收入库冲销单中同时存在科目分配类别为Q和1时，在验收入库完成后，科目分配类别为Q的行项目，生成上架作业单，科目分配类别为1的行项目，不生成上架作业单；
            if(subjectType.equals("1") && projectType.equals("标准")) {
                // sap入库过账
                inputComponent.postInputToSap(ctx);
                // 更新批次入库时间
                inputComponent.updateInputDate(ctx);
                // 单据日志 - 过账
                receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                        EnumReceiptOperationType.RECEIPT_OPERATION_POSTING, "", ctx.getCurrentUser().getId());
                // 更新入库单 - 已完成
                inputComponent.updateStatusCompleted(ctx);
            }else {
                // 生成ins凭证
                generateInsDocToPost(ctx);
                // sap入库过账
                inputComponent.postInputToSap(ctx);
                // ins入库过账
                inputComponent.postInputToIns(ctx);
                // 更新批次入库时间
                inputComponent.updateInputDate(ctx);
                // 普通标签生成上架请求
                inputComponent.generateLoadReq(ctx);
            }
            
        }
    }

    /**
     * 入库单冲销处理
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "成套设备验收入库冲销单}
     */
    public void handleInputReceiptWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // ins入库冲销
        List<BizReceiptInputItemDTO> noFixList = headDTO.getItemList().stream().filter(obj -> !"1".equals(obj.getSubjectType())).collect(Collectors.toList());
        try {
            if (UtilCollection.isNotEmpty(noFixList)) {
                // 生成ins冲销过账凭证
                this.generateInsDocToPostWriteOff(ctx);
            }
            // sap入库冲销 106
            if (UtilObject.isEmpty(headDTO.getItemList().get(0).getWriteOffMatDocCode())) {
                unitizedInspectInputComponent.writeOffInputToSap(ctx);
            }
            // sap入库冲销 104
            if (UtilObject.isEmpty(headDTO.getItemList().get(0).getDeliveryWriteOffMatDocCode())) {
                unitizedInspectInputComponent.writeOffInputToSap(ctx);
                // 更新item状态 - 已冲销
                unitizedInspectInputComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            }
        }catch (WmsException e){
            // 更新item状态 - 未同步 同时更新凭证
            headDTO.getItemList().forEach(obj->obj.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue()));
//            inspectItemDataWrap.updateBatchDtoById(headDTO.getItemList());
            unitizedInspectInputComponent.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            //异常信息转换
            String errorMsg = "";
            if (e.getArgs()!=null&&e.getArgs().length>0){
                StringBuffer sb = new StringBuffer();
                for (int i = 0 ; i < e.getArgs().length ; i++) {
                    sb.append(e.getArgs()[i]);
                }
                errorMsg = sb.toString();
            }
            // 抛出接口调用失败异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                    UtilObject.getStringOrEmpty(errorMsg));
        }
        // 单据日志 - 冲销
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF, "", ctx.getCurrentUser().getId());

        //重新获取带凭证的非固定资产列表
        noFixList = headDTO.getItemList().stream().filter(obj -> !"1".equals(obj.getSubjectType())).collect(Collectors.toList());
        List<BizReceiptInputItemDTO> allList = UtilCollection.toList(headDTO.getItemList(),BizReceiptInputItemDTO.class);
        // ins入库冲销
        if (UtilCollection.isNotEmpty(noFixList)) {
            //需求中不存在固定资产与非固定资产同时进行冲销
//            headDTO.setItemList(noFixList);
            // 入参上下文 - 凭证
            StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
            //instok冲销
            unitizedInspectInputComponent.writeOffInputToIns(ctx);
        }
        headDTO.setWaybillDTOList(null);
        unitizedInspectInputComponent.updateStatus(headDTO, allList, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 生成ins凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "成套设备验收入库冲销单}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证}
     */
    public void generateInsDocToPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成ins凭证
            insMoveTypeDTO = inspectInputMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 设置ins凭证到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 生成ins质检凭证
     *
     * @in ctx  入参 {@link BizReceiptInputHeadDTO : "成套设备验收入库冲销单}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证}
     */
    public void generateInspectInsDocToPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO;

        try {
            // 生成ins凭证
            insMoveTypeDTO = inspectInputInspectMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }

        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);

        // 设置ins凭证到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 验收入库冲销前校验
     *
     * @in ctx 入参 {@link BizReceiptInputWriteOffPO : "验收入库冲销入参"}
     * @out ctx 出参 {@link BizReceiptInputHeadDTO : "成套设备验收入库冲销单"}
     */
    public void checkInspectInputWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) && UtilNumber.isEmpty(po.getId()) && UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 冲销过账时间赋值
        if (UtilObject.isNull(po.getWriteOffPostingDate()) && UtilObject.isNotNull(po.getPostingDate())) {
            po.setWriteOffPostingDate(po.getPostingDate());
        }

        // 设置冲销标识
        po.getItemList().forEach(itemDTO -> {
            itemDTO.setWriteOffPostingDate(po.getWriteOffPostingDate());
            itemDTO.setPostingDate(po.getWriteOffPostingDate());
            itemDTO.setDeliveryWriteOffPostingDate(po.getWriteOffPostingDate());
            itemDTO.setIsWriteOff(EnumRealYn.TRUE.getIntValue());

            // 重置本位币金额，避免数据覆盖，只在106冲销后，由SAP返回参数写入到数据库
            itemDTO.setDmbtr(null);

        });
//        inputHeadDTO.setItemList(inputItemDTOList);
        // 设置要冲销的成套设备验收入库冲销单到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, po);
    }

    /**
     * 生成ins冲销过账凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "验收入库冲销入参"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void generateInsDocToPostWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 装载凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成凭证
            insMoveTypeDTO = unitizedInspectInputWriteOffMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 生成ins冲销过账凭证 质检过账冲销
     *
     * @in ctx  入参 {@link BizReceiptInputHeadDTO : "验收入库冲销入参"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void generateInspectInsDocToPostWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 装载凭证
        StockInsMoveTypeDTO insMoveTypeDTO;

        try {
            // 生成凭证
            insMoveTypeDTO = inspectInputInspectWriteOffMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }

        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);

        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * 采购验收-生成成套设备验收入库冲销单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO :"成套设备验收入库冲销单"}
     */
    public void genInspectInput(BizContext ctx) {
        // MQ入参上下文 - 成套设备验收入库冲销单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        // 生成成套设备验收入库冲销单
        inputComponent.saveInput(ctx);
        // 保存操作日志
        inputComponent.saveBizReceiptOperationLog(ctx);
        // 保存批次图片
        inputComponent.saveBizBatchImg(ctx);
        // 更新成套设备验收入库冲销单head、item状态 - 已提交
//        inputComponent.updateStatus(headDTO, headDTO.getItemList(),
//            EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue());
    }

    /**
     * 删除校验
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "成套设备验收入库冲销单删除入参"}
     * @out ctx 出参 {@link BizReceiptInputDeletePO : "如果是全部删除，设置行项目id集合"}
     */
    public void checkDeleteInspectInput(BizContext ctx) {
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取入库单信息
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getHeadId());
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(inputHeadDTO);
        /* ******** 校验成套设备验收入库冲销单head ******** */
        if (UtilObject.isNotNull(inputHead)) {
            if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(inputHead.getReceiptStatus())
                && !EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(inputHead.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        /* ******** 校验是否全部删除 ******** */
        if (po.isDeleteAll()) {
            po.setItemIds(
                inputHeadDTO.getItemList().stream().map(BizReceiptInputItemDTO::getId).collect(Collectors.toList()));
        } else if (UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 删除成套设备验收入库冲销单
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "验收单行删除入参"}
     */
    public void deleteInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除成套设备验收入库冲销单 ******** */
        if (po.isDeleteAll()) {
            // 删除成套设备验收入库冲销单head
            bizReceiptInputHeadDataWrap.removeById(po.getHeadId());
            // 删除成套设备验收入库冲销单item
            UpdateWrapper<BizReceiptInputItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptInputItem::getHeadId, po.getHeadId());
            bizReceiptInputItemDataWrap.remove(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                EnumReceiptType.STOCK_INPUT_INSPECT.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                ctx.getCurrentUser().getId());
        } else {
            // 删除成套设备验收入库冲销单item
            bizReceiptInputItemDataWrap.removeByIds(po.getItemIds());
        }
    }

    /**
     * 成套设备验收入库冲销单上架回调校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO:"入库单上架回调入参"}
     */
    public void checkInspectInputByCallback(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 校验入参
        if (null == vo) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验行项目
        if (UtilCollection.isEmpty(vo.getInputItemCallbackVoList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 校验单据类型
        if (!EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(vo.getReceiptType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_FALSE);
        }
    }

    /**
     * 冲销行项目校验
     *
     * @param inputItemDTO BizInspectInputItemDTO
     * @return true/false
     */
    public boolean itemCanWriteOff(BizReceiptInputItemDTO inputItemDTO) {
        Integer receiptType = inputItemDTO.getReceiptType();
        Integer receiptStatus = inputItemDTO.getReceiptStatus();
        return (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus))
            && EnumReceiptType.STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(receiptType);
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return WmsQueryWrapper<BizReceiptInputSearchPO>
     */
    private WmsQueryWrapper<BizReceiptInputSearchPO> setQueryWrapper(BizReceiptInputSearchPO po,CurrentUser user) {
        if (null == po) {
            po = new BizReceiptInputSearchPO();
        }
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }
        String deliveryNoticeDescribe = po.getDeliveryNoticeDescribe();
        // 查询条件设置
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = new WmsQueryWrapper<>();

        /**
         0列表页高级搜索调整:
        1)"物料编码"查询条件描述修改为"主设备编码”，对采购订单行项目中的物料编码查询有效:
        2)"物料描述"查询条件描述修改为"主设备描述"，对采购订单行项目中的物料描述查询有效，支持模糊搜索，
         3) 增加"子设备编码"查询条件，对运单中系统自动生成的物料编码 (CTXXXX) 查询有效:
        4) 增加"子设备描述"查询条件，对运单中B列"描述"查询有效，支持模糊搜索:
        5) 增加"广核物料编码”查询条件，对运单中P列"物资编码"查询有效;
        6) 增加"合同号”查询条件，对运单中AP列“外部合同号”查询有，
        7) 增加"LOT包号”查询条件，对运单中O列“采购包"查询有效
        8)增加"功能位置码”"P码”、"规格型号"查询条件，分别对运单中N列"功能位置码”、V列"UP码”、Q列"规格型号"查询有效。
         */
        /* ********************** 高级搜索字段开始 *************************/
        // 主设备编码
        wrapper.eq(UtilString.isNotNullOrEmpty(po.getMainMatCode()), "parent.mat_code", po.getMainMatCode());
        // 主设备描述
        wrapper.like(UtilString.isNotNullOrEmpty(po.getMainMatName()), "parent.mat_name", po.getMainMatName());
        // 子设备编码
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatCode()),BizReceiptInputSearchPO::getMatCode, DicMaterial.class, po.getMatCode());
        // 子设备描述
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getMatName()), BizReceiptInputSearchPO::getMatName, DicMaterial.class, po.getMatName());
        // 广核物料编码(物资编码)
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getExtend20()),BizReceiptInputSearchPO::getExtend20, BizReceiptWaybill.class, po.getExtend20());
        // 合同号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getExtend65()),BizReceiptInputSearchPO::getExtend65, BizReceiptWaybill.class, po.getExtend65());
        // LOT包号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getExtend2()),BizReceiptInputSearchPO::getExtend2, BizReceiptWaybill.class, po.getExtend2());
        // 功能位置码
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getFunctionalLocationCode()),BizReceiptInputSearchPO::getFunctionalLocationCode, BizReceiptWaybill.class, po.getFunctionalLocationCode());
        // P码
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getExtend29()),BizReceiptInputSearchPO::getExtend29, BizReceiptWaybill.class, po.getExtend29());
        // 规格型号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getExtend24()),BizReceiptInputSearchPO::getExtend24, BizReceiptWaybill.class, po.getExtend24());
        /* ********************** 高级搜索字段结束 *************************/

        // 入库单据号
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
            BizReceiptInputHead.class, po.getReceiptCode());
        // 成套设备验收入库冲销单据号
        wrapper.like(UtilString.isNotNullOrEmpty(po.getInspectCode()), "b1.receiptCode", po.getInspectCode());
        // 单据类型
        wrapper.lambda().eq(Boolean.TRUE, BizReceiptInputSearchPO::getReceiptType, BizReceiptInputHead.class,
            EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue());
        // 单据状态
        wrapper.lambda().in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),
            BizReceiptInputSearchPO::getReceiptStatus, BizReceiptInputHead.class, po.getReceiptStatusList());
        wrapper.lambda().in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptInputSearchPO::getLocationId,
                BizReceiptInputItem.class,locationIdList);
        //到货登记描述
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getDeliveryNoticeDescribe()), BizReceiptInputSearchPO::getDeliveryNoticeDescribe,
                BizReceiptDeliveryNoticeHead.class, po.getDeliveryNoticeDescribe());
        // 创建人
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptInputSearchPO::getUserName,
                SysUser.class, po.getCreateUserName());
        // 创建时间
        wrapper.lambda().between((UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime())), BizReceiptInputSearchPO::getCreateTime,
                BizReceiptInputHead.class, po.getStartTime(), po.getEndTime());
        return wrapper.setEntity(po);
    }

    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        if (UtilString.isNullOrEmpty(po.getBizLabelPrintDTO().getPrinterIp())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_IP_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrinterPort())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PORT_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptInputHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }


    /**
     * 填充打印数据
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();
        // 装载打印机打印数据
        List<LabelReceiptInputBox> receiptInputBoxes = new ArrayList<>();
        // 装载要更新的批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptInputHeadDTO headDTO = (BizReceiptInputHeadDTO) po.getHeadDTO();
        // 新建打印实体对象
        List<BizReceiptInputItemDTO> itemDTOList = headDTO.getItemList();

        itemDTOList.forEach(itemDTO->{
            // 生成标签编码
            String labelCode =
                    bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());

            // 标签打印数据
           this.setPrintLabelData(receiptInputBoxes, itemDTO, labelCode);

            // 设置要更新的批次信息数据
            itemDTO.getBizBatchInfoDTO().setInspectHeadId(itemDTO.getHeadId());
            itemDTO.getBizBatchInfoDTO().setInspectItemId(itemDTO.getId());
            itemDTO.getBizBatchInfoDTO()
                    .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
            itemDTO.getBizBatchInfoDTO().setInspectCode(itemDTO.getReceiptCode());
            itemDTO.getBizBatchInfoDTO().setInspectUserId(user.getId());
            itemDTO.getBizBatchInfoDTO().setTagType(2);
            bizBatchInfoDTOList.add(itemDTO.getBizBatchInfoDTO());

            // 设置需要保存的标签数据
            BizLabelDataDTO label = BizLabelDataDTO.builder()
                    .id(null)
                    .matId(itemDTO.getMatId())
                    .ftyId(itemDTO.getFtyId())
                    .locationId(itemDTO.getLocationId())
                    .batchId(itemDTO.getBizBatchInfoDTO().getId())
                    .binId(itemDTO.getBinId())
                    .whId(itemDTO.getWhId())
                    .typeId(itemDTO.getTypeId())
                    .labelCode(labelCode)
                    .snCode(labelCode)
                    .qty(itemDTO.getQty())
                    .labelType(itemDTO.getBizBatchInfoDTO().getTagType())
                    .receiptHeadId(itemDTO.getHeadId())
                    .receiptItemId(itemDTO.getId())
                    .receiptType(itemDTO.getReceiptType())
                    .preReceiptHeadId(itemDTO.getPreReceiptHeadId())
                    .preReceiptItemId(itemDTO.getPreReceiptItemId())
                    .preReceiptType(itemDTO.getPreReceiptType())
                    .build();
            labelDataList.add(label);

        });

        /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("验收入库-PDA端-打印-更新批次信息成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelDataService.saveBatchDto(labelDataList);
            log.info("验收入库-PDA端-打印-插入标签数据成功 " + JSONObject.toJSONString(labelDataList));
            List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(label.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
            log.info("验收入库-PDA端-打印-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }

        // 填充打印信息
        printInfo.setLabelBoxList(receiptInputBoxes);

        if (UtilCollection.isNotEmpty(receiptInputBoxes)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,receiptInputBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.PRINT_INSPECT_INPUT_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 设置标签打印数据
     * @param receiptInputBoxes 装载打印机打印数据
     * @param itemDTO 要打印的验收单行项目
     * @param labelCode rfid 编码
     * @return
     */
    private void setPrintLabelData(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, String labelCode) {

        // 单品打印将行项目拆分
        if (itemDTO.getIsSingle().equals("单品")){
            int qty = itemDTO.getQty().intValue();
            for (int i = 0; i < qty; i++) {
                printCount(receiptInputBoxes, itemDTO, labelCode);
            }
        }else { // 批次打印不需要拆分
            printCount(receiptInputBoxes, itemDTO, labelCode);
        }

    }

    /**
     * 标签打印的数据
     * @param receiptInputBoxes
     * @param itemDTO
     * @param labelCode
     */
    private void printCount(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, String labelCode) {
        // 设置需要打印的数据
        LabelReceiptInputBox labelReceiptInputBox = UtilBean.newInstance(itemDTO, LabelReceiptInputBox.class);
        labelReceiptInputBox.setTagType(itemDTO.getTagType());
        labelReceiptInputBox.setIsSingle(itemDTO.getIsSingle());
        labelReceiptInputBox.setBatchCode(itemDTO.getBizBatchInfoDTO().getBatchCode());
        labelReceiptInputBox.setRfidCode(labelCode);
        labelReceiptInputBox.setLifetimeDate((itemDTO.getBizBatchInfoDTO().getLifetimeDate()));

        // 0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵
        switch (itemDTO.getPackageType()) {
            case 0:
                labelReceiptInputBox.setPackageTypeI18n("不需要包装");
                break;
            case 1:
                labelReceiptInputBox.setPackageTypeI18n("防潮、防撞、防尘包装");
                break;
            case 2:
                labelReceiptInputBox.setPackageTypeI18n("避光包装");
                break;
            case 3:
                labelReceiptInputBox.setPackageTypeI18n("防锈包装");
                break;
            case 4:
                labelReceiptInputBox.setPackageTypeI18n("防静电包装");
                break;
            case 5:
                labelReceiptInputBox.setPackageTypeI18n("真空包装");
                break;
            case 6:
                labelReceiptInputBox.setPackageTypeI18n("原包装");
                break;
            case 7:
                labelReceiptInputBox.setPackageTypeI18n("双面保护");
                break;
            case 8:
                labelReceiptInputBox.setPackageTypeI18n("端口封堵");
                break;
            default:
                labelReceiptInputBox.setPackageTypeI18n(" ");
        }
        // 批次 + 普通标签不需要生成rfid,其他生成rfid
//        if (!(EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsSingle()) && EnumTagType.GENERAL.getValue().equals(itemDTO.getTagType()))) {
//            labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
//        } else {
//            labelReceiptInputBox.setRfidCode(Const.BATCH_GENERAL_LABEL_TAB);
//        }
        labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
        receiptInputBoxes.add(labelReceiptInputBox);
    }

    /**
     * 通过head id获取item
     * @param headId head id
     * @return
     */
    private BizReceiptInputHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptInputHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizReceiptInputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 成套设备验收入库冲销单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"成套设备验收入库冲销单详情","button":"按钮组")}
     */
    public void getPrintInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取成套设备验收入库冲销单
        BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizInspectInputHeadDTO =
                UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizInspectInputHeadDTO);
        BizReceiptInputItemDTO firstItemDTO = bizInspectInputHeadDTO.getItemList().get(0);
        // 填充打印数据
        bizInspectInputHeadDTO.setMatDocCode(bizInspectInputHeadDTO.getItemList().get(0).getMatDocCode());
        bizInspectInputHeadDTO.setReferReceiptCode(bizInspectInputHeadDTO.getItemList().get(0).getReferReceiptCode());
        bizInspectInputHeadDTO.setSupplierName(firstItemDTO.getSupplierName());
     //   bizInspectInputHeadDTO.setDocDate(bizInspectInputHeadDTO.getItemList().get(0).getDocDate());
        bizInspectInputHeadDTO.setFtyName(bizInspectInputHeadDTO.getItemList().get(0).getFtyName());
        bizInspectInputHeadDTO.setWriteOffMatDocCode(bizInspectInputHeadDTO.getItemList().get(0).getWriteOffMatDocCode());
        bizInspectInputHeadDTO.setWriteOffMatDocYear(bizInspectInputHeadDTO.getItemList().get(0).getWriteOffMatDocYear());
        BigDecimal sumPrice = BigDecimal.ZERO;

        String signInspectReceiptCode = null;
        String erpCreateUserName = firstItemDTO.getErpCreateUserName();

        String inspectUserName = null;
        String inspectSumbitterName = null;
        BizReceiptInspectHead inspectHead = null;
        QueryWrapper<BizReceiptInputItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptInputItem::getHeadId,bizInspectInputHeadDTO.getItemList().get(0).getPreReceiptHeadId());
        List<BizReceiptInputItem> preItemList = bizReceiptInputItemDataWrap.list(queryWrapper);
        List<BizReceiptInputItemDTO> preItemDTOList = UtilCollection.toList(preItemList, BizReceiptInputItemDTO.class);
        dataFillService.fillAttr(preItemDTOList);
        if (preItemDTOList.get(0).getPreReceiptType().equals(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue())) {
            signInspectReceiptCode = preItemDTOList.get(0).getPreReceiptCode();
            inspectHead = bizReceiptInspectHeadDataWrap.findByCode(signInspectReceiptCode);
        } else if(preItemDTOList.get(0).getPreReceiptType().equals(EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue())) {
            inspectHead = bizReceiptInspectHeadDataWrap.getById(
                    bizReceiptInconformityItemDataWrap.getById(
                                      preItemDTOList.get(0).getPreReceiptItemId()
                                   ).getPreReceiptHeadId());
        }
        if (inspectHead != null) {
            signInspectReceiptCode = inspectHead.getReceiptCode();
            inspectUserName = inspectHead.getInspectUserName();
            Long userId = inspectHead.getCreateUserId();
            List<Long> userIdList = new ArrayList<>(1);
            userIdList.add(userId);
            Collection<SysUser> sysUserCollection = dictionaryService.getSysUserCacheByIds(userIdList);
            if (!CollectionUtils.isEmpty(sysUserCollection)) {
                List<SysUser> sysUserList = sysUserCollection.stream().collect(Collectors.toList());
                inspectSumbitterName = sysUserList.get(0).getUserName();
            }
        }
        String finalSignInspectReceiptCode = signInspectReceiptCode;
        List<BizReceiptInputWaybillDTO> iputWaybillDTOList=new ArrayList<>();
        List<BizReceiptInputItemDTO> itemDTOList = bizInspectInputHeadDTO.getItemList();
        Map<Long, String> subjectMap = new HashMap<>(itemDTOList.size());
        for (BizReceiptInputItemDTO itemDTO : itemDTOList) {
            subjectMap.put(itemDTO.getMatId(), itemDTO.getSubjectType());
        }
        for (BizReceiptInputItemDTO itemDTO : bizInspectInputHeadDTO.getItemList()) {
            for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                iputWaybillDTOList.add(inputWaybillDTO);
            }
        }
        bizInspectInputHeadDTO.setDocDate(iputWaybillDTOList.get(0).getDocDate());
        for (BizReceiptInputWaybillDTO iputWaybillDTO : iputWaybillDTOList) {
            iputWaybillDTO.setBatchCode(iputWaybillDTO.getBizBatchInfoDTO().getBatchCode()).setSignInspectReceiptCode(finalSignInspectReceiptCode);
            BigDecimal qty =iputWaybillDTO.getQty();
            BigDecimal totalPrice = (iputWaybillDTO.getQty().multiply(iputWaybillDTO.getWaybillDTO().getPrice()));
            iputWaybillDTO.setTotalPrice(totalPrice);
            sumPrice = sumPrice.add(totalPrice);
            // 填充抬头 公司名称信息 打印时需要 取行项目工厂对应的公司信息
            if (UtilString.isNullOrEmpty(bizInspectInputHeadDTO.getCorpName())) {
                DicFactoryDTO factoryDTO = dictionaryService.getFtyCacheById(iputWaybillDTO.getFtyId());
                if (factoryDTO == null) {
                    // 如果缓存没有获取到，默认一个公司名称（这种情况为异常数据，行项目上缺少了工厂有效的工厂id）
                    log.warn("成套设备验收入库冲销单{} 行项目缺少有效的工厂数据，请检查", bizInspectInputHeadDTO.getReceiptCode());
                    bizInspectInputHeadDTO.setCorpName(Const.FACTORY_J046_DEFAULT_CORP_NAME);
                }
                bizInspectInputHeadDTO.setCorpName(factoryDTO.getCorpName());
            }
            Long parentMatId = iputWaybillDTO.getWaybillDTO().getParentMatId();
            String subjectType = subjectMap.get(parentMatId);
            if (subjectType != null) {
                iputWaybillDTO.setSubjectType(subjectType);
            }
        }
        bizInspectInputHeadDTO.setSumPrice(sumPrice);
        String sumPriceStr = sumPrice.stripTrailingZeros().toPlainString();
        String sumPriceLocal = UtilNumber.bigDecimalToLocalStr(new BigDecimal(sumPriceStr));
        bizInspectInputHeadDTO.setErpCreateUserName(erpCreateUserName);
        bizInspectInputHeadDTO.setSumPriceStr(sumPriceStr + "元");
        bizInspectInputHeadDTO.setSumPriceLocal(sumPriceLocal);
        bizInspectInputHeadDTO.setInspectUserName(inspectUserName);
        bizInspectInputHeadDTO.setInspectSumbitterName(inspectSumbitterName);

        // 设置按钮组权限
        ButtonVO buttonVO = inputComponent.setButton(bizInspectInputHeadDTO);
        buttonVO.setButtonDelete(false);
        // 设置成套设备验收入库冲销单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(bizInspectInputHeadDTO, new ExtendVO(), buttonVO));
    }

    public void getInsepctInput(BizContext ctx) {
        // 上下文入参
        BizReceiptInputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        String inspectReceiptCode = po.getInspectCode();
        QueryWrapper<BizReceiptInputHead> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BizReceiptInputHead::getReceiptCode,inspectReceiptCode)
                .eq(BizReceiptInputHead::getReceiptType,EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue());
        List<BizReceiptInputHead> inputHeadList = bizReceiptInputHeadDataWrap.list(queryWrapper);
        //当成套设备验收入库冲销单为“草稿”、“未同步”时，提示“成套设备验收入库冲销单未过账，请完成入库”
        List<BizReceiptInputHead> drawOrUnSyncList = inputHeadList.stream().filter(obj -> EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(obj.getReceiptStatus()) || EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(obj.getReceiptStatus())).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(drawOrUnSyncList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_INPUT_NOT_POST);
        }
        //当成套设备验收入库冲销单为“已记账”、“已作业”、“作业中”时，提示“成套设备验收入库冲销单存在未上架的行项目，请完成上架作业”；
        List<BizReceiptInputHead> takingList = inputHeadList.stream().filter(obj -> EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(obj.getReceiptStatus()) || EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(obj.getReceiptStatus())).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(takingList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_INPUT_NOT_TASK);
        }
        List<BizReceiptInputHeadDTO> headDTOList = UtilCollection.toList(inputHeadList, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTOList);
        if (UtilCollection.isEmpty(headDTOList)){
            return;
        }
        //查询已创建的行项目
        QueryWrapper<BizReceiptInputWaybill> preItemListQueryWrapper = new QueryWrapper<>();
        List<Long> headIdList = headDTOList.stream().map(obj -> obj.getId()).collect(Collectors.toList());
        preItemListQueryWrapper.lambda().in(BizReceiptInputWaybill::getPreReceiptHeadId,headIdList);
        List<BizReceiptInputWaybill> list = bizReceiptInputWaybillDataWrap.list(preItemListQueryWrapper);
        Map<Long,BizReceiptInputWaybill> preItemIdAndItemMap = list.stream().collect(Collectors.toMap(obj->obj.getPreInputWaybillId(), obj->obj));
        //组装返回
        for (BizReceiptInputHeadDTO bizReceiptInputHeadDTO : headDTOList) {
            bizReceiptInputHeadDTO.setWaybillDTOWriteOffList(bizReceiptInputHeadDTO.getWaybillDTOList());
            bizReceiptInputHeadDTO.setWaybillDTOList(null);
            bizReceiptInputHeadDTO.setId(null);
            bizReceiptInputHeadDTO.setCreateTime(new Date());
            bizReceiptInputHeadDTO.setModifyTime(new Date());
            bizReceiptInputHeadDTO.setCreateUserId(ctx.getCurrentUser().getId());
            bizReceiptInputHeadDTO.setModifyUserId(ctx.getCurrentUser().getId());
            bizReceiptInputHeadDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue());
            for (BizReceiptInputItemDTO itemDTO : bizReceiptInputHeadDTO.getItemList()) {
//                itemDTO.setId(null);
                itemDTO.setPreReceiptItemId(itemDTO.getId());
                itemDTO.setPreReceiptHeadId(itemDTO.getHeadId());
                itemDTO.setPreReceiptRid(itemDTO.getRid());
                itemDTO.setPreReceiptQty(itemDTO.getQty());
                itemDTO.setPreReceiptType(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue());
                itemDTO.setPreReceiptCode(itemDTO.getReceiptCode());
                itemDTO.setQty(BigDecimal.ZERO);
                for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                    inputWaybillDTO.setPreQty(inputWaybillDTO.getQty());
                    inputWaybillDTO.setPreReceiptCode(bizReceiptInputHeadDTO.getItemList().get(0).getReceiptCode());
                    //更新已修改的状态到查询页面，逻辑为冲销单，冲销后不会更新入库单状态，而查询入库单行项目进行重霄时，行项目为冲销状态的不可选
                    BizReceiptInputWaybill bizReceiptInputWaybill = preItemIdAndItemMap.get(inputWaybillDTO.getId());
                    if (UtilObject.isNotEmpty(bizReceiptInputWaybill)&&
                            (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(bizReceiptInputWaybill.getBillStatus())||EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(bizReceiptInputWaybill.getBillStatus()))){
                        inputWaybillDTO.setBillStatus(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
                    }
                    //如果已经存在凭证或行状态已经为已冲销，则查询时设定为不可选(处理过往数据)
                    if (UtilObject.isNotEmpty(inputWaybillDTO.getWriteOffMatDocCode())||EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue().equals(inputWaybillDTO.getBillStatus())){
                        inputWaybillDTO.setBillStatus(EnumReceiptStatus.RECEIPT_STATUS_WRITED_OFF.getValue());
                    }else {
                        inputWaybillDTO.setBillStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
                    }
                    // 已冲销数量
                    List<BizReceiptInputWaybill> allReadyWriteOffList = bizReceiptInputWaybillDataWrap.list(new QueryWrapper<BizReceiptInputWaybill>().lambda()
                            .isNotNull(BizReceiptInputWaybill::getWriteOffDocDate)
                            .eq(BizReceiptInputWaybill::getPreInputWaybillId, inputWaybillDTO.getPreInputWaybillId()));
                    if(UtilCollection.isNotEmpty(allReadyWriteOffList)){
                        BigDecimal allReadyWriteOffQty = BigDecimal.ZERO;
                        for(BizReceiptInputWaybill allReadyWriteOffWaybill : allReadyWriteOffList){
                            allReadyWriteOffQty = allReadyWriteOffQty.add(allReadyWriteOffWaybill.getQty());
                        }
                        inputWaybillDTO.setAllReadyWriteOffQty(allReadyWriteOffQty);
                    }
                }
            }

            // 行项目中如果存在固定资产物料，处理物料描述显示问题
            List<Long> fixedMaterialItemsReferReceiptItemIdList = bizReceiptInputHeadDTO.getItemList().stream().filter(bizReceiptInputItemDTO -> bizReceiptInputItemDTO.getMatId() == 0L).map(BizReceiptInputItemDTO::getReferReceiptItemId).collect(Collectors.toList());
            if (fixedMaterialItemsReferReceiptItemIdList.size() > 0){
                List<ErpPurchaseReceiptItem> erpPurchaseReceiptItemList = erpPurchaseReceiptItemDataWrap.list(new QueryWrapper<ErpPurchaseReceiptItem>().lambda()
                    .in(ErpPurchaseReceiptItem::getId, fixedMaterialItemsReferReceiptItemIdList)
                );
                Map<Long, ErpPurchaseReceiptItem> erpPurchaseMap = erpPurchaseReceiptItemList.stream()
                        .collect(Collectors.toMap(ErpPurchaseReceiptItem::getId, obj -> obj));

                bizReceiptInputHeadDTO.getItemList().stream().filter(itemDTO -> itemDTO.getMatId() == 0L).forEach(itemDTO -> {
                    ErpPurchaseReceiptItem erpPurchaseList = erpPurchaseMap.get(itemDTO.getReferReceiptItemId());
                    // 科目类别subjectType == 1的为固定资产物料
                    if (null != erpPurchaseList && "1".equals(erpPurchaseList.getSubjectType())){
                        itemDTO.setMatName(erpPurchaseList.getMatNameBack());
                    }
                });
            }

        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,headDTOList);
    }


    public void  fillEntity(BizContext ctx) {
        BizReceiptInputHeadDTO headDTO= ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptWaybillDTO> waybillDTOList = headDTO.getWaybillDTOWriteOffList();
        if(UtilCollection.isNotEmpty(waybillDTOList)) {
            List<Long> inspectInputItemIdList =waybillDTOList.stream().map(p -> p.getInspectInputItemId()).collect(Collectors.toList());
            List<BizReceiptInputItemDTO>  itemDTOList = headDTO.getItemList().stream().filter(p -> inspectInputItemIdList.contains(p.getPreReceiptItemId())).collect(Collectors.toList());
            headDTO.setItemList(itemDTOList);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO,
                new ExtendVO().setOperationLogRequired(true).setAttachmentRequired(true).setRelationRequired(true),
                new ButtonVO().setButtonSave(true).setButtonSubmit(true)));
    }

    /**
     * 配货
     *
     * @param ctx 系统上下文
     */
    public void distribution(BizContext ctx) {
        /* ************************ 获取上下文参数 **********************************/
        Long itemId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        BizReceiptInputWaybill inputWaybill = bizReceiptInputWaybillDataWrap.getById(itemId);
        BizReceiptInputWaybillDTO inputWaybillDTO = UtilBean.newInstance(inputWaybill, BizReceiptInputWaybillDTO.class);
        dataFillService.fillAttr(inputWaybillDTO);
        /* ************************ 组装返回结构 **********************************/
        // 请求行项目map（作业单获取请求行项目的信息）
//        BizReceiptTaskReqItemVO reqItemVO = UtilBean.deepCopyNewInstance(reqItem, BizReceiptTaskReqItemVO.class);
//        inputItemDTO.setSpecCode(stock);
        //配货
        BizReceiptAssembleRuleSearchPO rulePo = new BizReceiptAssembleRuleSearchPO();
        rulePo.setMatId(inputWaybillDTO.getMatId());
        rulePo.setLocationId(inputWaybillDTO.getLocationId());
        rulePo.setFtyId(inputWaybillDTO.getFtyId());
        rulePo.setStockStatus(EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue());
        // 配货特性查询
        QueryWrapper<BizReceiptAssembleRule> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizReceiptAssembleRule::getReceiptType, EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue());
        BizReceiptAssembleRule specFeature = bizReceiptAssembleRuleDataWrap.getOne(wrapper);
        List<String> featureCodeList = Arrays.asList(specFeature.getFeatureCode().split(Const.COMMA));
        String itemSpecCode = "";
        String itemSpecValue = "";
        for (int i = 0; i < featureCodeList.size() ; i++) {
            String specCode = featureCodeList.get(i);
            // 去表名
            String fieldName = specCode.substring(specCode.indexOf(Const.POINT) + 1);
            // 转驼峰
            fieldName = UtilMetadata.underlineToHump(fieldName);
            // 根据字段名称获取属性的值 当属性值为空 返回null 不抛出异常
            String specValue = UtilObject.getStringOrEmpty(UtilReflect.getValueByFieldNullReturnNull(fieldName, inputWaybillDTO));
            if (UtilString.isNullOrEmpty(specValue)) {
                // 特性值为空 则不参加特性匹配
                continue;
            }
            if (i == 0){
                itemSpecCode = specCode;
                itemSpecValue = specValue;
            }else {
                itemSpecCode = itemSpecCode + Const.COMMA + specCode;
                itemSpecValue = itemSpecValue + Const.COMMA + specValue;
            }
        }
        inputWaybillDTO.setSpecCode(itemSpecCode);
        inputWaybillDTO.setSpecValue(itemSpecValue);
        List<StockBinDTO> stockBinDTOList = stockCommonService.getStockBinByFeatureCodeAndValue(inputWaybillDTO,
                inputWaybillDTO.getFtyId(), inputWaybillDTO.getLocationId(), inputWaybillDTO.getMatId(),
                EnumStockStatus.STOCK_BATCH_STATUS_UNRESTRICTED.getValue(), inputWaybillDTO.getBizBatchInfoDTO().getSpecStock());
        inputWaybillDTO.setStockBinList(stockBinDTOList);

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, inputWaybillDTO);
    }
}
