package com.inossem.wms.bizdomain.unitized.service.component;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.material.service.datawrap.DicMaterialFactoryDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.task.service.biz.LoadTaskService;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqHeadDataWrap;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumDefaultStorageType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputWaybillDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqHead;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqItem;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.dto.PrintItemDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UnitizedPrintComponent {

    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;

    @Autowired
    protected BatchInfoService bizBatchInfoService;

    @Autowired
    protected BatchImgService bizBatchImgService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected LoadTaskService loadTaskService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private BizReceiptTaskReqItemDataWrap bizReceiptTaskReqItemDataWrap;

    @Autowired
    private BizReceiptTaskReqHeadDataWrap bizReceiptTaskReqHeadDataWrap;
    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();


        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }


        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptInputHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizReceiptInputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);


        // 过滤行项目
        if(UtilCollection.isEmpty(po.getBizLabelPrintDTO().getPrintItemDTOList())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }

        Set<Long> itemIdSet = po.getBizLabelPrintDTO().getPrintItemDTOList().stream().map(PrintItemDTO::getItemId).collect(Collectors.toSet());

        Map<Long,PrintItemDTO> printMap = po.getBizLabelPrintDTO().getPrintItemDTOList().stream().collect(Collectors.toMap(e->e.getItemId(), e->e));
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList =new ArrayList<>();
        headDTO.getItemList().removeIf(o->UtilCollection.isEmpty(o.getInputWaybillList()));
        for (BizReceiptInputItemDTO itemDTO : headDTO.getItemList()) {
            for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                inputWaybillDTOList.add(inputWaybillDTO);
            }
        }

        List<BizReceiptInputWaybillDTO> itemList = inputWaybillDTOList.stream().filter(e->itemIdSet.contains(e.getId())).collect(Collectors.toList());

        if(UtilCollection.isEmpty(itemList)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        for(BizReceiptInputWaybillDTO itemDTO:itemList){
            PrintItemDTO print = printMap.get(itemDTO.getId());


            itemDTO.setPrintNum(print.getPrintNum());

        }


        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }



    @Autowired
    private DicMaterialFactoryDataWrap dicMaterialFactoryDataWrap;


    /**
     * 填充打印数据
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();
        // 装载打印机打印数据
        List<LabelReceiptInputBox> receiptInputBoxes = new ArrayList<>();
        // 装载要更新的批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptInputHeadDTO headDTO = (BizReceiptInputHeadDTO) po.getHeadDTO();
        // 新建打印实体对象
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList =new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : headDTO.getItemList()) {
            for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                inputWaybillDTOList.add(inputWaybillDTO);
            }
        }

        List<BizReceiptInputWaybillDTO> itemDTOList = inputWaybillDTOList.stream().filter(e->e.getPrintNum()!=null&&e.getPrintNum()>0).collect(Collectors.toList());

        List<BizReceiptInputItemDTO> inputItemDTOList = headDTO.getItemList();
        Map<Long, BizReceiptInputItemDTO> inputItemDTOMap = inputItemDTOList.stream().collect(Collectors.toMap(e->e.getId(), e->e));
        int printCount = 0;
        for(BizReceiptInputWaybillDTO itemDTO:itemDTOList){
            printCount = printCount + itemDTO.getPrintNum();
        }

        List<String> labelCodeList = bizCommonService.getNextNValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue(),printCount);
        int la = 0;

        for(BizReceiptInputWaybillDTO itemDTO:itemDTOList ) {
            Long typeId = dictionaryService.getStorageTypeIdCacheByCode(itemDTO.getWhCode(), EnumDefaultStorageType.INPUT.getTypeCode());
            Long binId = dictionaryService.getBinIdCacheByCode(itemDTO.getWhCode(), EnumDefaultStorageType.INPUT.getTypeCode(), EnumDefaultStorageType.INPUT.getBinCode());

            BizReceiptInputItemDTO inputItemDTO = inputItemDTOMap.get(itemDTO.getItemId());
            inputItemDTO.setInputType("冻结");
            // 前置单据类型是有条件放行则为冻结入库
            if(inputItemDTO.getPreReceiptType().equals(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue())
                    || inputItemDTO.getPreReceiptType().equals(EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue())){
                inputItemDTO.setInputType("普通");
            }

            // 设置要更新的批次信息数据
            itemDTO.getBizBatchInfoDTO().setInspectHeadId(headDTO.getId());
            itemDTO.getBizBatchInfoDTO().setInspectItemId(itemDTO.getId());
            itemDTO.getBizBatchInfoDTO()
                    .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
            itemDTO.getBizBatchInfoDTO().setInspectCode(itemDTO.getReceiptCode());
            itemDTO.getBizBatchInfoDTO().setInspectUserId(user.getId());
            itemDTO.getBizBatchInfoDTO().setTagType(2);
            bizBatchInfoDTOList.add(itemDTO.getBizBatchInfoDTO());
            for (int i = 0; i < itemDTO.getPrintNum(); i++) {
                String labelCode =  labelCodeList.get(la);
                la++;
                // 设置需要保存的标签数据
                BizLabelDataDTO label = BizLabelDataDTO.builder()
                        .id(null)
                        .matId(itemDTO.getMatId())
                        .ftyId(itemDTO.getFtyId())
                        .locationId(itemDTO.getLocationId())
                        .batchId(itemDTO.getBizBatchInfoDTO().getId())
                        .binId(binId)
                        .whId(itemDTO.getWhId())
                        .typeId(typeId)
                        .labelCode(labelCode)
                        .snCode(labelCode)
                        .qty(BigDecimal.ZERO)
                        .labelType(itemDTO.getBizBatchInfoDTO().getTagType())
                        .receiptHeadId(headDTO.getId())
                        .receiptItemId(itemDTO.getId())  //上架请求对应的是运单表id
                        .receiptType(headDTO.getReceiptType())
                        .preReceiptHeadId(inputItemDTO.getPreReceiptHeadId())
                        .preReceiptItemId(itemDTO.getId())
                        .preReceiptType(inputItemDTO.getPreReceiptType())
                        .build();
                labelDataList.add(label);
                printCount(receiptInputBoxes, inputItemDTO, itemDTO,labelCode);
            }

        }
        /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("验收入库-PDA端-打印-更新批次信息成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
        List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelDataService.saveBatchDto(labelDataList);
            log.info("验收入库-PDA端-打印-插入标签数据成功 " + JSONObject.toJSONString(labelDataList));

            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(label.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }
            log.info("验收入库-PDA端-打印-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }
        List<BizReceiptTaskReqItem> reqItemList = this.getReqByPreHeadId(headDTO.getId());
        if(UtilCollection.isNotEmpty(reqItemList)){

            Map<Long, BizReceiptTaskReqItem> reqItemMap = reqItemList.stream().collect(Collectors.toMap(e->e.getPreReceiptItemId(), e->e,(k1,k2)->k2));

            for (BizLabelDataDTO label : labelDataList) {

                BizReceiptTaskReqItem reqItem = reqItemMap.get(label.getReceiptItemId());
                if(reqItem!=null){
                    BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                    bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                    bizLabelReceiptRel.setId(null);
                    bizLabelReceiptRel.setLabelId(label.getId());
                    bizLabelReceiptRel.setReceiptType(EnumReceiptType.STOCK_TASK_REQ_SHELF_LOAD.getValue());
                    bizLabelReceiptRel.setReceiptHeadId(reqItem.getHeadId());
                    bizLabelReceiptRel.setReceiptItemId(reqItem.getId());
                    bizLabelReceiptRel.setPreReceiptType(reqItem.getPreReceiptType());
                    bizLabelReceiptRel.setPreReceiptHeadId(reqItem.getPreReceiptHeadId());
                    bizLabelReceiptRel.setPreReceiptItemId(reqItem.getPreReceiptItemId());
                    bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                    bizLabelReceiptRelList.add(bizLabelReceiptRel);
                }

            }

        } else {
            log.error("入库单单据{},因缺少作业请求信息无法添加上架请求与标签对应的关联关系，标签打印失败", headDTO.getReceiptCode());
            throw new WmsException(EnumReturnMsg.RETURN_CODE_REQUEST_ALREADY_IN_PROGRESS_EXCEPTION);
        }

        labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
        // 填充打印信息
        printInfo.setLabelBoxList(receiptInputBoxes);

        if (UtilCollection.isNotEmpty(receiptInputBoxes)) {
            Map<String, Long> labelDataMap = labelDataList.stream().collect(Collectors.toMap(e->e.getLabelCode(), e->e.getId()));
            for(LabelReceiptInputBox box:receiptInputBoxes){
                Long labelId = labelDataMap.get(box.getRfidCode());
                if(labelId!=null){
                    box.setLabelId(labelId);
                }
            }
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,receiptInputBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message =
                ProducerMessageContent.messageContent(TagConst.PRINT_INSPECT_INPUT_BOX_LABEL_UNITIZED, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }
    /**
     * 获取上架请求
     * @param headId
     * @return
     */
    private List<BizReceiptTaskReqItem> getReqByPreHeadId(Long headId){
        List<BizReceiptTaskReqItem> reqItems = new ArrayList<>();

        QueryWrapper<BizReceiptTaskReqItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTaskReqItem::getPreReceiptHeadId, headId);

        reqItems = bizReceiptTaskReqItemDataWrap.list(queryWrapper);
        if(UtilCollection.isNotEmpty(reqItems)){
            List<Long> reqHeadIdList = reqItems.stream().map(e->e.getHeadId()).collect(Collectors.toList());

            QueryWrapper<BizReceiptTaskReqHead> headQueryWrapper = new QueryWrapper<>();
            headQueryWrapper.lambda().in(BizReceiptTaskReqHead::getId, reqHeadIdList);
            headQueryWrapper.lambda().eq(BizReceiptTaskReqHead::getReceiptType, EnumReceiptType.UNITIZED_STOCK_TASK_REQ_SHELF_LOAD.getValue());
            List<BizReceiptTaskReqHead> headList = bizReceiptTaskReqHeadDataWrap.list(headQueryWrapper);
            if(UtilCollection.isNotEmpty(headList)){
                List<Long> findHeadIdList = headList.stream().map(e->e.getId()).collect(Collectors.toList());
                reqItems = reqItems.stream().filter(e->findHeadIdList.contains(e.getHeadId())).collect(Collectors.toList());
            }

        }

        return reqItems;
    }
    /**
     * 标签打印的数据
     * @param receiptInputBoxes
     * @param itemDTO
     * @param labelCode
     */
    private void printCount(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptInputItemDTO itemDTO, BizReceiptInputWaybillDTO inputWaybillDTO, String labelCode) {
        // 设置需要打印的数据
        LabelReceiptInputBox labelReceiptInputBox = UtilBean.newInstance(inputWaybillDTO, LabelReceiptInputBox.class);
        labelReceiptInputBox.setTagType(inputWaybillDTO.getBizBatchInfoDTO().getTagType()); // 标签类型
        labelReceiptInputBox.setIsSingle(inputWaybillDTO.getBizBatchInfoDTO().getIsSingle()); // 单品/批次
        labelReceiptInputBox.setBatchCode(inputWaybillDTO.getBizBatchInfoDTO().getBatchCode());
        labelReceiptInputBox.setRfidCode(labelCode);
        labelReceiptInputBox.setCorpName(itemDTO.getCorpName());
        labelReceiptInputBox.setReferReceiptCode(itemDTO.getReferReceiptCode());
        labelReceiptInputBox.setMatDocCode(inputWaybillDTO.getMatDocCode());
        labelReceiptInputBox.setMatDocRid(inputWaybillDTO.getMatDocRid());
        labelReceiptInputBox.setItemId(inputWaybillDTO.getId());
        if(inputWaybillDTO.getBizBatchInfoDTO()!=null){
            labelReceiptInputBox.setLifetimeDate(inputWaybillDTO.getBizBatchInfoDTO().getLifetimeDate());
        }


        // 0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵
        switch (inputWaybillDTO.getWaybillDTO().getPackageType()) {
            case 0:
                labelReceiptInputBox.setPackageTypeI18n("不需要包装");
                break;
            case 1:
                labelReceiptInputBox.setPackageTypeI18n("防潮、防撞、防尘包装");
                break;
            case 2:
                labelReceiptInputBox.setPackageTypeI18n("避光包装");
                break;
            case 3:
                labelReceiptInputBox.setPackageTypeI18n("防锈包装");
                break;
            case 4:
                labelReceiptInputBox.setPackageTypeI18n("防静电包装");
                break;
            case 5:
                labelReceiptInputBox.setPackageTypeI18n("真空包装");
                break;
            case 6:
                labelReceiptInputBox.setPackageTypeI18n("原包装");
                break;
            case 7:
                labelReceiptInputBox.setPackageTypeI18n("双面保护");
                break;
            case 8:
                labelReceiptInputBox.setPackageTypeI18n("端口封堵");
                break;
            default:
                labelReceiptInputBox.setPackageTypeI18n(" ");
        }

        labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());

        // 采购包号
        labelReceiptInputBox.setExtend2(inputWaybillDTO.getWaybillDTO().getExtend2());
        // 存储级别
        labelReceiptInputBox.setLocationCode(inputWaybillDTO.getWaybillDTO().getLocationCode());
        // UP码
        labelReceiptInputBox.setExtend29(inputWaybillDTO.getWaybillDTO().getExtend29());
        // 物资类型
        labelReceiptInputBox.setExtend28(inputWaybillDTO.getWaybillDTO().getExtend28());
        // 质保分级
        labelReceiptInputBox.setExtend27(inputWaybillDTO.getWaybillDTO().getExtend27());
        // 安全分级
        labelReceiptInputBox.setExtend26(inputWaybillDTO.getWaybillDTO().getExtend26());
        // 功能位置码/物资编码
        labelReceiptInputBox.setFunctionalLocationCode(inputWaybillDTO.getWaybillDTO().getFunctionalLocationCode());
        labelReceiptInputBox.setExtend20(inputWaybillDTO.getWaybillDTO().getExtend20());
        // 入库类型
        labelReceiptInputBox.setInputType(itemDTO.getInputType());
        // 公司名称
        labelReceiptInputBox.setCorpName(itemDTO.getCorpName());

        receiptInputBoxes.add(labelReceiptInputBox);
    }



}
