package com.inossem.wms.bizdomain.returns.service.component.callback;

import com.alibaba.fastjson.JSONObject;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizdomain.returns.service.component.ReturnComponent;
import com.inossem.wms.bizdomain.returns.service.datawrap.BizReceiptReturnBinDataWrap;
import com.inossem.wms.bizdomain.returns.service.datawrap.BizReceiptReturnHeadDataWrap;
import com.inossem.wms.bizdomain.returns.service.datawrap.BizReceiptReturnItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnHeadDTO;
import com.inossem.wms.common.model.bizdomain.returns.dto.BizReceiptReturnItemDTO;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnBin;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnHead;
import com.inossem.wms.common.model.bizdomain.returns.entity.BizReceiptReturnItem;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskItemDTO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 退库回调组件
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2021-04-27
 */

@Service
@Slf4j
public class ReturnTaskCallbackComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private ReturnComponent returnComponent;

    @Autowired
    private BizReceiptReturnHeadDataWrap bizReceiptReturnHeadDataWrap;

    @Autowired
    private BizReceiptReturnItemDataWrap bizReceiptReturnItemDataWrap;

    @Autowired
    private BizReceiptReturnBinDataWrap bizReceiptReturnBinDataWrap;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    protected BatchInfoService bizBatchInfoService;

    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private LabelDataService labelDataService;

    /**
     * 获取行项目作业数量map
     *
     * @param taskItemDTOList
     * @return
     */
    public Map<Long, BigDecimal> getItemTaskQtyMap(List<BizReceiptTaskItemDTO> taskItemDTOList) {
        // item的已作业数
        Map<Long, BigDecimal> modifyItemTaskQtyMap = new HashMap<>();
        for (BizReceiptTaskItemDTO itemDTO : taskItemDTOList) {
            Long itemId = itemDTO.getPreReceiptItemId();
            BigDecimal modifyItemTaskQty = itemDTO.getQty();
            if (modifyItemTaskQtyMap.containsKey(itemId)) {
                modifyItemTaskQty = modifyItemTaskQty.add(modifyItemTaskQtyMap.get(itemId));
            }
            modifyItemTaskQtyMap.put(itemId, modifyItemTaskQty);
        }
        return modifyItemTaskQtyMap;
    }

    /**
     * 获取bin作业数量map
     *
     * @param taskItemDTOList
     * @return
     */
    public Map<Long, BigDecimal> getBinTaskQtyMap(List<BizReceiptTaskItemDTO> taskItemDTOList) {
        // bin的已作业数
        Map<Long, BigDecimal> modifyBinTaskQtyMap = new HashMap<>();
        for (BizReceiptTaskItemDTO itemDTO : taskItemDTOList) {
            Long binId = itemDTO.getPreReceiptBinId();
            BigDecimal modifyBinTaskQty = itemDTO.getQty();
            if (modifyBinTaskQtyMap.containsKey(binId)) {
                modifyBinTaskQty = modifyBinTaskQty.add(modifyBinTaskQtyMap.get(binId));
            }
            modifyBinTaskQtyMap.put(binId, modifyBinTaskQty);
        }
        return modifyBinTaskQtyMap;
    }

    /**
     * 修改行项目状态及已作业数
     *
     * @param modifyItemTaskQtyMap 数量map
     */
    public List<BizReceiptReturnItem> updateItemTaskQty(Map<Long, BigDecimal> modifyItemTaskQtyMap,
        List<BizReceiptReturnItem> itemList) {
        List<BizReceiptReturnItem> updateTaskQtyList = new ArrayList<>();
        // 更新数量
        for (Long itemId : modifyItemTaskQtyMap.keySet()) {
            for (BizReceiptReturnItem item : itemList) {
                if (item.getId().equals(itemId)) {
                    BigDecimal taskQty = item.getTaskQty().add(modifyItemTaskQtyMap.get(itemId));
                    item.setTaskQty(taskQty);
                    updateTaskQtyList.add(item);
                    break;
                }
            }
        }
        bizReceiptReturnItemDataWrap.updateBatchById(updateTaskQtyList);
        return updateTaskQtyList;
    }

    /**
     * 修改行项目的已作业数量
     *
     * @param modifyBinTaskQtyMap 数量map
     */
    public void updateBinTaskQty(Map<Long, BigDecimal> modifyBinTaskQtyMap) {
        List<BizReceiptReturnBin> updateTaskQtyList = new ArrayList<>();
        Set<Long> binIdSet = modifyBinTaskQtyMap.keySet();
        List<BizReceiptReturnBin> binList = bizReceiptReturnBinDataWrap.listByIds(binIdSet);
        for (Long binId : modifyBinTaskQtyMap.keySet()) {
            for (BizReceiptReturnBin bin : binList) {
                if (bin.getId().equals(binId)) {
                    BigDecimal taskQty = bin.getTaskQty().add(modifyBinTaskQtyMap.get(binId));
                    bin.setTaskQty(taskQty);
                    updateTaskQtyList.add(bin);
                    break;
                }
            }
        }
        bizReceiptReturnBinDataWrap.updateBatchById(updateTaskQtyList);
    }

    /**
     * 更新数量和状态
     *
     * @param ctx 上下文
     */
    public void updateQtyAndStatus(BizContext ctx) {
        BizReceiptTaskHeadDTO bizReceiptTaskHeadDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptTaskItemDTO> taskItemDTOList = bizReceiptTaskHeadDTO.getBizReceiptTaskItemDTOList();
        // 获取退库单id
        Long headId = taskItemDTOList.get(0).getPreReceiptHeadId();
        if (UtilCollection.isNotEmpty(taskItemDTOList)) {
            // item的已作业数
            Map<Long, BigDecimal> modifyItemTaskQtyMap = this.getItemTaskQtyMap(taskItemDTOList);
            // bin的已作业数
            Map<Long, BigDecimal> modifyBinTaskQtyMap = this.getBinTaskQtyMap(taskItemDTOList);
            // 获取作业模式
            List<BizReceiptReturnItem> itemList = bizReceiptReturnItemDataWrap.listByIds(modifyItemTaskQtyMap.keySet());
            boolean isPostFirstMode = itemList.stream().allMatch(a -> UtilString.isNotNullOrEmpty(a.getMatDocCode()));
            // 修改bin已作业数量
            if (!modifyItemTaskQtyMap.isEmpty()) {
                this.updateBinTaskQty(modifyBinTaskQtyMap);
            }
            // 修改行项目已作业数量和状态
            if (!modifyItemTaskQtyMap.isEmpty()) {
                this.updateItemQtyAndStatus(modifyItemTaskQtyMap, itemList, isPostFirstMode);
            }
            // 修改单据状态自动过账
            if (Objects.nonNull(headId)) {
                this.updateReceiptStatus(headId, isPostFirstMode);
            }
        }
        BizReceiptReturnHeadDTO headDTO = UtilBean.newInstance(bizReceiptReturnHeadDataWrap.getById(headId), BizReceiptReturnHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        // 设置上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_ID, headId);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, bizReceiptTaskHeadDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
    }

    /**
     * 更新行项目状态
     * 
     * @param itemList 行项目
     * @param isPostFirstMode
     */
    public void updateItemStatus(List<BizReceiptReturnItem> itemList, Boolean isPostFirstMode) {
        // 更新状态
        List<BizReceiptReturnItem> updateStatusList = new ArrayList<>();
        for (BizReceiptReturnItem item : itemList) {
            BigDecimal taskQty = item.getTaskQty();
            BigDecimal qty = item.getQty();
            if (isPostFirstMode) {
                // 当已作业数量等于出库数量时，修改状态【已完成】,否则修改为【作业中】
                if (taskQty.equals(qty)) {
                    item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
                } else {
                    item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
                }
            } else {
                // 当已作业数量等于出库数量时，修改状态【已作业】,否则修改为【作业中】
                if (taskQty.equals(qty)) {
                    item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
                } else {
                    item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
                }
            }
            updateStatusList.add(item);
        }
        bizReceiptReturnItemDataWrap.updateBatchById(updateStatusList);
    }

    /**
     * 更新行项目数量和状态
     *
     * @param modifyItemTaskQtyMap
     */
    public void updateItemQtyAndStatus(Map<Long, BigDecimal> modifyItemTaskQtyMap, List<BizReceiptReturnItem> itemList,
        Boolean isPostFirstMode) {
        // 更新数量
        List<BizReceiptReturnItem> updateTaskQtyList = this.updateItemTaskQty(modifyItemTaskQtyMap, itemList);
        // 更新状态
        this.updateItemStatus(updateTaskQtyList, isPostFirstMode);
    }

    /**
     * 修改单据状态
     *
     * @param headId headId
     * @param isPostFirstMode 是否先过账模式
     */
    public void updateReceiptStatus(Long headId, Boolean isPostFirstMode) {
        List<Integer> itemStatusList = returnComponent.getItemStatusList(headId);
        Set<Integer> itemStatusSet = new HashSet<>(itemStatusList);
        BizReceiptReturnHeadDTO headDTO =
            UtilBean.newInstance(bizReceiptReturnHeadDataWrap.getById(headId), BizReceiptReturnHeadDTO.class);
        if (isPostFirstMode) {
            // 行项目状态都是【已完成】【冲销中】【已冲销】，更新单据状态为【已完成】，否则单据更新为【作业中】
            if (bizCommonService.getCompletedItemStatusSet().containsAll(itemStatusSet)) {
                returnComponent.updateReceiptStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
            } else {
                returnComponent.updateReceiptStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
            }
        } else {
            // 判断所有行项目都是【已作业】，单据更新为【已作业】，否则单据更新为【作业中】
            if (itemStatusSet.size() == 1 && itemStatusSet.contains(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
                returnComponent.updateReceiptStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
            } else {
                returnComponent.updateReceiptStatus(headDTO, EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
            }
        }
    }


    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        if (UtilString.isNullOrEmpty(po.getBizLabelPrintDTO().getPrinterIp())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_IP_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrinterPort())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PORT_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptReturnHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }


    /**
     * 填充打印数据
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();
        // 装载打印机打印数据
        List<LabelReceiptInputBox> receiptInputBoxes = new ArrayList<>();
        // 装载要更新的批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptReturnHeadDTO headDTO = (BizReceiptReturnHeadDTO) po.getHeadDTO();
        // 新建打印实体对象
        List<BizReceiptReturnItemDTO> itemDTOList = headDTO.getItemDTOList();

        itemDTOList.forEach(itemDTO->{
            // 生成标签编码
            String labelCode =
                    bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());

            // 标签打印数据
            this.setPrintLabelData(receiptInputBoxes, itemDTO, labelCode);

            // 设置要更新的批次信息数据
            // TODO-BO: 2022/7/9 这里没有遍历批次信息
            itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().setInspectHeadId(itemDTO.getHeadId());
            itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().setInspectItemId(itemDTO.getId());
            itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo()
                    .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
            itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().setInspectCode(itemDTO.getReceiptCode());
            itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().setInspectUserId(user.getId());
            itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().setTagType(2);
            bizBatchInfoDTOList.add(itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo());

            // 设置需要保存的标签数据
            BizLabelDataDTO label = BizLabelDataDTO.builder()
                    .id(null)
                    .matId(itemDTO.getMatId())
                    .ftyId(itemDTO.getFtyId())
                    .locationId(itemDTO.getLocationId())
                    .batchId(itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().getId())
                    .binId(itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().getBinId())
                    .whId(itemDTO.getWhId())
                    .typeId(itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getTypeId())
                    .labelCode(labelCode)
                    .snCode(labelCode)
                    .qty(itemDTO.getQty())
                    .labelType(itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().getTagType())
                    .receiptHeadId(itemDTO.getHeadId())
                    .receiptItemId(itemDTO.getId())
                    .receiptType(itemDTO.getReceiptType())
                    .preReceiptHeadId(itemDTO.getPreReceiptHeadId())
                    .preReceiptItemId(itemDTO.getPreReceiptItemId())
                    .preReceiptType(itemDTO.getPreReceiptType())
                    .build();
            labelDataList.add(label);

        });

        /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("验收入库-PDA端-打印-更新批次信息成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelDataService.saveBatchDto(labelDataList);
            log.info("验收入库-PDA端-打印-插入标签数据成功 " + JSONObject.toJSONString(labelDataList));
            List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(label.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
            log.info("验收入库-PDA端-打印-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }

        // 填充打印信息
        printInfo.setLabelBoxList(receiptInputBoxes);

        if (UtilCollection.isNotEmpty(receiptInputBoxes)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,receiptInputBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.PRINT_INSPECT_INPUT_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 设置标签打印数据
     * @param receiptInputBoxes 装载打印机打印数据
     * @param itemDTO 要打印的验收单行项目
     * @param labelCode rfid 编码
     * @return
     */
    private void setPrintLabelData(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptReturnItemDTO itemDTO, String labelCode) {

        // 单品打印将行项目拆分
        if (itemDTO.getItemInfoList().get(EnumRealYn.FALSE.getIntValue()).getBatchInfo().getIsSingle().equals("单品")){
            int qty = itemDTO.getQty().intValue();
            for (int i = 0; i < qty; i++) {
                printCount(receiptInputBoxes, itemDTO, labelCode);
            }
        }else { // 批次打印不需要拆分
            printCount(receiptInputBoxes, itemDTO, labelCode);
        }

    }

    /**
     * 标签打印的数据
     * @param receiptInputBoxes
     * @param itemDTO
     * @param labelCode
     */
    private void printCount(List<LabelReceiptInputBox> receiptInputBoxes, BizReceiptReturnItemDTO itemDTO, String labelCode) {
        // 设置需要打印的数据
        LabelReceiptInputBox labelReceiptInputBox = UtilBean.newInstance(itemDTO, LabelReceiptInputBox.class);
        labelReceiptInputBox.setTagType(itemDTO.getItemInfoList().get(0).getBatchInfo().getTagType());
        labelReceiptInputBox.setIsSingle(itemDTO.getItemInfoList().get(0).getBatchInfo().getIsSingle());
        labelReceiptInputBox.setBatchCode(itemDTO.getItemInfoList().get(0).getBatchInfo().getBatchCode());
        labelReceiptInputBox.setRfidCode(labelCode);
        labelReceiptInputBox.setLifetimeDate(itemDTO.getItemInfoList().get(0).getBatchInfo().getLifetimeDate());
        // 0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵
        switch (itemDTO.getPackageType()) {
            case 0:
                labelReceiptInputBox.setPackageTypeI18n("不需要包装");
                break;
            case 1:
                labelReceiptInputBox.setPackageTypeI18n("防潮、防撞、防尘包装");
                break;
            case 2:
                labelReceiptInputBox.setPackageTypeI18n("避光包装");
                break;
            case 3:
                labelReceiptInputBox.setPackageTypeI18n("防锈包装");
                break;
            case 4:
                labelReceiptInputBox.setPackageTypeI18n("防静电包装");
                break;
            case 5:
                labelReceiptInputBox.setPackageTypeI18n("真空包装");
                break;
            case 6:
                labelReceiptInputBox.setPackageTypeI18n("原包装");
                break;
            case 7:
                labelReceiptInputBox.setPackageTypeI18n("双面保护");
                break;
            case 8:
                labelReceiptInputBox.setPackageTypeI18n("端口封堵");
                break;
            default:
                labelReceiptInputBox.setPackageTypeI18n(" ");
        }
        labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
        receiptInputBoxes.add(labelReceiptInputBox);
    }

    /**
     * 通过head id获取item
     * @param headId head id
     * @return
     */
    private BizReceiptReturnHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptReturnHead bizReceiptReturnHead = bizReceiptReturnHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptReturnHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptReturnHeadDTO headDTO = UtilBean.newInstance(bizReceiptReturnHead, BizReceiptReturnHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }


}