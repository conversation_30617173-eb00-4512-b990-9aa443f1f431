package com.inossem.wms.bizdomain.unitized.service.component;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.inossem.wms.bizbasis.batch.service.biz.BatchImgService;
import com.inossem.wms.bizbasis.batch.service.biz.BatchInfoService;
import com.inossem.wms.bizbasis.batch.service.datawrap.BizBatchInfoDataWrap;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.material.service.biz.MaterialService;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.StockLocationService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelDataService;
import com.inossem.wms.bizbasis.rfid.service.biz.LabelReceiptRelService;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizbasis.stock.service.biz.StockCommonService;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.inconformity.service.datawrap.BizReceiptInconformityItemDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputBinDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputWaybillDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectHeadDataWrap;
import com.inossem.wms.bizdomain.register.service.datawrap.BizReceiptRegisterItemDataWrap;
import com.inossem.wms.bizdomain.task.service.biz.LoadTaskService;
import com.inossem.wms.bizdomain.task.service.datawrap.BizReceiptTaskReqItemDataWrap;
import com.inossem.wms.bizdomain.unitized.service.component.moveType.UnitizedInspectInputMoveTypeComponent;
import com.inossem.wms.bizdomain.unitized.service.component.moveType.UnitizedInspectInputWriteOffMoveTypeComponent;
import com.inossem.wms.bizdomain.unitized.service.datawrap.BizReceiptWaybillDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumImageBizType;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.EnumTagType;
import com.inossem.wms.common.enums.dataFill.EnumDataFillType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.batch.dto.BizBatchInfoDTO;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityHead;
import com.inossem.wms.common.model.bizdomain.inconformity.entity.BizReceiptInconformityItem;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputWaybillDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputBin;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputWaybill;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputDeletePO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputSearchPO;
import com.inossem.wms.common.model.bizdomain.input.po.BizReceiptInputWriteOffPO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadCallbackVO;
import com.inossem.wms.common.model.bizdomain.input.vo.BizReceiptInputHeadVO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectHead;
import com.inossem.wms.common.model.bizdomain.register.entity.BizReceiptRegisterItem;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqHeadDTO;
import com.inossem.wms.common.model.bizdomain.task.dto.BizReceiptTaskReqItemDTO;
import com.inossem.wms.common.model.bizdomain.task.entity.BizReceiptTaskReqItem;
import com.inossem.wms.common.model.bizdomain.task.po.BizReceiptTaskReqSavePo;
import com.inossem.wms.common.model.bizdomain.unitized.dto.BizReceiptWaybillDTO;
import com.inossem.wms.common.model.bizdomain.unitized.entity.BizReceiptWaybill;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.ErpReturnObject;
import com.inossem.wms.common.model.common.base.ErpReturnObjectItem;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.erp.entity.ErpPurchaseReceiptHead;
import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import com.inossem.wms.common.model.label.dto.BizLabelPrintDTO;
import com.inossem.wms.common.model.label.entity.BizLabelReceiptRel;
import com.inossem.wms.common.model.masterdata.mat.info.entity.DicMaterial;
import com.inossem.wms.common.model.org.location.dto.DicStockLocationDTO;
import com.inossem.wms.common.model.print.label.LabelReceiptInputBox;
import com.inossem.wms.common.model.stock.dto.StockInsMoveTypeDTO;
import com.inossem.wms.common.model.stock.entity.StockInsDocBatch;
import com.inossem.wms.common.model.stock.entity.StockInsDocBin;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 成套设备验收入库 组件库
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-03-12
 */

@Service
@Slf4j
public class UnitizedInspectInputComponent {

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    protected ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;

    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    @Autowired
    private BizReceiptInputBinDataWrap bizReceiptInputBinDataWrap;

    @Autowired
    private BizReceiptInputWaybillDataWrap bizReceiptInputWaybillDataWrap;

    @Autowired
    private BizReceiptRegisterItemDataWrap bizReceiptRegisterItemDataWrap;

    @Autowired
    private UnitizedInspectInputMoveTypeComponent unitizedInspectInputMoveTypeComponent;

    @Autowired
    private UnitizedInspectInputWriteOffMoveTypeComponent unitizedInspectInputWriteOffMoveTypeComponent;

    @Autowired
    private StockCommonService stockCommonService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    protected BatchInfoService bizBatchInfoService;

    @Autowired
    protected LabelReceiptRelService labelReceiptRelService;

    @Autowired
    private LabelDataService labelDataService;

    @Autowired
    private StockLocationService stockLocationService;

    @Autowired
    private MaterialService materialService;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    protected BizReceiptWaybillDataWrap bizReceiptWaybillDataWrap;
    @Autowired
    private BizReceiptInspectHeadDataWrap bizReceiptInspectHeadDataWrap;
    @Autowired
    private BizReceiptInconformityItemDataWrap bizReceiptInconformityItemDataWrap;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private BizBatchInfoDataWrap bizBatchInfoDataWrap;
    @Autowired
    protected BatchImgService bizBatchImgService;
    @Autowired
    private SysUserDataWrap sysUserDataWrap;

    @Autowired
    private BizReceiptTaskReqItemDataWrap bizReceiptTaskReqItemDataWrap;
    @Autowired
    private BizReceiptDeliveryNoticeHeadDataWrap bizReceiptDeliveryNoticeHeadDataWrap;
    @Autowired
    protected LoadTaskService loadTaskService;

    /**
     * 验收入库单-分页
     *
     * @in ctx 入参 {@link BizReceiptInputSearchPO :"查询条件对象"}
     * @out ctx 出参 {@link PageObjectVO<BizReceiptInputHeadDTO> ("dtoList":"列表数据","total":"总条数")}
     */
    public void getPage(BizContext ctx) {
        // 上下文入参
        BizReceiptInputSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        // 组装查询条件
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = this.setQueryWrapper(po,user);
        // 分页处理
        IPage<BizReceiptInputHeadVO> page = po.getPageObj(BizReceiptInputHeadVO.class);
        // 获取验收入库单
        bizReceiptInputHeadDataWrap.getInspectInputListUnitized(page, wrapper, EnumDataFillType.FILL_RLAT);
        // 返回上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 验收入库单详情
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"验收入库单详情","button":"按钮组")}
     */
    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取验收入库单
        BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizInspectInputHeadDTO =
            UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizInspectInputHeadDTO);
        // 填充打印数据-物料凭证-总价
        bizInspectInputHeadDTO.setMatDocCode(bizInspectInputHeadDTO.getItemList().get(EnumRealYn.FALSE.getIntValue()).getMatDocCode())
                .setSumPrice(bizInspectInputHeadDTO.getItemList().stream().map(com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO::getDmbtr).reduce(BigDecimal.ZERO,BigDecimal::add));
        // 填充打印数据-净单价
        bizInspectInputHeadDTO.getItemList().stream().forEach(itemDTO -> {
            BigDecimal qty = itemDTO.getQty();
            if (qty.compareTo(BigDecimal.ZERO) > 0)
                itemDTO.setSinglePrice(itemDTO.getDmbtr().divide(qty, 3, BigDecimal.ROUND_HALF_UP));
            // 设置运单总价
            if(itemDTO.getInputWaybillList()!=null){
                itemDTO.getInputWaybillList().forEach(p -> p.setTotalPrice((p.getQty().multiply(p.getWaybillDTO().getPrice()))));
            }
            // 前置单据类型是有条件放行则为冻结入库
            bizInspectInputHeadDTO.setInputType("冻结入库");
            if(itemDTO.getPreReceiptType().equals(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue())
                || itemDTO.getPreReceiptType().equals(EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue())){
                bizInspectInputHeadDTO.setInputType("正常入库");
            }
        });
        // 新增“是否进口核安全设备”信息，不允许编辑，与对应到货通知抬头信息保持一致
        if(UtilCollection.isNotEmpty(bizInspectInputHeadDTO.getItemList())){
            Long deliveryNoticeHeadId = bizInspectInputHeadDTO.getItemList().get(0).getDeliveryNoticeHeadId();
            BizReceiptDeliveryNoticeHead bizReceiptDeliveryNoticeHead = bizReceiptDeliveryNoticeHeadDataWrap.getById(deliveryNoticeHeadId);
            bizInspectInputHeadDTO.setIsSafe(bizReceiptDeliveryNoticeHead.getIsSafe());
            bizInspectInputHeadDTO.setIsRecheck(bizReceiptDeliveryNoticeHead.getIsRecheck());
        }

        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(bizInspectInputHeadDTO);
        // 设置验收入库单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
            new BizResultVO<>(bizInspectInputHeadDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 按钮组
     *
     * @param headDTO 入库单
     * @return 按钮组对象
     */
    public ButtonVO setButton(BizReceiptInputHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)) {
            // 已记账 -【冲销、打印】
            return buttonVO.setButtonPrint(true).setButtonDeal(checkIsGenerateLoadReq(headDTO));
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus)) {
            // 作业中 -【冲销】
            List<com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO> itemList = this.getItemListById(headDTO.getId()).getItemList();
            boolean canDisplayWriteOff = false;
            for (com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO inputItemDTO : itemList) {
                if (EnumRealYn.TRUE.getIntValue().equals(inputItemDTO.getIsPost())) {
                    canDisplayWriteOff = true;
                    break;
                }
            }
            return buttonVO.setButtonPrint(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            // 已完成 【打印】
            return buttonVO.setButtonPrint(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue().equals(receiptStatus)) {
            // 未同步 -【过账】
            return buttonVO.setButtonPost(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue().equals(receiptStatus)) {
            // 已作业 -【过账】
            return buttonVO.setButtonPost(true);
        }
        return buttonVO;
    }

    /**
     * 根据headId查询出库单列表
     *
     * @param headId 单据id
     * @return 出库单信息
     */
    public BizReceiptInputHeadDTO getItemListById(Long headId) {
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(headId);
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 保存验收入库前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存的验收入库单}
     */
    public void checkSaveInspectInput(BizContext ctx) {
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 校验行项目是都为空
        this.checkEmptyItem(po);
    }

    /**
     * 判断行项目是否为空
     *
     * @param headDTO 入库单
     */
    public void checkEmptyItem(BizReceiptInputHeadDTO headDTO) {
        if (UtilObject.isNotNull(headDTO)) {
            if (UtilNumber.isEmpty(headDTO.getReceiptType())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY);
            }
            if (UtilCollection.isEmpty(headDTO.getItemList())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
            }
            if (UtilString.isNotNullOrEmpty(headDTO.getRemark()) && headDTO.getRemark().length() > 200) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 保存入库单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存的入库单}
     * @out ctx 出参 {"stockInputCode" : "入库单号"},{@link BizReceiptInputHeadDTO : "已保存的入库单}
     */
    public void saveInput(BizContext ctx) {
        // 入参上下文 - 要保存的入库单
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String stockInputCode = po.getReceiptCode();
        po.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
        po.setCreateUserId(user.getId());
        po.setModifyUserId(user.getId());
        po.setCreateTime(null);
        po.setModifyTime(UtilDate.getNow());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            // 更新入库单
            bizReceiptInputHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteInputItem(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            stockInputCode = "SK" + po.getUnit() + "-CWH5" + DateUtil.thisYear() + "-IIS-" + DateUtil.format(new Date(), "MM") + bizCommonService.getNextSequenceCodeMonth(EnumSequenceCode.SEQUENCE_UNITIZED_INSPECT_INPUT.getValue());
            po.setReceiptCode(stockInputCode);
            bizReceiptInputHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }
        log.debug("保存入库单head成功!单号{},主键{}", stockInputCode, po.getId());
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        Long deliveryNoticeHeadId = null;
        if(UtilCollection.isNotEmpty(po.getWaybillDTOList())) {
            deliveryNoticeHeadId = po.getWaybillDTOList().get(0).getDeliveryNoticeHeadId();
        }
        AtomicInteger rid = new AtomicInteger(1);
        List<BizBatchInfoDTO> updateBatchInfoDtoList = new ArrayList<>();
        for (com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            itemDTO.setId(null);
            itemDTO.setHeadId(po.getId());
            itemDTO.setRid(Integer.toString(rid.getAndIncrement()));
            itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            if(EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue().equals(po.getReceiptType()) && !UtilObject.isNull(po.getPostingDate())){
                // 成套设备入库
                itemDTO.setPostingDate(po.getPostingDate());
                itemDTO.setDocDate(po.getDocDate());
            }
            itemDTO.setCreateUserId(user.getId());
            itemDTO.setModifyUserId(user.getId());
            itemDTO.setCreateTime(UtilDate.getNow());
            itemDTO.setModifyTime(UtilDate.getNow());
            itemDTO.setReceiptCode(po.getReceiptCode());
            itemDTO.setReceiptType(po.getReceiptType());
            if (UtilNumber.isEmpty(itemDTO.getDeliveryNoticeHeadId())) {
                itemDTO.setDeliveryNoticeHeadId(deliveryNoticeHeadId);
            }
        }
        // 批量保存item
        po.getItemList().forEach(item -> {
            item.setId(null);
            if(UtilObject.isNotNull(item.getBizBatchInfoDTO())) {
                item.setBatchId(item.getBizBatchInfoDTO().getId());
            }
        });
        bizReceiptInputItemDataWrap.saveBatchDto(po.getItemList());
        log.debug("保存入库单item成功!单号{},headId{}", stockInputCode, po.getId());
        // 保存运单信息
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList = new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            Integer bid = 0;
            if(UtilCollection.isNotEmpty(itemDTO.getInputWaybillList())) {
                for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                    inputWaybillDTO.setId(null);
                    inputWaybillDTO.setHeadId(po.getId());
                    inputWaybillDTO.setItemId(itemDTO.getId());
                    inputWaybillDTO.setBid(String.valueOf(++bid));
                    inputWaybillDTO.setCreateUserId(user.getId());
                    inputWaybillDTO.setModifyUserId(user.getId());
                    inputWaybillDTO.setCreateTime(UtilDate.getNow());
                    inputWaybillDTO.setModifyTime(UtilDate.getNow());
                    inputWaybillDTOList.add(inputWaybillDTO);
                }
            }
        }
        if(UtilCollection.isNotEmpty(inputWaybillDTOList)){
            bizReceiptInputWaybillDataWrap.saveBatchDto(inputWaybillDTOList);
            log.debug("保存入库单成套运单表waybill成功!单号{},headId{}", stockInputCode, po.getId());
        }

        /* ********************** item处理结束 *************************/
        /* ********************** waybill处理开始 *************************/
        // 前序单据类型
        Integer preReceiptType = po.getItemList().get(0).getPreReceiptType() ;
        if(UtilCollection.isNotEmpty(po.getWaybillDTOList()) && UtilCollection.isEmpty(inputWaybillDTOList)){
            List<BizBatchInfoDTO> saveBatchInfoDtoList = new ArrayList<>();
            inputWaybillDTOList.clear();
            for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
                Integer bid = 0;
                for (BizReceiptWaybillDTO waybillDTO : po.getWaybillDTOList()) {
                    BizReceiptInputWaybillDTO inputWaybillDTO=UtilBean.newInstance(waybillDTO, BizReceiptInputWaybillDTO.class);
                    if( EnumReceiptType.UNITIZED_INCONFORMITY_NAINTAIN.getValue().equals(preReceiptType)){ //成套设备不符合项处置
                        if(!itemDTO.getPreReceiptItemId().equals(waybillDTO.getQualityInconformityMaintainItemId())){
                            continue;
                        }
                        inputWaybillDTO.setQty(waybillDTO.getUnqualifiedQty()) ;
                    }else if (EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue().equals(preReceiptType)){//成套设备不符合项通知
                        if(!itemDTO.getPreReceiptItemId().equals(waybillDTO.getQualityInconformityNoticeItemId())){
                            continue;
                        }
                        inputWaybillDTO.setQty(waybillDTO.getUnqualifiedQty()) ;
                    }else if (EnumReceiptType.UNITIZED_CONDITIONAL_RELEASE.getValue().equals(preReceiptType)){//成套设备有条件放行
                        if(!itemDTO.getPreReceiptItemId().equals(waybillDTO.getConditionalReleaseItemId())){
                            continue;
                        }
                        inputWaybillDTO.setQty(waybillDTO.getUnqualifiedQty()) ;
                    }else{ //质检会签
                        if(!itemDTO.getPreReceiptItemId().equals(waybillDTO.getSignInspectItemId())){
                            continue;
                        }
                        inputWaybillDTO.setQty(waybillDTO.getQualifiedQty()) ;
                    }
                    inputWaybillDTO.setId(null);
                    inputWaybillDTO.setHeadId(po.getId());
                    inputWaybillDTO.setItemId(itemDTO.getId());
                    inputWaybillDTO.setBillId(waybillDTO.getId());
                    inputWaybillDTO.setBid(String.valueOf(++bid));
                    inputWaybillDTO.setCreateUserId(user.getId());
                    inputWaybillDTO.setModifyUserId(user.getId());
                    inputWaybillDTO.setCreateTime(UtilDate.getNow());
                    inputWaybillDTO.setModifyTime(UtilDate.getNow());
                    inputWaybillDTOList.add(inputWaybillDTO);
                    BizBatchInfoDTO batchInfoDto = waybillDTO.getBizBatchInfoDTO();
                    if (UtilObject.isNotNull(batchInfoDto)) {
                        batchInfoDto.setMatId(waybillDTO.getMatId());
                        batchInfoDto.setFtyId(waybillDTO.getFtyId());
                        batchInfoDto.setCreateUserId(user.getId());
                        batchInfoDto.setTagType(EnumTagType.METAL_UNRESISTANT.getValue()); // 标签类型
                        batchInfoDto.setIsSingle(EnumRealYn.FALSE.getIntValue()); // 单品/批次
                        String extend60 =batchInfoDto.getExtend60();
                        if(StringUtils.isNotEmpty(extend60)){
                            batchInfoDto.setShelfLine(Integer.valueOf(extend60));//保质期
                        }
                        String extend61 =batchInfoDto.getExtend61();
                        if(StringUtils.isNotEmpty(extend61)){
                            batchInfoDto.setMaintenanceCycle(Integer.valueOf(extend61));//维保周期
                        }

                        bizBatchInfoService.updateLifetimeDateUnitized(batchInfoDto);
                        saveBatchInfoDtoList.add(batchInfoDto);
                        inputWaybillDTO.setBizBatchInfoDTO(batchInfoDto);
                        inputWaybillDTO.setTagType(batchInfoDto.getTagType());
                        inputWaybillDTO.setIsSingle(batchInfoDto.getIsSingle());
                    }
                }
            }
            // 批量保存批次信息
            bizBatchInfoService.multiCheckUKSaveBatchInfo(saveBatchInfoDtoList);
            if(UtilCollection.isNotEmpty(saveBatchInfoDtoList)) {
                log.debug("批量保存批次信息成功!批次号{},操作人{}", saveBatchInfoDtoList.get(0).getBatchCode(), user.getUserName());
            }
            inputWaybillDTOList.forEach(inputWaybillDTO -> {
                if(UtilObject.isNotNull(inputWaybillDTO.getBizBatchInfoDTO())) {
                    inputWaybillDTO.setBatchId(inputWaybillDTO.getBizBatchInfoDTO().getId());
                }
            });
            bizReceiptInputWaybillDataWrap.saveBatchDto(inputWaybillDTOList);
        }
        /* ********************** waybill处理结束 *************************/
        // 保存单据流
        this.saveReceiptTree(po);
        // 返回单据code
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, stockInputCode);
        // 返回保存的入库单
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, po);
        // 前端刷新页面标记
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_REFRESH_ID, po.getId());
    }

    /**
     * 删除入库单行项目
     *
     * @param po 要删除的入库信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInputItem(BizReceiptInputHeadDTO po) {
        UpdateWrapper<BizReceiptInputItem> wrapperItem = new UpdateWrapper<>();
        wrapperItem.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputItem::getHeadId, po.getId());
        bizReceiptInputItemDataWrap.physicalDelete(wrapperItem);
        UpdateWrapper<BizReceiptInputBin> wrapperBin = new UpdateWrapper<>();
        wrapperBin.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputBin::getHeadId, po.getId());
        bizReceiptInputBinDataWrap.physicalDelete(wrapperBin);
        UpdateWrapper<BizReceiptInputWaybill> wrapperWaybill = new UpdateWrapper<>();
        wrapperWaybill.lambda().eq(UtilNumber.isNotEmpty(po.getId()), BizReceiptInputWaybill::getHeadId, po.getId());
        bizReceiptInputWaybillDataWrap.physicalDelete(wrapperWaybill);
    }


    /**
     * 提交验收入库单前校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的验收入库单}
     */
    public void checkSubmitInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 校验行项目是都为空 ******** */
        this.checkEmptyItem(po);
        /* ******** 提交前校验物料、库存地点是否冻结 ******** */
        this.checkFreeze(po);
        // 提交时间
        po.setSubmitTime(new Date());
        // 设置提交操作信息
        po.setSubmitUserId(ctx.getCurrentUserId());
    }

    /**
     * 提交前校验物料、库存地点是否冻结
     */
    public void checkFreeze(BizReceiptInputHeadDTO headDTO) {
        // 提交前校验物料、库存地点是否冻结
        materialService.checkFreezeMaterial(headDTO.getItemList().stream().map(com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO::getMatId)
                .distinct().collect(Collectors.toList()));
        stockLocationService.checkFreezeInputLocation(headDTO.getItemList());
    }

    /**
     * 提交验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要提交的验收入库单}
     * @out ctx 出参 {"stockInputCode" : "验收入库单号"}
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        po.setPostingDate(new Date());
        po.setDocDate(new Date());
        // 保存验收入库单
        this.saveInput(ctx);
    }

    /**
     * 验收入库过账前数据校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要过账的验收入库单}
     */
    public void checkInspectInputPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取入库单
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getId());
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性及父子属性
        dataFillService.fillAttr(inputHeadDTO);
        // 校验数据
        this.checkEmptyItem(inputHeadDTO);
        // 校验可过账的状态
        Set<Integer> canRePostStatusSet = new HashSet<>();
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
        canRePostStatusSet.add(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
        if (!canRePostStatusSet.contains(inputHeadDTO.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_POST);
        }
    }

    /**
     * 入库单过账处理
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "验收入库单}
     */
    public void handleInputReceiptPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 生成ins凭证
        this.generateInsDocToPost(ctx);
        // sap入库过账
        this.postInputToSap(ctx);
        // ins入库过账
        this.postInputToIns(ctx);
        // 更新批次入库时间
        this.updateInputDate(ctx);
        // 普通标签生成上架请求
        this.generateLoadReq(ctx);
    }

    /**
     * 生成ins凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "验收入库单}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证}
     */
    public void generateInsDocToPost(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 装载ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成ins凭证
            insMoveTypeDTO = unitizedInspectInputMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 设置ins凭证到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }

    /**
     * sap入库过账
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"},{@link StockInsMoveTypeDTO : "ins凭证"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "补全凭证【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间】"}
     */
    public void postInputToSap(BizContext ctx) {

        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList =new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : headDTO.getItemList()) {
            for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                inputWaybillDTO.setInputPreReceiptType(itemDTO.getPreReceiptType());
                inputWaybillDTOList.add(inputWaybillDTO);
            }
        }
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ******** 设置入库单账期 ******** */
        inputWaybillDTOList.forEach(p -> p.setPostingDate(headDTO.getPostingDate()));
        this.setInPostDate(inputWaybillDTOList, user);
        // 运单行合格数量等于0 或运单行单价等于0的 更新运单过账信息【过帐日期、凭证时间、sap过账标识】
        List<BizReceiptInputWaybillDTO> unToSapItemDTOList = inputWaybillDTOList.stream().filter(p -> p. getQty().compareTo(BigDecimal.ZERO) == 0
                || p.getWaybillDTO().getPrice().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        if(UtilCollection.isNotEmpty(unToSapItemDTOList)) {
            unToSapItemDTOList.forEach(p -> p.setIsPost(EnumRealYn.TRUE.getIntValue()));
            bizReceiptInputWaybillDataWrap.updateBatchDtoById(unToSapItemDTOList);
        }
        // 运单行合格数量大于0 运单行单价大于0的 过账SAP
        List<BizReceiptInputWaybillDTO> itemListNotSync = inputWaybillDTOList.stream().filter(p -> p. getQty().compareTo(BigDecimal.ZERO) > 0
                && p.getWaybillDTO().getPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        itemListNotSync.forEach(p -> {
            BizReceiptInputItemDTO inputItemDTO = UtilBean.newInstance(bizReceiptInputItemDataWrap.getById(p.getItemId()), BizReceiptInputItemDTO.class);
            dataFillService.fillAttr(inputItemDTO);
            Long arrivalRegisterItemId = p.getWaybillDTO().getArrivalRegisterItemId();
            if (UtilNumber.isNotEmpty(p.getWaybillDTO().getPreId())) {
                BizReceiptWaybill preWaybill = bizReceiptWaybillDataWrap.getById(p.getWaybillDTO().getPreId());
                arrivalRegisterItemId = preWaybill.getArrivalRegisterItemId();
            }
            BizReceiptRegisterItem registerItem = bizReceiptRegisterItemDataWrap.getById(arrivalRegisterItemId);
            p.setInspectInputItem(inputItemDTO).setPreMatDocCode(registerItem.getMatDocCode()).setPreMatDocRid(registerItem.getMatDocRid()).setPreMatDocYear(registerItem.getMatDocYear());
        });
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.postingNew(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd", SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    List<BizReceiptInputItemDTO> inputItemDTOList=headDTO.getItemList();
                    for (BizReceiptInputItemDTO inputItemDTO : inputItemDTOList) {
                        // 获取当前item返回对象
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptCode().equals(inputItemDTO.getReceiptCode())
                                        && item.getReceiptRid().equals(inputItemDTO.getRid()) )
                                .findFirst().orElse(null);
                        if (UtilObject.isNull(currentReturnObject)) {
                            continue;
                        }
                        String matDocCode = currentReturnObject.getMatDocCode();
                        inputItemDTO.setMatDocCode(matDocCode);
                        String matDocRid = currentReturnObject.getMatDocRid();
                        inputItemDTO.setMatDocRid(matDocRid);
                        inputItemDTO.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        inputItemDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                    }
                    for (BizReceiptInputWaybillDTO inputWaybillDTO : itemListNotSync) {
                        // 获取当前item返回对象
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptCode().equals(inputWaybillDTO.getInspectInputItem().getReceiptCode())
                                        && item.getReceiptRid().equals(inputWaybillDTO.getInspectInputItem().getRid())
                                        && item.getReceiptBid().equals(inputWaybillDTO.getBid()))
                                .findFirst().orElse(null);
                        if (UtilObject.isNull(currentReturnObject)) {
                            continue;
                        }
                        String matDocCode = currentReturnObject.getMatDocCode();
                        inputWaybillDTO.setMatDocCode(matDocCode);
                        String matDocRid = currentReturnObject.getMatDocRid();
                        inputWaybillDTO.setMatDocRid(matDocRid);
                        inputWaybillDTO.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        inputWaybillDTO.setIsPost(EnumRealYn.TRUE.getIntValue());
                        inputWaybillDTO.setDmbtr(currentReturnObject.getDmbtr());
                        Date postingDate = inputWaybillDTO.getPostingDate();
                        Long batchId = inputWaybillDTO.getBatchId();
                        if ((postingDate != null) && UtilNumber.isNotEmpty(batchId)) {
                            bizBatchInfoDataWrap.updateMatDoc(batchId, postingDate, matDocCode, matDocRid);
                        }
                        // 过账成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch insDocBatch : insMoveTypeDTO.getInsDocBatchList()) {
                                if (insDocBatch.getPreReceiptItemId().equals(inputWaybillDTO.getId())) {
                                    insDocBatch.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBatch.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBatch.setPostingDate(inputWaybillDTO.getPostingDate());
                                    insDocBatch.setDocDate(inputWaybillDTO.getDocDate());
                                    insDocBatch.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                            for (StockInsDocBin insDocBin : insMoveTypeDTO.getInsDocBinList()) {
                                if (insDocBin.getPreReceiptItemId().equals(inputWaybillDTO.getId())) {
                                    insDocBin.setMatDocCode(currentReturnObject.getMatDocCode());
                                    insDocBin.setMatDocRid(currentReturnObject.getMatDocRid());
                                    insDocBin.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                        }
                    }
                    // 更新waybill单行项目【物料凭证编号、物料凭证的行序号、物料凭证年度、冲销标识、过帐日期、凭证时间、sap过账标识】
                    bizReceiptInputItemDataWrap.updateBatchDtoById(inputItemDTOList);
                    bizReceiptInputWaybillDataWrap.updateBatchDtoById(itemListNotSync);
                }
                // 更新入库单状态 - 已记账
                this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("入库单{}SAP过账失败", headDTO.getReceiptCode());
                // 更新入库单head、item状态-未同步
                if (!checkCompletedStatus(headDTO)) {
                    this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
                }
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE, UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        }
    }

    public boolean checkCompletedStatus(BizReceiptInputHeadDTO headDTO) {
        return bizReceiptInputHeadDataWrap.getById(headDTO.getId()).getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 更新入库单head
     *
     * @param headDto 入库单head
     */
    private void updateHead(BizReceiptInputHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptInputHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新入库单item
     *
     * @param itemDtoList 入库单item
     */
    public void updateItem(List<com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptInputItemDataWrap.updateBatchDtoById(itemDtoList);
            List<BizReceiptInputWaybillDTO> iputWaybillDTOList=new ArrayList<>();
            for (BizReceiptInputItemDTO itemDTO : itemDtoList) {
                for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                    inputWaybillDTO.setBillStatus(itemDTO.getItemStatus());
                    iputWaybillDTOList.add(inputWaybillDTO);
                }
            }
            bizReceiptInputWaybillDataWrap.updateBatchDtoById(iputWaybillDTOList);
        }
    }

    /**
     * 准备更新入库单状态
     *
     * @param headDTO 入库单head
     * @param itemDTOList 入库单item
     * @param status 要修改的单据状态
     */
    public void updateStatus(BizReceiptInputHeadDTO headDTO, List<BizReceiptInputItemDTO> itemDTOList, Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param inputWaybillDTOList 运单行信息
     * @param user 当前用户
     */
    public void setInPostDate(List<BizReceiptInputWaybillDTO> inputWaybillDTOList, CurrentUser user) {
        if (UtilCollection.isNotEmpty(inputWaybillDTOList)) {
            Date postingDate = inputWaybillDTOList.get(0).getPostingDate();
            Date writeOffPostingDate = inputWaybillDTOList.get(0).getWriteOffPostingDate();
            if (UtilObject.isNull(postingDate)) {
                postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            if (UtilObject.isNull(writeOffPostingDate)) {
                writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            // 判断过账日期是否在帐期内
            postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
            writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
            for (BizReceiptInputWaybillDTO inputWaybillDTO : inputWaybillDTOList) {
                if (EnumRealYn.FALSE.getIntValue().equals(inputWaybillDTO.getIsWriteOff())) {
                    inputWaybillDTO.setDocDate(UtilDate.getNow());
                    inputWaybillDTO.setPostingDate(postingDate);
                } else {
                    inputWaybillDTO.setWriteOffDocDate(UtilDate.getNow());
                    inputWaybillDTO.setWriteOffPostingDate(writeOffPostingDate);
                    inputWaybillDTO.setDeliveryWriteOffDocDate(UtilDate.getNow());
                    inputWaybillDTO.setDeliveryWriteOffPostingDate(writeOffPostingDate);
                }
            }
        }
    }

    /**
     * 过账前设置行项目账期
     *
     * @param inputWaybillDTOList 运单行信息
     * @param user 当前用户
     */
    public void setInPostDateWriteOff(List<BizReceiptInputWaybillDTO> inputWaybillDTOList, CurrentUser user) {
        if (UtilCollection.isNotEmpty(inputWaybillDTOList)) {
            Date postingDate = inputWaybillDTOList.get(0).getPostingDate();
            Date writeOffPostingDate = inputWaybillDTOList.get(0).getWriteOffPostingDate();
            if (UtilObject.isNull(postingDate)) {
                postingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            if (UtilObject.isNull(writeOffPostingDate)) {
                writeOffPostingDate = UtilLocalDateTime.getDate(LocalDateTime.now());
            }
            // 判断过账日期是否在帐期内
            postingDate = bizCommonService.checkAndUpdateInPostDate(postingDate, user.getId());
            writeOffPostingDate = bizCommonService.checkAndUpdateInPostDate(writeOffPostingDate, user.getId());
            for (BizReceiptInputWaybillDTO inputWaybillDTO : inputWaybillDTOList) {
                inputWaybillDTO.setWriteOffDocDate(UtilDate.getNow());
                inputWaybillDTO.setWriteOffPostingDate(writeOffPostingDate);
                inputWaybillDTO.setDeliveryWriteOffDocDate(UtilDate.getNow());
                inputWaybillDTO.setDeliveryWriteOffPostingDate(writeOffPostingDate);
            }
        }
    }

    /**
     * ins入库过账
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"},{@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void postInputToIns(BizContext ctx) {
        // 入参上下文-ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 入参上下文-入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        try {
            // 修改库存
            stockCommonService.modifyStock(insMoveTypeDTO);
            // 更新入库单状态 - 已记账
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
            // 单据日志 - 过账
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), EnumReceiptOperationType.RECEIPT_OPERATION_POSTING, "", ctx.getCurrentUser().getId());
        } catch (Exception e) {
            log.error("入库单{}ins过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 失败时更新入库单及行项目为未同步
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());
            // 过账失败 抛出异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 普通标签生成上架请求
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void generateLoadReq(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList =new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
            for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                inputWaybillDTOList.add(inputWaybillDTO);
            }
        }
        // 生成上架请求前过滤非普通标签
        if (UtilCollection.isNotEmpty(inputWaybillDTOList)) {
            /* ******** 设置作业请求head ******** */
            BizReceiptTaskReqHeadDTO reqHeadDTO = UtilBean.newInstance(po, BizReceiptTaskReqHeadDTO.class);
            reqHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPLETED.getValue());
            reqHeadDTO.setReceiptType(EnumReceiptType.UNITIZED_STOCK_TASK_REQ_SHELF_LOAD.getValue());
            reqHeadDTO.setId(null);
            reqHeadDTO.setCreateTime(UtilDate.getNow());
            reqHeadDTO.setModifyTime(UtilDate.getNow());
            /* ******** 设置作业请求item ******** */
            List<BizReceiptTaskReqItemDTO> repItemListDTO = new ArrayList<>();
            for (BizReceiptInputItemDTO itemDTO : po.getItemList()) {
                for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                    BizReceiptTaskReqItemDTO reqItemDTO = UtilBean.newInstance(inputWaybillDTO, BizReceiptTaskReqItemDTO.class);
                    reqItemDTO.setQty(inputWaybillDTO.getQty());
                    reqItemDTO.setPreReceiptType(po.getReceiptType());
                    reqItemDTO.setPreReceiptCode(po.getReceiptCode());
                    reqItemDTO.setPreReceiptHeadId(po.getId());
                    reqItemDTO.setPreReceiptItemId(reqItemDTO.getId());
                    reqItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPLETED.getValue());
                    reqItemDTO.setId(null);
                    reqItemDTO.setHeadId(null);
                    reqItemDTO.setCreateTime(UtilDate.getNow());
                    reqItemDTO.setModifyTime(UtilDate.getNow());
                    repItemListDTO.add(reqItemDTO);
                }
            }

            reqHeadDTO.setItemList(repItemListDTO);
            log.debug("基于成套设备验收入库{}, receiptId={},组装参数，生成上架单", po.getReceiptCode(), po.getId());
            // 设置上架请求数据到上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_REF_PO, new BizReceiptTaskReqSavePo().setStockTaskReqHeadInfo(reqHeadDTO));
            // 推送MQ异步生成上架请求
//            ProducerMessageContent message = ProducerMessageContent.messageContent(TagConst.GENERATE_LOAD_REQ_TAGS, ctx);
//            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);

           loadTaskService.generateLoadReq(ctx);
        }
    }

    /**
     * 验收入库冲销前校验
     *
     * @in ctx 入参 {@link BizReceiptInputWriteOffPO : "验收入库冲销入参"}
     * @out ctx 出参 {@link BizReceiptInputHeadDTO : "验收入库单"}
     */
    public void checkInspectInputWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) && UtilNumber.isEmpty(po.getHeadId()) && UtilCollection.isEmpty(po.getWaybillIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取入库单运单
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList = UtilCollection.toList(bizReceiptInputWaybillDataWrap.listByIds(po.getWaybillIds()), BizReceiptInputWaybillDTO.class);
        // 数据填充
        dataFillService.fillAttr(inputWaybillDTOList);
        // 获取入库单
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(bizReceiptInputHeadDataWrap.getById(po.getHeadId()), BizReceiptInputHeadDTO.class);
        // 设置 冲销过账时间 冲销原因 冲销数量
        inputWaybillDTOList.forEach(waybillDTO -> waybillDTO.setWriteOffPostingDate(po.getWriteOffPostingDate()).setWriteOffReason(po.getWriteOffReason())
        );
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, inputHeadDTO);
    }

    /**
     * 入库单冲销处理
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "验收入库单}
     */
    public void handleInputReceiptWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 生成ins冲销过账凭证
        this.generateInsDocToPostWriteOff(ctx);
        // sap入库冲销
        this.writeOffInputToSap(ctx);
        // ins入库冲销
        this.writeOffInputToIns(ctx);
        // 冲销同步更新上架请求
        this.writeOffUpdateReq(ctx);
    }

    /**
     * 生成ins冲销过账凭证
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "验收入库冲销入参"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void generateInsDocToPostWriteOff(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 装载凭证
        StockInsMoveTypeDTO insMoveTypeDTO;
        try {
            // 生成凭证
            insMoveTypeDTO = unitizedInspectInputWriteOffMoveTypeComponent.generateInsDocToPost(headDTO);
        } catch (Exception e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION);
        }
        // 过账前的校验和数量计算
        stockCommonService.checkAndComputeForModifyStock(insMoveTypeDTO);
        // 上下文返回参数
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_INS, insMoveTypeDTO);
    }


    /**
     * sap入库冲销
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"},{@link StockInsMoveTypeDTO : "ins凭证"}
     * @out ctx 出参 {@link StockInsMoveTypeDTO : "补全凭证【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间】"}
     */
    public void writeOffInputToSap(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - ins凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        // 当前用户
        CurrentUser user = ctx.getCurrentUser();
        List<BizReceiptInputWaybillDTO> inputWaybillDTOList =new ArrayList<>();
        for (BizReceiptInputItemDTO itemDTO : headDTO.getItemList()) {
            for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                inputWaybillDTOList.add(inputWaybillDTO);
            }
        }
        if (EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(headDTO.getItemList().get(0).getReceiptType())) {
            this.setInPostDateWriteOff(inputWaybillDTOList, user);
        }else{
            /* ******** 设置冲销账期 ******** */
            this.setInPostDate(inputWaybillDTOList, user);
        }

        // 运单行冲销数量等于0 或运单行单价等于0的 更新运单冲销信息【过帐日期、凭证时间、sap冲销标识、冲销原因】
        List<BizReceiptInputWaybillDTO> unToSapItemDTOList = inputWaybillDTOList.stream().filter(p -> p. getQty().compareTo(BigDecimal.ZERO) == 0
                || p.getWaybillDTO().getPrice().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        if(UtilCollection.isNotEmpty(unToSapItemDTOList)) {
            unToSapItemDTOList.forEach(p -> p.setIsWriteOff(EnumRealYn.TRUE.getIntValue()));
            bizReceiptInputWaybillDataWrap.updateBatchDtoById(unToSapItemDTOList);
        }
        // 运单行冲销数量大于0 运单行单价大于0的 过账SAP
        List<BizReceiptInputWaybillDTO> itemListNotSync = inputWaybillDTOList.stream().filter(p -> p. getQty().compareTo(BigDecimal.ZERO) > 0
                && p.getWaybillDTO().getPrice().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        itemListNotSync.forEach(p -> {
            BizReceiptInputItemDTO inputItemDTO = UtilBean.newInstance(bizReceiptInputItemDataWrap.getById(p.getItemId()), BizReceiptInputItemDTO.class);
            dataFillService.fillAttr(inputItemDTO);
            BizReceiptRegisterItem receiptRegisterItem= bizReceiptRegisterItemDataWrap.getById(p.getWaybillDTO().getArrivalRegisterItemId());
            inputItemDTO.setMatDocCode103(receiptRegisterItem.getMatDocCode());
            inputItemDTO.setMatDocRid103(receiptRegisterItem.getMatDocRid());
            inputItemDTO.setMatDocYear103(receiptRegisterItem.getMatDocYear());
            p.setInspectInputItem(inputItemDTO);
            p.setMatDocCode103(receiptRegisterItem.getMatDocCode());
            p.setMatDocRid103(receiptRegisterItem.getMatDocRid());
            p.setMatDocYear103(receiptRegisterItem.getMatDocYear());
        });
        ErpReturnObject returnObj = new ErpReturnObject();
        if (UtilCollection.isNotEmpty(itemListNotSync)) {
            /* ******** 调用sap ******** */
            returnObj = sapInterfaceService.postingNew(JSONArray.toJSONStringWithDateFormat(itemListNotSync, "yyyyMMdd", SerializerFeature.WriteDateUseDateFormat));
            /* ******** 调用sap后处理开始 ******** */
            if (Const.ERP_RETURN_TYPE_S.equalsIgnoreCase(returnObj.getSuccess())) {
                // 更新冲销物料凭证号
                List<ErpReturnObjectItem> returnObjectItems = returnObj.getReturnItemList();
                if (UtilCollection.isNotEmpty(returnObjectItems)) {
                    List<BizReceiptInputItemDTO> inputItemDTOList=headDTO.getItemList();
                    for (BizReceiptInputItemDTO inputItemDTO : inputItemDTOList) {
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptCode().equals(inputItemDTO.getReceiptCode())
                                        && item.getReceiptRid().equals(inputItemDTO.getRid())
                                         )
                                .findFirst().orElse(null);
                        if (EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(inputItemDTO.getReceiptType())) {
                            currentReturnObject = returnObjectItems.stream()
                                    .filter(item -> item.getReceiptCode().equals(inputItemDTO.getPreReceiptCode())
                                            && item.getReceiptRid().equals(inputItemDTO.getPreReceiptRid()) )
                                    .findFirst().orElse(null);
                        }
                        if (null == currentReturnObject) {
                            continue;
                        }
                        // 为了单据可以往复核销，冲销，当冲销时修改过账状态未0
                        inputItemDTO.setIsPost(EnumRealYn.FALSE.getIntValue());
                        inputItemDTO.setDmbtr(currentReturnObject.getDmbtr());

                        if (UtilString.isNullOrEmpty(inputItemDTO.getWriteOffMatDocCode())) {
                            inputItemDTO.setWriteOffMatDocCode(currentReturnObject.getWriteOffMatDocCode());
                            inputItemDTO.setWriteOffMatDocRid(currentReturnObject.getWriteOffMatDocRid());
                            inputItemDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        } else if(UtilString.isNullOrEmpty(inputItemDTO.getDeliveryWriteOffMatDocCode())){
                            inputItemDTO.setDeliveryWriteOffMatDocCode(currentReturnObject.getWriteOffMatDocCode());
                            inputItemDTO.setDeliveryWriteOffMatDocRid(currentReturnObject.getWriteOffMatDocRid());
                            inputItemDTO.setDeliveryWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));

                            // 入库冲销时，106冲销时保存dmbtr本位币金额字段，104业务不保存，避免数据覆盖
                            inputItemDTO.setDmbtr(null);
                        }
                        if(UtilCollection.isNotEmpty(inputItemDTO.getInputWaybillList())) {
                            inputItemDTO.setDocDate(inputItemDTO.getInputWaybillList().get(0).getDocDate());
                            inputItemDTO.setWriteOffDocDate(inputItemDTO.getInputWaybillList().get(0).getWriteOffDocDate());
                            inputItemDTO.setMatDocCode103(inputItemDTO.getInputWaybillList().get(0).getMatDocCode());
                            inputItemDTO.setMatDocRid103(inputItemDTO.getInputWaybillList().get(0).getMatDocRid());
                            inputItemDTO.setMatDocYear103(inputItemDTO.getInputWaybillList().get(0).getMatDocYear());
                        }
                    }
                    for (BizReceiptInputWaybillDTO inputWaybillDTO : itemListNotSync) {
                        ErpReturnObjectItem currentReturnObject = returnObjectItems.stream()
                                .filter(item -> item.getReceiptCode().equals(inputWaybillDTO.getInspectInputItem().getReceiptCode())
                                        && item.getReceiptRid().equals(inputWaybillDTO.getInspectInputItem().getRid())
                                        && item.getReceiptBid().equals(inputWaybillDTO.getBid()))
                                .findFirst().orElse(null);
                        if (EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT_WRITE_OFF.getValue().equals(inputWaybillDTO.getInspectInputItem().getReceiptType())) {
                            currentReturnObject = returnObjectItems.stream()
                                    .filter(item -> item.getReceiptCode().equals(inputWaybillDTO.getInspectInputItem().getPreReceiptCode())
                                            && item.getReceiptRid().equals(inputWaybillDTO.getInspectInputItem().getPreReceiptRid())
                                            && item.getReceiptBid().equals(inputWaybillDTO.getPreReceipBid()))
                                    .findFirst().orElse(null);
                        }
                        if (null == currentReturnObject) {
                            continue;
                        }

                        // 为了单据可以往复核销，冲销，当冲销时修改过账状态未0
                        inputWaybillDTO.setIsPost(EnumRealYn.FALSE.getIntValue());
                        inputWaybillDTO.setDmbtr(currentReturnObject.getDmbtr());

                        if (UtilString.isNullOrEmpty(inputWaybillDTO.getWriteOffMatDocCode())) {
                            inputWaybillDTO.setWriteOffMatDocCode(currentReturnObject.getWriteOffMatDocCode());
                            inputWaybillDTO.setWriteOffMatDocRid(currentReturnObject.getWriteOffMatDocRid());
                            inputWaybillDTO.setWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                        } else if(UtilString.isNullOrEmpty(inputWaybillDTO.getDeliveryWriteOffMatDocCode())){
                            inputWaybillDTO.setDeliveryWriteOffMatDocCode(currentReturnObject.getWriteOffMatDocCode());
                            inputWaybillDTO.setDeliveryWriteOffMatDocRid(currentReturnObject.getWriteOffMatDocRid());
                            inputWaybillDTO.setDeliveryWriteOffMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));

                            // 入库冲销时，106冲销时保存dmbtr本位币金额字段，104业务不保存，避免数据覆盖
                            inputWaybillDTO.setDmbtr(null);
                        }
                        // 冲销成功，补全ins凭证
                        if (UtilObject.isNotNull(insMoveTypeDTO)) {
                            for (StockInsDocBatch dto : insMoveTypeDTO.getInsDocBatchList()) {
                                if (dto.getPreReceiptItemId().equals(inputWaybillDTO.getId())) {
                                    dto.setMatDocCode(currentReturnObject.getMatDocCode());
                                    dto.setMatDocRid(currentReturnObject.getMatDocRid());
                                    dto.setPostingDate(inputWaybillDTO.getWriteOffPostingDate());
                                    dto.setDocDate(inputWaybillDTO.getWriteOffDocDate());
                                    dto.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                            for (StockInsDocBin dto : insMoveTypeDTO.getInsDocBinList()) {
                                if (dto.getPreReceiptItemId().equals(inputWaybillDTO.getId())) {
                                    dto.setMatDocCode(currentReturnObject.getMatDocCode());
                                    dto.setMatDocRid(currentReturnObject.getMatDocRid());
                                    dto.setMatDocYear(UtilObject.getStringOrEmpty(currentReturnObject.getMatDocYear()));
                                }
                            }
                        }
                    }
                    // 更新入库单wayBill行项目【冲销物料凭证编号、冲销物料凭证的行序号、冲销物料凭证年度、冲销标识、过帐日期、凭证时间、冲销原因、本位币金额】
                    bizReceiptInputItemDataWrap.updateBatchDtoById(inputItemDTOList);
                    bizReceiptInputWaybillDataWrap.updateBatchDtoById(itemListNotSync);
                }
                /* ******** 调用sap后处理结束 ******** */
            } else {
                log.error("入库单{}SAP冲销过账失败", headDTO.getReceiptCode());
                // 抛出接口调用失败异常
                throw new WmsException(EnumReturnMsg.RETURN_CODE_INTERFACE_CALL_FAILURE,
                        UtilObject.getStringOrEmpty(returnObj.getReturnMessage()));
            }
        }
    }

    /**
     * ins入库冲销
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库冲销入参"},{@link StockInsMoveTypeDTO : "ins凭证"}
     */
    public void writeOffInputToIns(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 入参上下文 - 凭证
        StockInsMoveTypeDTO insMoveTypeDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_INS);
        try {
            /* ***** 修改库存 ***** */
            stockCommonService.modifyStock(insMoveTypeDTO);
            /* ***** 单据日志 - 冲销 ***** */
            receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                    EnumReceiptOperationType.RECEIPT_OPERATION_WRITEOFF, "", ctx.getCurrentUser().getId());
        } catch (Exception e) {
            log.error("入库单{}ins冲销过账失败，失败原因：{}", headDTO.getReceiptCode(), e.getMessage());
            // 过账失败 抛出异常
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
        }
    }

    /**
     * 获取运单状态集合
     *
     * @param headId 单据id
     * @return 状态列表
     */
    public List<Integer> getWaybillStatusList(Long headId) {
        // 获取入库单运单
        List<BizReceiptInputWaybill> inputWaybillList = bizReceiptInputWaybillDataWrap.list(new QueryWrapper<BizReceiptInputWaybill>().lambda().eq(BizReceiptInputWaybill::getHeadId, headId));
        // 行项目状态集合
        return inputWaybillList.stream().map(BizReceiptInputWaybill::getBillStatus).collect(Collectors.toList());
    }

    /**
     * 冲销同步更新上架请求
     *
     * @in ctx 入参 {@link BizReceiptInputWriteOffPO : "入库冲销入参对象"}
     */
    public void writeOffUpdateReq(BizContext ctx) {
        // 入参上下文
        BizReceiptInputWriteOffPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_IDS, po.getItemIds());
        // 推送异步MQ
        RocketMQProducerProcessor.getInstance().AsyncMQSend(ProducerMessageContent.messageContent(TagConst.RECEIPT_WRITE_OFF_MODIFY_REQ_ITEM, ctx));
    }

    /**
     * 采购验收-生成验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO :"验收入库单"}
     */
    public void genInspectInput(BizContext ctx) {
        // MQ入参上下文 - 验收入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
        // 生成验收入库单
        this.saveInput(ctx);
        // 保存操作日志
        this.saveBizReceiptOperationLog(ctx);
        // 保存批次图片
        this.saveBizBatchImg(ctx);
        // 保存批次附件
        this.saveBatchAttachment(ctx);
    }
    /**
     * 保存批次图片
     *
     * @in ctx 入参 {@link BizReceiptInputItemDTO : "入库单批次图片"}
     */
    public void saveBizBatchImg(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
          if(  UtilCollection.isNotEmpty(headDTO.getWaybillDTOList())) {
              for (BizReceiptWaybillDTO itemDto : headDTO.getWaybillDTOList()) {
                  if (UtilCollection.isNotEmpty(itemDto.getBizBatchImgDTOList())) {
                      itemDto.getBizBatchImgDTOList().forEach(imgDTO -> {
                          imgDTO.setId(null);
                          imgDTO.setMatId(itemDto.getMatId());
                          imgDTO.setFtyId(itemDto.getFtyId());
                          imgDTO.setBatchId(itemDto.getBizBatchInfoDTO().getId());
                          imgDTO.setImgBizType(EnumImageBizType.BATCH.getValue());
                          imgDTO.setReceiptType(0);
                          imgDTO.setReceiptHeadId(0L);
                          imgDTO.setReceiptItemId(0L);
                          imgDTO.setCreateUserId(ctx.getCurrentUser().getId());
                      });
                  } else {
                      itemDto.setBizBatchImgDTOList(new ArrayList<>());
                  }
              }
              // 批量保存入库单批次图片
              bizBatchImgService.multiSaveBizBatchImg(headDTO.getWaybillDTOList().stream()
                      .flatMap(item -> item.getBizBatchImgDTOList().stream()).collect(Collectors.toList()));
              log.debug("批量保存验收单批次图片成功!");

          }
    }

    /**
     * 保存批次附件
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存附件的入库单"}
     */
    public void saveBatchAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        if(UtilCollection.isNotEmpty(headDTO.getWaybillDTOList())) {
            for (BizReceiptWaybillDTO itemDto : headDTO.getWaybillDTOList()) {
                // 前序单据是质检会签单据时，保存行项目附件作为每行物料的批次附件
                receiptAttachmentService.saveBatchInfoAttachment(itemDto.getFileList(), itemDto.getMatId(), itemDto.getBizBatchInfoDTO().getId(), user.getId());
            }
        }
    }

    /**
     * 删除校验
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "验收入库单删除入参"}
     * @out ctx 出参 {@link BizReceiptInputDeletePO : "如果是全部删除，设置行项目id集合"}
     */
    public void checkDeleteInspectInput(BizContext ctx) {
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNull(po) || UtilNumber.isEmpty(po.getHeadId())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取入库单信息
        BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(po.getHeadId());
        // 转DTO
        BizReceiptInputHeadDTO inputHeadDTO = UtilBean.newInstance(inputHead, BizReceiptInputHeadDTO.class);
        // 填充父子属性
        dataFillService.fillSonAttrForDataObj(inputHeadDTO);
        /* ******** 校验验收入库单head ******** */
        if (UtilObject.isNotNull(inputHead)) {
            if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(inputHead.getReceiptStatus())
                && !EnumReceiptStatus.RECEIPT_STATUS_SUBMITTED.getValue().equals(inputHead.getReceiptStatus())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_NO_AUTHORITY_DELETE);
            }
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        /* ******** 校验是否全部删除 ******** */
        if (po.isDeleteAll()) {
            po.setItemIds(
                inputHeadDTO.getItemList().stream().map(com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO::getId).collect(Collectors.toList()));
        } else if (UtilCollection.isEmpty(po.getItemIds())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }

    /**
     * 删除验收入库单
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "验收单行删除入参"}
     */
    public void deleteInspectInput(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        /* ******** 删除验收入库单 ******** */
        if (po.isDeleteAll()) {
            // 删除验收入库单head
            bizReceiptInputHeadDataWrap.removeById(po.getHeadId());
            // 删除验收入库单item
            UpdateWrapper<BizReceiptInputItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptInputItem::getHeadId, po.getHeadId());
            bizReceiptInputItemDataWrap.remove(wrapper);
            // 保存操作日志 - 删除
            receiptOperationLogService.saveBizReceiptOperationLogList(po.getHeadId(),
                EnumReceiptType.STOCK_INPUT_INSPECT.getValue(), EnumReceiptOperationType.RECEIPT_OPERATION_DELETE, "",
                ctx.getCurrentUser().getId());
        } else {
            // 删除验收入库单item
            bizReceiptInputItemDataWrap.removeByIds(po.getItemIds());
        }
    }

    /**
     * 验收入库单上架回调校验
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO:"入库单上架回调入参"}
     */
    public void checkInspectInputByCallback(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 校验入参
        if (null == vo) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 校验行项目
        if (UtilCollection.isEmpty(vo.getInputItemCallbackVoList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 校验单据类型
        if (!EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue().equals(vo.getReceiptType())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_TYPE_FALSE);
        }
    }

    /**
     * 冲销行项目校验
     *
     * @param inputItemDTO BizInspectInputItemDTO
     * @return true/false
     */
    public boolean itemCanWriteOff(com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO inputItemDTO) {
        Integer receiptType = inputItemDTO.getReceiptType();
        Integer receiptStatus = inputItemDTO.getReceiptStatus();
        return (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue().equals(receiptStatus)
            || EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue().equals(receiptStatus))
            && EnumReceiptType.STOCK_INPUT_INSPECT.getValue().equals(receiptType);
    }

    /**
     * 设置列表、分页查询条件
     *
     * @param po 查询条件对象
     * @return WmsQueryWrapper<BizReceiptInputSearchPO>
     */
    private WmsQueryWrapper<BizReceiptInputSearchPO> setQueryWrapper(BizReceiptInputSearchPO po, CurrentUser user) {
        if (null == po) {
            po = new BizReceiptInputSearchPO();
        }
        if(StringUtils.isNotEmpty(po.getChildMatCode())){
            Long matId = dictionaryService.getMatIdByMatCode(po.getChildMatCode());
            po.setChildMatId(matId);
        }
        List<Long> locationIdList =null;
        if(user!=null &&user.getLocationList()!=null){
            List<DicStockLocationDTO> locationList =user.getLocationList();
            locationIdList =locationList.stream().map(DicStockLocationDTO::getId).collect(Collectors.toList());
        }

        String deliveryNoticeDesc = po.getDeliveryNoticeDescribe();
        // 查询条件设置
        WmsQueryWrapper<BizReceiptInputSearchPO> wrapper = new WmsQueryWrapper<>();
        BizReceiptInputSearchPO finalPo = po;
        // 入库单据号
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
            BizReceiptInputHead.class, po.getReceiptCode());
        // 单据类型
        wrapper.lambda().eq(Boolean.TRUE, BizReceiptInputSearchPO::getReceiptType, BizReceiptInputHead.class,
            EnumReceiptType.UNITIZED_STOCK_INPUT_INSPECT.getValue());
        // 单据状态
        wrapper.lambda().in(UtilCollection.isNotEmpty(po.getReceiptStatusList()),
            BizReceiptInputSearchPO::getReceiptStatus, BizReceiptInputHead.class, po.getReceiptStatusList());
        wrapper.lambda().in(UtilCollection.isNotEmpty(locationIdList),  BizReceiptInputSearchPO::getLocationId,
                BizReceiptInputItem.class,locationIdList);
        // 物料凭证号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatDocCode()), BizReceiptInputSearchPO::getMatDocCode,
            BizReceiptInputItem.class, po.getMatDocCode());
        // 质检会签单号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getInspectReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
                BizReceiptInspectHead.class, po.getInspectReceiptCode());
        // 不符合项处置单号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getInconformityReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
                BizReceiptInconformityHead.class, po.getInconformityReceiptCode());
        // 采购订单号
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getPurchaseReceiptCode()), BizReceiptInputSearchPO::getReceiptCode,
                ErpPurchaseReceiptHead.class, po.getPurchaseReceiptCode());
        // 物料编码
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getMatCode()), BizReceiptInputSearchPO::getMatCode,
                DicMaterial.class, po.getMatCode());
        wrapper.lambda().eq(UtilNumber.isNotEmpty(po.getChildMatId()), BizReceiptInputSearchPO::getMatId,
                BizReceiptWaybill.class, po.getChildMatId());
        // 创建人
        wrapper.lambda().eq(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptInputSearchPO::getUserName,
                SysUser.class, po.getCreateUserName());
        wrapper.lambda().like(UtilString.isNotNullOrEmpty(deliveryNoticeDesc), BizReceiptInputSearchPO::getDeliveryNoticeDescribe,
                BizReceiptInspectHead.class, deliveryNoticeDesc);
        // 创建时间
        wrapper.lambda().between((UtilObject.isNotNull(po.getStartTime()) && UtilObject.isNotNull(po.getEndTime())), BizReceiptInputSearchPO::getCreateTime,
                BizReceiptInputHead.class, po.getStartTime(), po.getEndTime());
        return wrapper.setEntity(po);
    }

    /**
     * 打印物料标签校验
     * @param ctx
     */
    public void checkPrint(BizContext ctx) {
        // 从上下文获取单据head id
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        Long headId = po.getBizLabelPrintDTO().getHeadId();
        if (UtilString.isNullOrEmpty(po.getBizLabelPrintDTO().getPrinterIp())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_IP_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrinterPort())){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_PORT_LOST_EXCEPTION);
        }
        if (UtilNumber.isEmpty(po.getBizLabelPrintDTO().getPrintNum()) || po.getBizLabelPrintDTO().getPrintNum() == 0){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PRINT_NUM_LOST_EXCEPTION);
        }
        // head id 为空
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        BizReceiptInputHeadDTO headDTO = this.getItemListByHeadId(headId);
        // headDTO填充入参
        po.setHeadDTO(headDTO);
    }


    /**
     * 填充打印数据
     * @param ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void fillPrintData(BizContext ctx) {
        // 从上下文中取出打印入参
        BizLabelPrintPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 装载要保存的标签数据集合
        List<BizLabelDataDTO> labelDataList = new ArrayList<>();
        // 装载打印机打印数据
        List<LabelReceiptInputBox> receiptInputBoxes = new ArrayList<>();
        // 装载要更新的批次信息
        List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
        // 从入参中获取打印信息
        BizLabelPrintDTO printInfo = po.getBizLabelPrintDTO();
        BizReceiptInputHeadDTO headDTO = (BizReceiptInputHeadDTO) po.getHeadDTO();
        // 新建打印实体对象
        List<com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO> itemDTOList = headDTO.getItemList();

        itemDTOList.forEach(itemDTO->{
            // 生成标签编码
            String labelCode =
                    bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_LABEL_CODE.getValue());

            // 标签打印数据
           this.setPrintLabelData(receiptInputBoxes, itemDTO, labelCode);

            // 设置要更新的批次信息数据
            itemDTO.getBizBatchInfoDTO().setInspectHeadId(itemDTO.getHeadId());
            itemDTO.getBizBatchInfoDTO().setInspectItemId(itemDTO.getId());
            itemDTO.getBizBatchInfoDTO()
                    .setInspectDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
            itemDTO.getBizBatchInfoDTO().setInspectCode(itemDTO.getReceiptCode());
            itemDTO.getBizBatchInfoDTO().setInspectUserId(user.getId());
            itemDTO.getBizBatchInfoDTO().setTagType(2);
            bizBatchInfoDTOList.add(itemDTO.getBizBatchInfoDTO());

            // 设置需要保存的标签数据
            BizLabelDataDTO label = BizLabelDataDTO.builder()
                    .id(null)
                    .matId(itemDTO.getMatId())
                    .ftyId(itemDTO.getFtyId())
                    .locationId(itemDTO.getLocationId())
                    .batchId(itemDTO.getBizBatchInfoDTO().getId())
                    .binId(itemDTO.getBinId())
                    .whId(itemDTO.getWhId())
                    .typeId(itemDTO.getTypeId())
                    .labelCode(labelCode)
                    .snCode(labelCode)
                    .qty(itemDTO.getQty())
                    .labelType(itemDTO.getBizBatchInfoDTO().getTagType())
                    .receiptHeadId(itemDTO.getHeadId())
                    .receiptItemId(itemDTO.getId())
                    .receiptType(itemDTO.getReceiptType())
                    .preReceiptHeadId(itemDTO.getPreReceiptHeadId())
                    .preReceiptItemId(itemDTO.getPreReceiptItemId())
                    .preReceiptType(itemDTO.getPreReceiptType())
                    .build();
            labelDataList.add(label);

        });

        /* *** 更新批次信息【验收单head表id、验收单item表id、验收日期、验收单号、验收人id】 *** */
        if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
            bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            log.info("验收入库-PDA端-打印-更新批次信息成功 " + JSONObject.toJSONString(bizBatchInfoDTOList));
        }
        /* *** 插入标签数据及关联属性 *** */
        if (UtilCollection.isNotEmpty(labelDataList)) {
            labelDataService.saveBatchDto(labelDataList);
            log.info("验收入库-PDA端-打印-插入标签数据成功 " + JSONObject.toJSONString(labelDataList));
            List<BizLabelReceiptRel> bizLabelReceiptRelList = new ArrayList<>();
            for (BizLabelDataDTO label : labelDataList) {
                BizLabelReceiptRel bizLabelReceiptRel = new BizLabelReceiptRel();
                bizLabelReceiptRel = UtilBean.newInstance(label, bizLabelReceiptRel.getClass());
                bizLabelReceiptRel.setId(null);
                bizLabelReceiptRel.setLabelId(label.getId());
                bizLabelReceiptRel.setReceiptType(label.getReceiptType());
                bizLabelReceiptRel.setReceiptHeadId(label.getReceiptHeadId());
                bizLabelReceiptRel.setReceiptItemId(label.getReceiptItemId());
                bizLabelReceiptRel.setPreReceiptType(label.getPreReceiptType());
                bizLabelReceiptRel.setPreReceiptHeadId(label.getPreReceiptHeadId());
                bizLabelReceiptRel.setPreReceiptItemId(label.getPreReceiptItemId());
                bizLabelReceiptRel.setStatus(EnumReceiptStatus.RECEIPT_STATUS_POSTED.getValue());
                bizLabelReceiptRelList.add(bizLabelReceiptRel);
            }
            labelReceiptRelService.saveBatch(bizLabelReceiptRelList);
            log.info("验收入库-PDA端-打印-插入标签单据关联数据成功 " + JSONObject.toJSONString(bizLabelReceiptRelList));
        }

        // 填充打印信息
        printInfo.setLabelBoxList(receiptInputBoxes);

        if (UtilCollection.isNotEmpty(receiptInputBoxes)) {
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,receiptInputBoxes);
            // 发送MQ打印请求
            ProducerMessageContent message =
                    ProducerMessageContent.messageContent(TagConst.PRINT_INSPECT_INPUT_BOX_LABEL, printInfo);
            RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
        }
    }

    /**
     * 设置标签打印数据
     * @param receiptInputBoxes 装载打印机打印数据
     * @param itemDTO 要打印的验收单行项目
     * @param labelCode rfid 编码
     * @return
     */
    private void setPrintLabelData(List<LabelReceiptInputBox> receiptInputBoxes, com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO itemDTO, String labelCode) {

        // 单品打印将行项目拆分
        if (itemDTO.getIsSingle().equals("单品")){
            int qty = itemDTO.getQty().intValue();
            for (int i = 0; i < qty; i++) {
                printCount(receiptInputBoxes, itemDTO, labelCode);
            }
        }else { // 批次打印不需要拆分
            printCount(receiptInputBoxes, itemDTO, labelCode);
        }

    }

    /**
     * 标签打印的数据
     * @param receiptInputBoxes
     * @param itemDTO
     * @param labelCode
     */
    private void printCount(List<LabelReceiptInputBox> receiptInputBoxes, com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO itemDTO, String labelCode) {
        // 设置需要打印的数据
        LabelReceiptInputBox labelReceiptInputBox = UtilBean.newInstance(itemDTO, LabelReceiptInputBox.class);
        labelReceiptInputBox.setTagType(itemDTO.getTagType());
        labelReceiptInputBox.setIsSingle(itemDTO.getIsSingle());
        labelReceiptInputBox.setBatchCode(itemDTO.getBizBatchInfoDTO().getBatchCode());
        labelReceiptInputBox.setRfidCode(labelCode);
        labelReceiptInputBox.setLifetimeDate(itemDTO.getBizBatchInfoDTO().getLifetimeDate());
        // 0：不需要包装；1：备件防潮、防撞、防尘包装；2：备件避光包装；3：备件防锈包装；4：备件防静电包装；5：备件真空包装；6：备件保存原包装；7：双面保护；8：端口封堵
        switch (itemDTO.getPackageType()) {
            case 0:
                labelReceiptInputBox.setPackageTypeI18n("不需要包装");
                break;
            case 1:
                labelReceiptInputBox.setPackageTypeI18n("防潮、防撞、防尘包装");
                break;
            case 2:
                labelReceiptInputBox.setPackageTypeI18n("避光包装");
                break;
            case 3:
                labelReceiptInputBox.setPackageTypeI18n("防锈包装");
                break;
            case 4:
                labelReceiptInputBox.setPackageTypeI18n("防静电包装");
                break;
            case 5:
                labelReceiptInputBox.setPackageTypeI18n("真空包装");
                break;
            case 6:
                labelReceiptInputBox.setPackageTypeI18n("原包装");
                break;
            case 7:
                labelReceiptInputBox.setPackageTypeI18n("双面保护");
                break;
            case 8:
                labelReceiptInputBox.setPackageTypeI18n("端口封堵");
                break;
            default:
                labelReceiptInputBox.setPackageTypeI18n(" ");
        }
        // 批次 + 普通标签不需要生成rfid,其他生成rfid
//        if (!(EnumRealYn.FALSE.getIntValue().equals(itemDTO.getIsSingle()) && EnumTagType.GENERAL.getValue().equals(itemDTO.getTagType()))) {
//            labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
//        } else {
//            labelReceiptInputBox.setRfidCode(Const.BATCH_GENERAL_LABEL_TAB);
//        }
        labelReceiptInputBox.setLabelIsRFID(EnumRealYn.TRUE.getIntValue());
        receiptInputBoxes.add(labelReceiptInputBox);
    }

    /**
     * 通过head id获取item
     * @param headId head id
     * @return
     */
    private BizReceiptInputHeadDTO getItemListByHeadId(Long headId) {
        BizReceiptInputHead bizReceiptInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 处理单据在其他客户端被删除了的情况
        if (bizReceiptInputHead == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizReceiptInputHead, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        return headDTO;
    }

    /**
     * 更新入库单【实际入库数量、单据状态】
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO : "入库上架回调VO"}
     */
    public void updateInputByCallback(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 装载已作业入库单
        List<BizReceiptInputWaybillDTO> taskStockInputList = Lists.newArrayList();
        vo.getInputItemCallbackVoList().forEach(taskItem -> {
            BizReceiptInputWaybillDTO input = new BizReceiptInputWaybillDTO();
            input.setId(taskItem.getTaskItemId());
            input.setTaskQty(taskItem.getTaskQty());
            input.setQty(taskItem.getQty());
            taskStockInputList.add(input);
        });
        // 根据作业情况修改实际入库数量
        if (UtilCollection.isNotEmpty(taskStockInputList)) {
            List<Long> waybillIds = taskStockInputList.stream().map(BizReceiptInputWaybillDTO::getId).collect(Collectors.toList());
            if (UtilCollection.isNotEmpty(waybillIds)) {
                // 获取验收入库单
                List<BizReceiptInputWaybill> inputWaybillList = bizReceiptInputWaybillDataWrap.listByIds(waybillIds);
                // 转DTO
                List<BizReceiptInputWaybillDTO> inputWaybillDTOList = UtilCollection.toList(inputWaybillList, BizReceiptInputWaybillDTO.class);
                for (BizReceiptInputWaybillDTO taskInputItem : taskStockInputList) {
                    BizReceiptInputWaybillDTO currentDbStockInputDTO = inputWaybillDTOList.stream().filter(item -> taskInputItem.getId().equals(item.getId())).findFirst().orElse(null);
                    if (UtilObject.isNull(currentDbStockInputDTO)) {
                        return;
                    }
                    // 设置当前行项目状态 【全部上架-已完成/部分上架-入库中】
                    if (taskInputItem.getQty().compareTo(taskInputItem.getTaskQty().add(currentDbStockInputDTO.getTaskQty())) == 0) {
                        taskInputItem.setBillStatus(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
                        taskInputItem.setTaskQty(currentDbStockInputDTO.getQty());
                    } else {
                        taskInputItem.setBillStatus(EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
                        taskInputItem.setTaskQty(taskInputItem.getTaskQty().add(currentDbStockInputDTO.getTaskQty()));
                    }
                }
                /* ***** 更新验收运单【实际入库数量、单据状态】***** */
                bizReceiptInputWaybillDataWrap.updateBatchDtoById(taskStockInputList);
                /* ***** 更新验收入库单head【单据状态】***** */
                List<Integer> waybillStatusList = this.getWaybillStatusList(vo.getTaskHeadId());
                Set<Integer> waybillStatusSet = new HashSet<>(waybillStatusList);
                // 判断所有行项目是否都是【已作业】，是更新单据为【已作业】，否则更新单据为【作业中】
                if (waybillStatusSet.size() == 1 && waybillStatusSet.contains(EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
                    this.updateStatus(new BizReceiptInputHeadDTO().setId(vo.getTaskHeadId()), null, EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue());
                } else {
                    this.updateStatus(new BizReceiptInputHeadDTO().setId(vo.getTaskHeadId()), null, EnumReceiptStatus.RECEIPT_STATUS_IN_TASK.getValue());
                }
            }
        }
    }

    /**
     * 校验行项目是否全部完作业
     *
     * @in ctx 入参 {@link BizReceiptInputHeadCallbackVO : "验收入库上架回调VO"}
     */
    public boolean checkAllItemStatusTask(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadCallbackVO vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_REF_PO);
        // 校验行项目是否全已作业
        if (this.checkAllWaybillStatusSame(vo.getTaskHeadId(), EnumReceiptStatus.RECEIPT_STATUS_TASK.getValue())) {
            BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(vo.getTaskHeadId());
            // 转DTO
            BizReceiptInputHeadDTO headDTO = UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
            // 填充关联属性和父子属性
            dataFillService.fillAttr(headDTO);
            // 设置入库单信息到上下文
            ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
            return true;
        }
        return false;
    }

    /**
     * 校验行行目状态是否全部相同
     *
     * @param headId 入库单抬头主键
     * @param waybillStatus 运单状态
     * @return true/false
     */
    public boolean checkAllWaybillStatusSame(Long headId, Integer waybillStatus) {
        UpdateWrapper<BizReceiptInputWaybill> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headId), BizReceiptInputWaybill::getHeadId, headId);
        // 获取全部上架作业的入库单
        List<BizReceiptInputWaybill> inputWaybill = bizReceiptInputWaybillDataWrap.list(wrapper);
        // 转DTO
        List<BizReceiptInputWaybillDTO> allStockInputDTOList = UtilCollection.toList(inputWaybill, BizReceiptInputWaybillDTO.class);
        if (UtilCollection.isEmpty(allStockInputDTOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
        // 过滤行项目
        List<BizReceiptInputWaybillDTO> stayInputList = allStockInputDTOList.stream().filter(e -> waybillStatus.equals(e.getBillStatus())).collect(Collectors.toList());
        return stayInputList.size() == allStockInputDTOList.size();
    }

    /**
     * 更新批次入库时间
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void updateInputDate(BizContext ctx) {
        // 入参上下文 - 入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(headDTO) ) {
            /* ******** 更新批次信息的入库时间 ******** */
            List<BizBatchInfoDTO> bizBatchInfoDTOList = new ArrayList<>();
            for (BizReceiptInputItemDTO itemDTO : headDTO.getItemList()) {
                for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                    BizBatchInfoDTO bizBatchInfo = inputWaybillDTO.getBizBatchInfoDTO();
                    bizBatchInfo.setId(inputWaybillDTO.getBatchId());
                    bizBatchInfo.setInputDate(UtilLocalDateTime.getDate(LocalDateTime.now()));
                    Date maintenanceDate=headDTO.getItemList().get(0).getDocDate();
                    if(maintenanceDate==null){
                        maintenanceDate=UtilLocalDateTime.getDate(LocalDateTime.now());
                    }
                    bizBatchInfo.setMaintenanceDate(maintenanceDate);
                    bizBatchInfoDTOList.add(bizBatchInfo);
                }
            }

            if (UtilCollection.isNotEmpty(bizBatchInfoDTOList)) {
                bizBatchInfoService.multiUpdateBatchInfo(bizBatchInfoDTOList);
            }
        }
    }

    /**
     * 更新入库单 - 已完成
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "入库单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 更新入库单 - 已完成
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptInputHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保持附件的入库单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存入库单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                headDTO.getReceiptType(), user.getId());
        log.debug("保存入库单附件成功!");
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptInputHeadDTO : "要保存操作日志的入库单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 - 要保存操作日志的入库单
        BizReceiptInputHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", user.getId());
    }

    /**
     * 保存单据流
     *
     * @param headDTO 要保持单据流的入库单
     */
    public void saveReceiptTree(BizReceiptInputHeadDTO headDTO) {
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO item : headDTO.getItemList()) {
            if (UtilNumber.isNotEmpty(item.getPreReceiptType()) && UtilNumber.isNotEmpty(item.getPreReceiptHeadId())
                    && UtilNumber.isNotEmpty(item.getPreReceiptItemId())) {
                BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(headDTO.getReceiptType())
                        .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                        .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                        .setPreReceiptItemId(item.getPreReceiptItemId());
                dtoList.add(dto);
            }
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 删除入库单单据流
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "入库单删除入参"}
     */
    public void deleteReceiptTree(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据流
            receiptRelationService.deleteReceiptTree(po.getReceiptType(), po.getHeadId());
        }
    }

    /**
     * 删除入库单单据流
     *
     * @in ctx 入参 {@link BizReceiptInputDeletePO : "入库单删除入参"}
     */
    public void deleteReceiptAttachment(BizContext ctx) {
        // 入参上下文
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po.isDeleteAll()) {
            // 删除单据附件
            receiptAttachmentService.deleteBizReceiptAttachment(po.getHeadId(), po.getReceiptType());
        }
    }

    /**
     * 删除作业请求
     *
     * @param ctx 上下文
     */
    public void cancelTaskRequest(BizContext ctx) {
        BizReceiptInputDeletePO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_IDS, po.getItemIds());
        RocketMQProducerProcessor.getInstance()
                .AsyncMQSend(ProducerMessageContent.messageContent(TagConst.DEL_RECEIPT_REQ_ITEM, ctx));
    }

    /**
     * 验收入库单单据打印
     *
     * @in ctx 入参 {"id":"主表id"}
     * @out ctx 出参 {@link BizResultVO ("head":"验收入库单详情","button":"按钮组")}
     */
    public void getPrintInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取验收入库单
        BizReceiptInputHead bizStockInputHead = bizReceiptInputHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptInputHeadDTO bizInspectInputHeadDTO = UtilBean.newInstance(bizStockInputHead, BizReceiptInputHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(bizInspectInputHeadDTO);
        // 填充属性
        List<BizReceiptInputItemDTO> itemDTOList = bizInspectInputHeadDTO.getItemList();
        if (!CollectionUtils.isEmpty(itemDTOList)) {
            BizReceiptInputItemDTO firstItemDTO = itemDTOList.get(0);
            bizInspectInputHeadDTO.setReferReceiptCode(firstItemDTO.getReferReceiptCode());
            bizInspectInputHeadDTO.setSupplierName(firstItemDTO.getSupplierName());
            bizInspectInputHeadDTO.setFtyName(firstItemDTO.getFtyName());
            Map<Long, String> subjectMap = new HashMap<>(itemDTOList.size());
            for (BizReceiptInputItemDTO itemDTO : itemDTOList) {
                subjectMap.put(itemDTO.getMatId(), itemDTO.getSubjectType());
            }
            String signInspectReceiptCode = null;
            String erpCreateUserName = firstItemDTO.getErpCreateUserName();
            String erpCreateUserCode = firstItemDTO.getErpCreateUserCode();
            if(StringUtils.isNotEmpty(erpCreateUserCode)){
                QueryWrapper<SysUser> userQueryWrapper = new QueryWrapper<>();
                userQueryWrapper.lambda().eq(SysUser::getUserCode, erpCreateUserCode);
                SysUser sysUser = sysUserDataWrap.getOne(userQueryWrapper);
                if(sysUser!=null){
                    erpCreateUserName=sysUser.getUserName();
                }
            }
            String inspectUserName = null;
            String inspectSumbitterName = null;
            BizReceiptInspectHead inspectHead = null;
            Integer preReceiptType = firstItemDTO.getPreReceiptType();
            if (preReceiptType.equals(EnumReceiptType.UNITIZED_SIGN_INSPECTION.getValue())) {
                signInspectReceiptCode = firstItemDTO.getPreReceiptCode();
                inspectHead = bizReceiptInspectHeadDataWrap.findByCode(signInspectReceiptCode);
            } else if(preReceiptType.equals(EnumReceiptType.UNITIZED_INCONFORMITY_NOTICE.getValue())) {
                BizReceiptInconformityItem bizReceiptInconformityItem = bizReceiptInconformityItemDataWrap.getById(firstItemDTO.getPreReceiptItemId());
                if (bizReceiptInconformityItem != null) {
                    inspectHead = bizReceiptInspectHeadDataWrap.getById(bizReceiptInconformityItem.getPreReceiptHeadId());
                }
            }
            if (inspectHead != null) {
                signInspectReceiptCode = inspectHead.getReceiptCode();
                inspectUserName = inspectHead.getInspectUserName();
                Long userId = inspectHead.getCreateUserId();
                List<Long> userIdList = new ArrayList<>(1);
                userIdList.add(userId);
                Collection<SysUser> sysUserCollection = dictionaryService.getSysUserCacheByIds(userIdList);
                if (!CollectionUtils.isEmpty(sysUserCollection)) {
                    List<SysUser> sysUserList = sysUserCollection.stream().collect(Collectors.toList());
                    inspectSumbitterName = sysUserList.get(0).getUserName();
                }
            }
            BigDecimal sumPrice = BigDecimal.ZERO;
            List<BizReceiptInputWaybillDTO> iputWaybillDTOList=new ArrayList<>();
            for (BizReceiptInputItemDTO itemDTO : bizInspectInputHeadDTO.getItemList()) {
                if (UtilCollection.isNotEmpty(itemDTO.getInputWaybillList())) {
                    for (BizReceiptInputWaybillDTO inputWaybillDTO : itemDTO.getInputWaybillList()) {
                        iputWaybillDTOList.add(inputWaybillDTO);
                    }
                }
            }
            boolean docFlag = true;
            for (BizReceiptInputWaybillDTO iputWaybillDTO : iputWaybillDTOList) {
                BizBatchInfoDTO batchInfoDTO = iputWaybillDTO.getBizBatchInfoDTO();
                if (batchInfoDTO != null) {
                    iputWaybillDTO.setBatchCode(batchInfoDTO.getBatchCode());
                }
                iputWaybillDTO.setSignInspectReceiptCode(signInspectReceiptCode);
                BigDecimal totalPrice = (iputWaybillDTO.getQty().multiply(iputWaybillDTO.getWaybillDTO().getPrice()));
                iputWaybillDTO.setTotalPrice(totalPrice);
                sumPrice = sumPrice.add(totalPrice);
                if (docFlag) {
                    bizInspectInputHeadDTO.setMatDocCode(iputWaybillDTO.getMatDocCode());
                    bizInspectInputHeadDTO.setDocDate(iputWaybillDTO.getDocDate());
                    docFlag = false;
                }
                Long parentMatId = iputWaybillDTO.getWaybillDTO().getParentMatId();
                String subjectType = subjectMap.get(parentMatId);
                if (subjectType != null) {
                    iputWaybillDTO.setSubjectType(subjectType);
                }
            }
            bizInspectInputHeadDTO.setSumPrice(sumPrice);
            String sumPriceStr = sumPrice.stripTrailingZeros().toPlainString();
            String sumPriceLocal = UtilNumber.bigDecimalToLocalStr(new BigDecimal(sumPriceStr));
            bizInspectInputHeadDTO.setErpCreateUserName(erpCreateUserName);
            bizInspectInputHeadDTO.setSumPriceLocal(sumPriceLocal);
            bizInspectInputHeadDTO.setSumPriceStr(sumPriceStr + "元");
            bizInspectInputHeadDTO.setInspectUserName(inspectUserName);
            bizInspectInputHeadDTO.setInspectSumbitterName(inspectSumbitterName);
        }
        // 设置验收入库单详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizInspectInputHeadDTO, new ExtendVO(), new ButtonVO()));
    }

    /**
     * 查询是否生成上架请求单
     * @param headDTO
     * @return
     */
    public boolean checkIsGenerateLoadReq(BizReceiptInputHeadDTO headDTO) {
        boolean flag=false ;
        QueryWrapper<BizReceiptTaskReqItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizReceiptTaskReqItem::getPreReceiptHeadId,headDTO.getId());
        List<BizReceiptTaskReqItem> itemList = bizReceiptTaskReqItemDataWrap.list(queryWrapper);
        if(UtilCollection.isEmpty(itemList)) {
            flag=true;
        }
        return flag;
    }
}
