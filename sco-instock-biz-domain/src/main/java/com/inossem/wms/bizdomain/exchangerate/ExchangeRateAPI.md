# 汇率主数据管理接口文档

---
## 1. 汇率主数据分页查询

### 基本信息

**Path:** /masterdata/exchange-rate/results

**Method:** POST

**Description:** 根据查询条件分页获取汇率主数据列表

### 请求参数

#### Body

```json
{
  "pageIndex": 1,
  "pageSize": 10,
  "paging": true,
  "year": 2024,
  "month": 10
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|pageIndex|number|是|页码|
|pageSize|number|是|每页大小|
|paging|boolean|是|是否分页|
|year|number|否|年份|
|month|number|否|月份|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "resultList": [{
      "id": 159843409264782,
      "year": 2024,
      "month": 10,
      "usdRate": 7.2500,
      "cnyRate": 1.0000,
      "pkrRate": 0.0260,
      "createTime": "2024-10-24 10:00:00",
      "modifyTime": "2024-10-24 15:30:00",
      "createUserId": 1,
      "modifyUserId": 1,
      "createUserName": "管理员"
    }],
    "totalCount": 100
  }
}
```

---
## 2. 汇率主数据详情

### 基本信息

**Path:** /masterdata/exchange-rate/{id}

**Method:** GET

**Description:** 获取汇率主数据详细信息

### 请求参数

#### Path Parameters

|名称|类型|必选|说明|
|---|---|---|---|
|id|number|是|汇率主数据ID|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "data": {
      "id": 159843409264782,
      "year": 2024,
      "month": 10,
      "usdRate": 7.2500,
      "cnyRate": 1.0000,
      "pkrRate": 0.0260,
      "createTime": "2024-10-24 10:00:00",
      "modifyTime": "2024-10-24 15:30:00",
      "createUserId": 1,
      "modifyUserId": 1,
      "createUserName": "管理员"
    },
    "extendVO": {
      "attachmentRequired": false,
      "operationLogRequired": true,
      "relationRequired": false
    },
    "buttonVO": {
      "buttonSave": true,
      "buttonSubmit": false,
      "buttonDelete": true,
      "buttonClose": false,
      "buttonPost": false
    }
  }
}
```

---
## 3. 汇率主数据保存

### 基本信息

**Path:** /masterdata/exchange-rate/save

**Method:** POST

**Description:** 保存汇率主数据（新增或修改）

### 请求参数

#### Body

```json
{
  "id": 159843409264782,
  "year": 2024,
  "month": 10,
  "usdRate": 7.2500,
  "cnyRate": 1.0000,
  "pkrRate": 0.0260
}
```

#### 参数说明

|名称|类型|必选|说明|
|---|---|---|---|
|id|number|否|主键ID(修改时必填)|
|year|number|是|年份|
|month|number|是|月份|
|usdRate|number|是|美元汇率|
|cnyRate|number|是|人民币汇率|
|pkrRate|number|是|卢比汇率|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

---
## 4. 汇率主数据删除

### 基本信息

**Path:** /masterdata/exchange-rate/remove/{id}

**Method:** DELETE

**Description:** 删除汇率主数据

### 请求参数

#### Path Parameters

|名称|类型|必选|说明|
|---|---|---|---|
|id|number|是|汇率主数据ID|

### 响应参数

```json
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

---
## 数据字典说明

### 汇率主数据字段说明

|字段名|类型|说明|备注|
|---|---|---|---|
|id|Long|主键ID|系统自动生成|
|year|Integer|年份|如：2024|
|month|Integer|月份|1-12|
|usdRate|BigDecimal|美元汇率|相对于人民币的汇率|
|cnyRate|BigDecimal|人民币汇率|基准汇率，通常为1.0000|
|pkrRate|BigDecimal|卢比汇率|相对于人民币的汇率|
|createTime|Date|创建时间|系统自动生成|
|modifyTime|Date|修改时间|系统自动更新|
|createUserId|Long|创建人ID|系统自动记录|
|modifyUserId|Long|修改人ID|系统自动记录|
|createUserName|String|创建人姓名|关联用户表获取|

### 业务规则

1. **唯一性约束**：同一年月只能存在一条汇率记录
2. **数据精度**：汇率数据保留4位小数
3. **必填字段**：年份、月份、三种汇率均为必填
4. **数据范围**：年份范围2000-2099，月份范围1-12
5. **汇率范围**：汇率值必须大于0
