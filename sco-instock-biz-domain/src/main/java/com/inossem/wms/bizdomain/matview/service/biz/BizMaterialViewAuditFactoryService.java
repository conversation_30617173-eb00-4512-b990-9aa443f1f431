package com.inossem.wms.bizdomain.matview.service.biz;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.masterdata.org.service.biz.OfficeService;
import com.inossem.wms.bizbasis.masterdata.org.service.datawrap.DicDeptDataWrap;
import com.inossem.wms.bizdomain.matview.service.datawrap.BizMaterialViewAuditFactoryDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumBatchSize;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.matview.dto.BizMaterialViewAuditFactoryDTO;
import com.inossem.wms.common.model.bizdomain.matview.entity.BizMaterialViewAuditFactory;
import com.inossem.wms.common.model.bizdomain.matview.po.BizMaterialViewAuditFactoryImportPO;
import com.inossem.wms.common.model.bizdomain.matview.vo.BizMaterialViewAuditFactoryExportVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.base.dto.DicDeptOfficeDTO;
import com.inossem.wms.common.model.masterdata.base.entity.DicDept;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.common.util.excel.UtilExcel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 物料主数据视图审批-工厂级别 服务接口
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Slf4j
@Service
public class BizMaterialViewAuditFactoryService {

    @Autowired
    private BizMaterialViewAuditFactoryDataWrap bizMaterialViewAuditFactoryDataWrap;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private DicDeptDataWrap dicDeptDataWrap;
    @Autowired
    private OfficeService officeService;
    @Autowired
    private DataFillService dataFillService;

    /**
     * 保存工厂级别物料视图
     */
    public void save(List<BizMaterialViewAuditFactoryDTO> factoryViewDTOList, Long headId,CurrentUser currentUser ) {
        QueryWrapper<BizMaterialViewAuditFactory> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(BizMaterialViewAuditFactory::getHeadId, headId);
        bizMaterialViewAuditFactoryDataWrap.remove(deleteWrapper);
        if (UtilCollection.isEmpty(factoryViewDTOList)) {
            return;
        }
        int rid = 1;
        for (BizMaterialViewAuditFactoryDTO factoryViewDTO : factoryViewDTOList) {
            factoryViewDTO.setId(null);
            factoryViewDTO.setHeadId(headId);
            factoryViewDTO.setCreateTime(new Date());
            factoryViewDTO.setCreateUserId(currentUser.getId());
            factoryViewDTO.setModifyTime(new Date());
            factoryViewDTO.setModifyUserId(currentUser.getId());
            factoryViewDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            factoryViewDTO.setRid(rid);
            rid++;
        }
        bizMaterialViewAuditFactoryDataWrap.saveBatchDto(factoryViewDTOList);
    }

    /**
     * 根据单据id删除
     */
    public List<BizMaterialViewAuditFactoryDTO> getByHeadId(Long headId) {
        QueryWrapper<BizMaterialViewAuditFactory> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(BizMaterialViewAuditFactory::getHeadId, headId);
        List<BizMaterialViewAuditFactory> list = bizMaterialViewAuditFactoryDataWrap.list(wrapper);
        return UtilCollection.toList(list,BizMaterialViewAuditFactoryDTO.class);
    }

    /**
     * 根据单据id删除
     */
    public void removeByHeadId(Long headId) {
        QueryWrapper<BizMaterialViewAuditFactory> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.lambda().eq(BizMaterialViewAuditFactory::getHeadId, headId);
        bizMaterialViewAuditFactoryDataWrap.remove(deleteWrapper);
    }


    /**
     * 工厂级别物料视图行校验
     */
    public void check(List<BizMaterialViewAuditFactoryDTO> factoryViewDTOList) {
        //按物料码统计个数
        Map<String, Long> countMap = factoryViewDTOList.stream()
                .filter(e -> UtilString.isNotNullOrEmpty(e.getMatCode()))
                .collect(Collectors.groupingBy(BizMaterialViewAuditFactoryDTO::getMatCode, Collectors.counting()));
        List<String> repeatCodeList = countMap.entrySet().stream().filter(entry -> entry.getValue() > 1L).map(Map.Entry::getKey).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(repeatCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODE_HAVE_BEEN_USED);
        }
        for (BizMaterialViewAuditFactoryDTO dto : factoryViewDTOList) {
            this.checkItem(dto);
        }

    }

    private void checkItem(BizMaterialViewAuditFactoryDTO dto) {
        if (UtilString.isNullOrEmpty(dto.getMatCode())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
        }
        Long matId = dictionaryService.getMatIdByMatCode(dto.getMatCode());
        if (UtilNumber.isEmpty(matId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
        }
        QueryWrapper<DicDept> deptQueryWrapper = new QueryWrapper<>();
        deptQueryWrapper.lambda().eq(DicDept::getDeptCode, dto.getDeptCode());
        List<DicDept> list = dicDeptDataWrap.list(deptQueryWrapper);
        if (UtilCollection.isEmpty(list)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_DEPT_CODE_NOT_ENOUGH);
        }
        Long deptId = list.get(0).getId();
        DicDeptOfficeDTO deptOffice = officeService.getDeptOfficeByCode(deptId, dto.getOfficeCode());
        if (UtilObject.isNull(deptOffice)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_DEPT_CODE_NOT_ENOUGH);
        }
        if (UtilNumber.isEmpty(dto.getMatCategory())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_TYPES_CODE_CAN_NOT_BE_EMPTY);
        }
//        if (UtilNumber.isNull(dto.getPackageType())) {
//            throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "包装方式不能为空");
//        }
//        if (UtilNumber.isNull(dto.getStorageType())) {
//            throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "存储方式不能为空");
//        }
//        if (UtilNumber.isEmpty(dto.getInspectType())) {
//            throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "验收方式不能为空");
//        }
//        if (UtilString.isNotNullOrEmpty(dto.getBatchSize())
//                && dto.getBatchSize().equals(EnumBatchSize.FX.name())
//                && UtilNumber.isEmpty(dto.getFixedBatch())){
//            throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "固定批量大小不能为空");
//        }
//        if (UtilString.isNotNullOrEmpty(dto.getBatchSize())
//                && dto.getBatchSize().equals(EnumBatchSize.HB.name())
//                && UtilNumber.isEmpty(dto.getStockMaximum())){
//            throw new WmsException(EnumReturnMsg.INIT_EXCEPTION_DES, "最大库存水平不能为空");
//        }
        dto.setMatId(matId);
        dto.setDeptId(deptId);
        dto.setOfficeId(deptOffice.getId());
    }

    /**
     * 更新状态为审批中
     */
    public void approving(Long headId) {
        UpdateWrapper<BizMaterialViewAuditFactory> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizMaterialViewAuditFactory::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue())
                .eq(BizMaterialViewAuditFactory::getHeadId, headId);
        bizMaterialViewAuditFactoryDataWrap.update(wrapper);
    }

    /**
     * 更新状态为已驳回
     */
    public void rejected(Long headId) {
        UpdateWrapper<BizMaterialViewAuditFactory> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizMaterialViewAuditFactory::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue())
                .eq(BizMaterialViewAuditFactory::getHeadId, headId);
        bizMaterialViewAuditFactoryDataWrap.update(wrapper);
    }

    /**
     * 更新状态为已完成
     */
    public void completed(Long headId) {
        UpdateWrapper<BizMaterialViewAuditFactory> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizMaterialViewAuditFactory::getItemStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
                .eq(BizMaterialViewAuditFactory::getHeadId, headId);
        bizMaterialViewAuditFactoryDataWrap.update(wrapper);
    }

    /**
     * 数据导入
     */
    public List<BizMaterialViewAuditFactoryDTO> importData(BizContext ctx) {
        //获取Excel附件
        MultipartFile file = ctx.getContextData(Const.BIZ_CONTEXT_KEY_FILE);
        CurrentUser currentUser = ctx.getCurrentUser();
        List<BizMaterialViewAuditFactoryImportPO> importPOList;
        try {
            //获取EXCEL数据
            importPOList = (List<BizMaterialViewAuditFactoryImportPO>) UtilExcel.readExcelData(file.getInputStream(), BizMaterialViewAuditFactoryImportPO.class, 1);
        } catch (IOException e) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IO_EXCEPTION);
        }
        if (UtilCollection.isEmpty(importPOList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
        }
        Map<String, Long> countMap = importPOList.stream()
                .filter(e -> UtilString.isNotNullOrEmpty(e.getMatCode()))
                .collect(Collectors.groupingBy(BizMaterialViewAuditFactoryImportPO::getMatCode, Collectors.counting()));
        List<String> repeatCodeList = countMap.entrySet().stream().filter(entry -> entry.getValue() > 1L).map(Map.Entry::getKey).collect(Collectors.toList());
        if (UtilCollection.isNotEmpty(repeatCodeList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODE_HAVE_BEEN_USED);
        }
        List<BizMaterialViewAuditFactoryDTO> list = UtilCollection.toList(importPOList, BizMaterialViewAuditFactoryDTO.class);
        this.checkImport(list);
        int rid = 1;
        for (BizMaterialViewAuditFactoryDTO dto : list) {
            dto.setCreateTime(new Date());
            dto.setCreateUserId(currentUser.getId());
            dto.setModifyTime(new Date());
            dto.setModifyUserId(currentUser.getId());
            dto.setRid(rid);
            rid++;
        }
        dataFillService.fillAttr(list);
        return list;
    }

    /**
     * 导入数据校验
     */
    private void checkImport(List<BizMaterialViewAuditFactoryDTO> list) {
        for (int i = 0; i < list.size(); i++) {
            BizMaterialViewAuditFactoryDTO dto = list.get(i);
            if (UtilString.isNullOrEmpty(dto.getFtyCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
            }
            if (UtilString.isNullOrEmpty(dto.getLocationCode())) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_LOCATION_NOT_EXIST);
            }
            Long ftyId = dictionaryService.getFtyIdCacheByCode(dto.getFtyCode());
            if (UtilNumber.isEmpty(ftyId)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_FACTORY_NOT_EXIST);
            }
            Long locationId = dictionaryService.getLocationIdCacheByCode(dto.getFtyCode(), dto.getLocationCode());
            if (UtilNumber.isEmpty(locationId)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_STOCK_LOCATION_NOT_EXIST);
            }
            try {
                this.checkItem(dto);
            }catch (WmsException e){
                String arg = e.getArgs()[0];
                throw new WmsException(EnumReturnMsg.RETURN_CODE_EXCEPTION);
            }
            dto.setFtyId(ftyId);
            dto.setLocationId(locationId);
        }

    }

    /**
     * 工厂物料导出
     */
    public void exportFtyMat(BizContext ctx) {
        Long id = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(id)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        List<BizMaterialViewAuditFactoryDTO> list = this.getByHeadId(id);
        if (UtilCollection.isEmpty(list)){
            throw new WmsException(EnumReturnMsg.RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY);
        }
        dataFillService.fillAttr(list);
        List<BizMaterialViewAuditFactoryExportVO> exportVOList = UtilCollection.toList(list, BizMaterialViewAuditFactoryExportVO.class);
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(this.getFileCode(Const.XLSX));
        bizCommonFile.setFileName(this.getFileName("工厂物料"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());
        // 推送MQ生成可下载文件数据
        ProducerMessageContent genMessage = ProducerMessageContent.messageContent(TagConst.GEN_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(genMessage);
        UtilExcel.writeExcel(BizMaterialViewAuditFactoryExportVO.class, exportVOList, bizCommonFile);
        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * 获取文件名
     */
    private String getFileCode(String ext) {
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        if (ext == null || ext.trim().length() == 0) {
            return uuid;
        } else {
            return String.format("%s.%s", uuid, ext);
        }
    }

    /**
     * 获取文件描述
     */
    private String getFileName(String fileName) {
        String yyyyMmDd = UtilDate.getStringDateForDate(new Date());
        return fileName + "-" + yyyyMmDd;
    }
}
