package com.inossem.wms.bizdomain.transport.service.datawrap;

import com.inossem.wms.bizdomain.transport.dao.BizReceiptTransportRuleMapper;
import com.inossem.wms.common.model.bizdomain.transport.entity.BizReceiptTransportRule;
import com.inossem.wms.common.mybatisplus.BaseDataWrap;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 转储规则配置表 服务实现类
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-04-22
 */
@Service
public class BizReceiptTransportRuleDataWrap extends BaseDataWrap<BizReceiptTransportRuleMapper, BizReceiptTransportRule> {

}
