package com.inossem.wms.bizdomain.unitized.service.component;


import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.*;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.SapInterfaceService;
import com.inossem.wms.bizdomain.stocktaking.dao.BizReceiptStocktakingReportHeadMapper;
import com.inossem.wms.bizdomain.stocktaking.service.datawrap.BizReceiptStocktakingReportHeadDataWrap;
import com.inossem.wms.bizdomain.stocktaking.service.datawrap.BizReceiptStocktakingReportItemDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.*;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.dept.EnumOffice;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingReportHeadDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.dto.BizReceiptStocktakingReportItemDTO;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingReportHead;
import com.inossem.wms.common.model.bizdomain.stocktaking.entity.BizReceiptStocktakingReportItem;
import com.inossem.wms.common.model.bizdomain.stocktaking.po.BizReceiptStocktakingReportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.stocktaking.vo.BizReceiptStocktakingReportHeadPageVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.metadata.po.MetaDataDeptOfficePO;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnitizedStocktakingReportComponent {

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    protected ReceiptAttachmentService receiptAttachmentService;

    @Autowired
    protected BizReceiptStocktakingReportHeadDataWrap bizReceiptStocktakingReportHeadDataWrap;

    @Autowired
    protected BizReceiptStocktakingReportItemDataWrap bizReceiptStocktakingReportItemDataWrap;

    @Autowired
    protected DictionaryService dictionaryService;

    @Autowired
    protected BizReceiptStocktakingReportHeadMapper bizReceiptStocktakingReportHeadMapper;

    @Autowired
    protected SapInterfaceService sapInterfaceService;

    @Autowired
    protected WorkflowService workflowService;

    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;

    @Autowired
    protected ApprovalService approvalService;

    /**
     * 页面初始化
     */
    public void setInit(BizContext ctx) {
        BizResultVO<BizReceiptStocktakingReportHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptStocktakingReportHeadDTO().setReceiptType(EnumReceiptType.UNITIZED_REPORT_STOCK_TAKE.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSubmit(true).setButtonSave(true));
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询盘点报告列表-分页
     */
    public void setPage(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取查询条件对象 ******** */
        BizReceiptStocktakingReportHeadSearchPO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (po == null) {
            po = new BizReceiptStocktakingReportHeadSearchPO();
        }
        po.setUserId(cUser.getId());
        if(po.getPageSize()==0){
            po.setPageSize(Integer.MAX_VALUE);
        }
        /* ********* 分页查询处理 ******** */
        IPage<BizReceiptStocktakingReportHeadPageVO> page = po.getPageObj(BizReceiptStocktakingReportHeadPageVO.class);
        bizReceiptStocktakingReportHeadDataWrap.getBizReceiptStocktakingReportHeadPageVOList(page, po);
        /* ********* 分页结果信息放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new PageObjectVO<>(page.getRecords(), page.getTotal()));
    }

    /**
     * 查询盘点报告详情
     *
     * @in ctx 入参 {@link Long :"抬头表id"}
     * @out ctx 出参 {@link BizResultVO<> ("head":"盘点报告详情","extend":"扩展功能配置","button":"按钮组")}
     */
    public void setInfo(BizContext ctx) {
        /* ********* 从上下文获取盘点报告抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询库存盘点报告 ******** */
        BizReceiptStocktakingReportHead bizReceiptStocktakingReportHead = bizReceiptStocktakingReportHeadDataWrap.getById(headId);
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingReportHeadDTO bizReceiptStocktakingReportHeadDTO = UtilBean.newInstance(bizReceiptStocktakingReportHead, BizReceiptStocktakingReportHeadDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(bizReceiptStocktakingReportHeadDTO);
        /* ********* 设置按钮组权限 ******** */
        ButtonVO buttonVO = this.setButton(bizReceiptStocktakingReportHeadDTO);
        /* ********* 扩展功能配置 ******** */
        ExtendVO extendVO = new ExtendVO();
        /* ********* 库存盘点报告详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptStocktakingReportHeadDTO, extendVO, buttonVO));
    }

    /**
     *  查询盘点报告详情
     */
    public void getInfo(BizContext ctx) {
        /* ********* 从上下文获取盘点报告抬头表id ******** */
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        /* ********* 查询库存盘点报告 ******** */
        BizReceiptStocktakingReportHead bizReceiptStocktakingReportHead = bizReceiptStocktakingReportHeadDataWrap.getById(headId);
        /* ********* 泛型转换 ******** */
        BizReceiptStocktakingReportHeadDTO bizReceiptStocktakingReportHeadDTO = UtilBean.newInstance(bizReceiptStocktakingReportHead, BizReceiptStocktakingReportHeadDTO.class);
        /* ********* 填充关联属性和父子属性 ******** */
        dataFillService.fillAttr(bizReceiptStocktakingReportHeadDTO);
        bizReceiptStocktakingReportHeadDTO.setReportTimeStr(UtilDate.getStringDateForDate(bizReceiptStocktakingReportHeadDTO.getReportTime(),Const.MATTER_MONTH)) ;
        if(bizReceiptStocktakingReportHeadDTO.getReportTime()!=null){
            String stocktakingTimeStr=UtilDate.getStringDateForDate(bizReceiptStocktakingReportHeadDTO.getStocktakingTime(),Const.FORMATTER_DATE3)+Const.HYPHEN+UtilDate.getStringDateForDate(bizReceiptStocktakingReportHeadDTO.getStocktakingEndTime(),Const.FORMATTER_DATE3);
            bizReceiptStocktakingReportHeadDTO.setStocktakingTimeStr(stocktakingTimeStr) ;
        }
        /* ********* 设置按钮组权限 ******** */
        ButtonVO buttonVO = this.setButton(bizReceiptStocktakingReportHeadDTO);
        /* ********* 扩展功能配置 ******** */
        ExtendVO extendVO = new ExtendVO();
        // 详情页 - 设置单据流开启
        extendVO.setRelationRequired(true);
        // 回填单据流
        // bizReceiptStocktakingReportHeadDTO.setRelationList(receiptRelationService.getReceiptTree(bizReceiptStocktakingReportHeadDTO.getReceiptType(), bizReceiptStocktakingReportHeadDTO.getId(), null));
        /* ********* 库存盘点报告详情放入上下文 ******** */
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(bizReceiptStocktakingReportHeadDTO, extendVO, buttonVO));
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO<> ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO<> ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        /* ********* 从上下文获取扩展功能对象 ******** */
        BizResultVO<BizReceiptStocktakingReportHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        /* ********* 设置附件开启/关闭 ******** */
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启审批
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptStocktakingReportHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);

        // 判断业务流程是否需要审批
        boolean wfByReceiptType = UtilConst.getInstance().getWfByReceiptType(EnumReceiptType.UNITIZED_REPORT_STOCK_TAKE.getValue());
        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            if (wfByReceiptType) {
                resultVO.getExtend().setWfRequired(wfByReceiptType);
                if (UtilObject.isNotNull(resultVO.getHead())) {
                    List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                    resultVO.getHead().setApproveList(approveList);
                    if (UtilCollection.isNotEmpty(approveList)) {
                        resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                    }
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 设置单据流
     *
     * @param ctx 上下文
     */
    public void setExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptStocktakingReportHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService
                    .getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 整理批准
     *
     * @param ctx ctx
     */
    public void assemApprove(BizContext ctx) {
        BizResultVO<BizReceiptStocktakingReportHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptStocktakingReportHeadDTO headDTO = resultVO.getHead();

        headDTO.setSign0(UtilPrint.SIGNATURE);
        headDTO.setSign1(UtilPrint.SIGNATURE);
        headDTO.setSign2(UtilPrint.SIGNATURE);
        headDTO.setSign3(UtilPrint.SIGNATURE);
        headDTO.setSign4(UtilPrint.SIGNATURE);
        headDTO.setSign5(UtilPrint.SIGNATURE);
        headDTO.setSign6(UtilPrint.SIGNATURE);
        headDTO.setSign7(UtilPrint.SIGNATURE);
        headDTO.setSign8(UtilPrint.SIGNATURE);
        if (UtilCollection.isNotEmpty(headDTO.getApproveList())){
            //设置审批人和审批时间
            for (int i = 0; i < headDTO.getApproveList().size(); i++) {
                if (i == headDTO.getApproveList().size() - 4){
                    headDTO.setSign0(headDTO.getApproveList().get(i).getAutographData()); // 申请人
                } else if(i == headDTO.getApproveList().size() - 3){
                    headDTO.setSign1(headDTO.getApproveList().get(i).getAutographData()); // 物资领用主管
                } else if(i == headDTO.getApproveList().size() - 2){
                    headDTO.setSign2(headDTO.getApproveList().get(i).getAutographData()); // 物资部门经理
                }
            }
        }
    }

    /**
     * 保存盘点报告
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReportHeadDTO :"盘点报告传输对象"}
     * @out ctx 出参 {@link String ("receiptCode":"盘点报告号")}
     */
    public void saveInfo(BizContext ctx) {
        /* ********* 从上下文获取当前用户信息 ******** */
        CurrentUser cUser = ctx.getCurrentUser();
        /* ********* 从上下文获取盘点报告传输对象 ******** */
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        /* ********* 判断是否为新增盘点报告 ******** */
        String receiptCode = headDTO.getReceiptCode();
        if (UtilNumber.isNotEmpty(headDTO.getId())) {
            bizReceiptStocktakingReportHeadDataWrap.updateDtoById(headDTO);
            // 修改前删除item
            UpdateWrapper<BizReceiptStocktakingReportItem> wrapperItem = new UpdateWrapper<>();
            wrapperItem.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptStocktakingReportItem::getHeadId, headDTO.getId());
            bizReceiptStocktakingReportItemDataWrap.physicalDelete(wrapperItem);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            receiptCode = bizCommonService.getNextSequenceValue(EnumSequenceCode.SEQUENCE_TAKE_REPORT.getValue());
            headDTO.setId(null);
            headDTO.setReceiptCode(receiptCode);
            headDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            headDTO.setCreateUserId(cUser.getId());
            headDTO.setModifyUserId(cUser.getId());
            bizReceiptStocktakingReportHeadDataWrap.saveDto(headDTO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, receiptCode);
        log.debug("盘点报告{}保存成功", receiptCode);
    }

    /**
     * 提交盘点报告
     */
    public void submitInfo(BizContext ctx) {
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 入参上下文
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        //提交盘点报告时赋值提交时间提交人
        headDTO.setSubmitTime(UtilDate.getNow());
        headDTO.setSubmitUserId(user.getId());
        // 保存盘点报告
        this.saveInfo(ctx);
    }
    
    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReportHeadDTO : "headDTO"："要保存附件的盘点报告对象"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        /* ********* 从上下文获取盘点报告对象 ******** */
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        CurrentUser user = ctx.getCurrentUser();
        /* ********* 保存盘点报告附件 ******** */
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(),
                EnumReceiptType.REPORT_STOCK_TAKE.getValue(), user.getId());
        log.debug("保存盘点报告附件成功!");
    }

    /**
     * 保存单据流
     *
     * @param ctx 上下文
     */
    public void saveReceiptTree(BizContext ctx) {
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizCommonReceiptRelation> list = new ArrayList<>();
        BizCommonReceiptRelation bizCommonReceiptRelation = new BizCommonReceiptRelation();
        bizCommonReceiptRelation.setReceiptType(headDTO.getReceiptType());
        bizCommonReceiptRelation.setReceiptHeadId(headDTO.getId());
        bizCommonReceiptRelation.setReceiptItemId(0L);
        bizCommonReceiptRelation.setPreReceiptType(EnumReceiptType.UNITIZED_DOC_STOCK_TAKE.getValue());
        bizCommonReceiptRelation.setPreReceiptHeadId(headDTO.getDocHeadId());
        bizCommonReceiptRelation.setPreReceiptItemId(null);
        list.add(bizCommonReceiptRelation);

        receiptRelationService.multiSaveReceiptTree(list);
    }

    /**
     *  提交盘点报告效验
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReportHeadDTO : "po"："盘点报告传输对象"}
     */
    public void checkSubmit(BizContext ctx) {
        BizReceiptStocktakingReportHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (UtilObject.isNotNull(po)) {


        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
    }

    /**
     * 设置按钮组
     *
     * @param bizReceiptStocktakingReportHeadDTO 库存盘点报告
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptStocktakingReportHeadDTO bizReceiptStocktakingReportHeadDTO) {
        /* ********* 设置单据抬头状态 ******** */
        Integer receiptStatus = bizReceiptStocktakingReportHeadDTO.getReceiptStatus();
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            /* ********* 草稿 -【保存、提交】 ******** */
            buttonVO.setButtonSave(true).setButtonSubmit(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            /* ********* 审批中 打印 ******** */
            buttonVO.setButtonPrint(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            /* ********* 已驳回 提交 ******** */
            buttonVO.setButtonSubmit(true);
        }else if (EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue().equals(receiptStatus)) {
            /* ********* 已完成 打印 ******** */
            buttonVO.setButtonPrint(true);
        }
        return buttonVO;
    }

    /**
     * 更新单据状态为已完成
     *
     * @in ctx 入参 {@link BizReceiptStocktakingReportHeadDTO : "登记单"}
     */
    public void updateStatusCompleted(BizContext ctx) {
        BizReceiptStocktakingReportHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
    }

    /**
     * 单据状态变更通用方法
     *
     * @param id head主表id
     * @param receiptStatus 状态
     */
    public void updateStatus(Long id, Integer receiptStatus) {
        // 单据状态
        BizReceiptStocktakingReportHead head = new BizReceiptStocktakingReportHead();
        head.setId(id);
        head.setReceiptStatus(receiptStatus);
        bizReceiptStocktakingReportHeadDataWrap.updateById(head);
        // 行项目状态
        UpdateWrapper<BizReceiptStocktakingReportItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().set(BizReceiptStocktakingReportItem::getItemStatus, receiptStatus)
                .eq(BizReceiptStocktakingReportItem::getHeadId, id);
        bizReceiptStocktakingReportItemDataWrap.update(wrapper);
    }

    /**
     * 更新单据状态
     *
     * @param headDTO 登记单抬头
     * @param itemDTOList 登记单行项目
     * @param receiptStatus 单据状态
     */
    public void updateStatus(BizReceiptStocktakingReportHeadDTO headDTO, List<BizReceiptStocktakingReportItemDTO> itemDTOList, Integer receiptStatus) {
        if(UtilObject.isNotNull(headDTO)) {
            UpdateWrapper<BizReceiptStocktakingReportHead> headUpdateWrapper = new UpdateWrapper<>();
            headUpdateWrapper.lambda().eq(BizReceiptStocktakingReportHead::getId, headDTO.getId())
                    .set(BizReceiptStocktakingReportHead::getReceiptStatus, receiptStatus);
            bizReceiptStocktakingReportHeadDataWrap.update(headUpdateWrapper);
        }
        if(UtilCollection.isNotEmpty(itemDTOList)) {
            UpdateWrapper<BizReceiptStocktakingReportItem> itemUpdateWrapper = new UpdateWrapper<>();
            itemUpdateWrapper.lambda().in(BizReceiptStocktakingReportItem::getId, itemDTOList.stream().map(p -> p.getId()).collect(Collectors.toList()))
                    .set(BizReceiptStocktakingReportItem::getItemStatus, receiptStatus);
            bizReceiptStocktakingReportItemDataWrap.update(itemUpdateWrapper);
        }
    }

    /**
     * 发起审批
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptStocktakingReportHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 属性填充
        dataFillService.fillAttr(po);
        // 校验审批人
        List<MetaDataDeptOfficePO> userDept = sysUserDeptOfficeRelDataWrap.getUserDept(ctx.getCurrentUser());
        Integer receiptType = this.approveCheckNew(userDept);

        Long receiptId = po.getId();
        String receiptCode = po.getReceiptCode();
        Map<String, Object> variables = new HashMap<>();
        variables.put("ftyCode", po.getFtyCode());
        // 用户所属部门
        variables.put("userDept", userDept);

        // receiptType 675
        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, receiptType, ctx, po.getReportName());
        workflowService.startWorkFlow(receiptId, receiptCode, receiptType, variables);
        // 更新转性单据状态 - 审批中
        this.updateStatus(receiptId, EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());
    }

    /**
     * 校验审批人
     *
     * @param userDept 发起人部门
     */
    private Integer approveCheckNew(List<MetaDataDeptOfficePO> userDept) {
        // 盘点报告
        Integer receiptType = EnumReceiptType.UNITIZED_REPORT_STOCK_TAKE.getValue();
        // 校验发起人是否绑定了部门
        if (UtilCollection.isEmpty(userDept)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_USER_NO_DEPT);
        }

        // 校验每个节点是否有审批人
        List<String> level1UserList = new ArrayList<>();
        List<String> level2UserList = new ArrayList<>();

        for (MetaDataDeptOfficePO deptOfficePO : userDept) {
            String deptCode = deptOfficePO.getDeptCode();
            // 一级审批节点 发起人所属部门物资领用主管
            List<String> userList = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_3);
            level1UserList.addAll(userList);
            // 二级审批节点 发起人所属部门物资部门经理
            List<String> user2List = sysUserDeptOfficeRelDataWrap.getApproveUserCode(deptCode, null, EnumApprovalLevel.LEVEL_6);
            level2UserList.addAll(user2List);
        }
        if (UtilCollection.isEmpty(level1UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
        }
        if (UtilCollection.isEmpty(level2UserList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
        }

        return receiptType;
    }
}
