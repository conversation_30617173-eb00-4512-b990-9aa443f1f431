package com.inossem.wms.bizdomain.exchangerate.controller;

import com.inossem.wms.bizdomain.exchangerate.service.biz.ExchangeRateService;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.exchangerate.dto.DicExchangeRateDTO;
import com.inossem.wms.common.model.masterdata.exchangerate.po.DicExchangeRateSearchPO;
import com.inossem.wms.common.model.masterdata.exchangerate.vo.DicExchangeRatePageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/**
 * 汇率主数据管理Controller
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@Api(tags = "汇率主数据管理")
public class DicExchangeRateController {

    @Autowired
    protected ExchangeRateService exchangeRateService;

    @ApiOperation(value = "汇率主数据列表分页查询", tags = {"主数据管理-汇率主数据"})
    @PostMapping(path = "/masterdata/exchange-rate/results", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<DicExchangeRatePageVO>> getPage(@RequestBody DicExchangeRateSearchPO po, BizContext ctx) {
        return BaseResult.success(exchangeRateService.getPage(ctx));
    }

    @ApiOperation(value = "查询汇率主数据详情信息", tags = {"主数据管理-汇率主数据"})
    @GetMapping(path = "/masterdata/exchange-rate/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BizResultVO<DicExchangeRateDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        return BaseResult.success(exchangeRateService.getInfo(ctx));
    }

    @ApiOperation(value = "保存更新汇率主数据", tags = {"主数据管理-汇率主数据"})
    @PostMapping(path = "/masterdata/exchange-rate/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> save(@RequestBody DicExchangeRateDTO po, BizContext ctx) {
        exchangeRateService.save(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "删除汇率主数据详情信息", tags = {"主数据管理-汇率主数据"})
    @DeleteMapping(path = "/masterdata/exchange-rate/remove/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> remove(@PathVariable("id") Long id, BizContext ctx) {
        exchangeRateService.remove(ctx);
        return BaseResult.success();
    }
}
