<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.bi.dao.BiPurchaseMapper">

    <select id="selectContractAmountSum" resultType="java.math.BigDecimal">
        select
            (select SUM((h.demand_qty * IFNULL((select no_tax_price from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))
                * (1 + IFNULL((select tax_code_rate from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0)))
             from biz_receipt_contract_head h
             where receipt_type = 403
               and receipt_status = 90
               and is_delete = 0)

                +

            (select sum(ifnull(i.tax_price * i.qty, 0))
             from biz_receipt_contract_head h
                      join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
             where h.receipt_type = 402
               and h.receipt_status = 90
               and h.is_delete = 0)
    </select>

    <select id="getCompletedContractCount" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractCountVO">
        select year(create_time)                 year,
               month(create_time)                month,
               SUM(IF(receipt_type = 402, 1, 0)) count402,
               SUM(IF(receipt_type = 403, 1, 0)) count403,
               COUNT(1)                          count_total
        from biz_receipt_contract_head
        where receipt_type in (402, 403)
          and receipt_status = 90
          AND year(create_time) = #{po.year}
          <if test="po.month != null">
              AND month(create_time) &lt;= #{po.month}
          </if>
        group by year(create_time),
                 month(create_time)
    </select>

    <select id="getCompletedContractAmount402" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO">
        select year(h.create_time)                 year,
               month(h.create_time)                month,
               sum(ifnull(i.tax_price * i.qty, 0)) amount402
        from biz_receipt_contract_head h
                 join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
        where h.receipt_type = 403
          and h.receipt_status = 90
          and h.is_delete = 0
          AND year(h.create_time) = #{po.year}
          <if test="po.month != null">
              AND month(h.create_time) &lt;= #{po.month}
          </if>
        group by year(h.create_time),
                 month(h.create_time)
    </select>

    <select id="getCompletedContractAmount403" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO">
        select year(create_time)                                                                                                                  year,
               month(create_time)                                                                                                                 month,
               SUM((h.demand_qty * IFNULL((select no_tax_price from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))
                   * (1 + IFNULL((select tax_code_rate from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))) amount403
        from biz_receipt_contract_head h
        where receipt_type = 403
          and receipt_status = 90
          and is_delete = 0
          AND year(create_time) = #{po.year}
          <if test="po.month != null">
              AND month(create_time) &lt;= #{po.month}
          </if>
        group by year(create_time),
                 month(create_time)
    </select>

    <select id="selectDemandPlanHead"
            resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseDemandPlanVO">
        select id,
               receipt_code,
               create_time,
               create_user_id,
               handle_user_id,
               modify_time,
               demand_plan_type,
               demand_type
        from biz_receipt_demand_plan_head
        where is_delete = 0
          and receipt_type = 400
    </select>

    <select id="selectPurchaseHead"
            resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchasePurchaseVO">
        select h.id,
               h.receipt_code,
               h.receipt_type,
               h.demand_plan_type,
               h.purchase_type,
               h.bid_method,
               h.purchase_subject,
               h.annual_budget_id,
               h.budget_amount,
               h.create_time,
               h.create_user_id,
               h.modify_time,
               ph.id demand_head_id
        from biz_receipt_purchase_apply_head h
                 left join biz_receipt_purchase_apply_item i on i.head_id = h.id and i.is_delete = 0
                 left join biz_receipt_demand_plan_head ph on ph.id = i.pre_receipt_id and ph.is_delete = 0
        where h.is_delete = 0
          and h.receipt_type in (401, 204, 4013, 4014)
        group by h.id, ph.id
    </select>

    <select id="selectContractHead"
            resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseContractVO">
        select h.id,
               h.receipt_code,
               h.receipt_type,
               h.purchase_type,
               h.first_party,
               h.supplier_id,
               CASE WHEN h.receipt_type = 403 THEN i.no_tax_price ELSE 0 END                                            oil_price,
               CASE WHEN h.receipt_type = 403 THEN h.demand_qty * i.no_tax_price ELSE h.contract_amount_exclude_tax END amount,
               h.create_time,
               ah.id                                                                                                    purchase_head_id,
               ph.id                                                                                                    demand_head_id
        from biz_receipt_contract_head h
                 left join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
                 left join biz_receipt_purchase_apply_head ah on (ah.id = i.pre_receipt_head_id or h.oil_purchase_code = ah.receipt_code) and ah.is_delete = 0
                 left join biz_receipt_purchase_apply_item ai on ai.head_id = ah.id and ai.is_delete = 0
                 left join biz_receipt_demand_plan_head ph on (ph.id = ai.pre_receipt_id or i.demand_plan_code = ph.receipt_code) and ph.is_delete = 0
        where h.is_delete = 0
          and h.receipt_type in (402, 403)
        group by h.id, ah.id, ph.id
    </select>

    <insert id="insertBiPurchaseBaseTable">
        INSERT INTO bi_purchase_base_table (
            contract_receipt_code,
            contract_receipt_type,
            contract_receipt_status,
            contract_purchase_type,
            contract_name,
            contract_first_party,
            contract_supplier_id,
            contract_currency,
            contract_amount_exclude_tax,
            contract_demand_qty,
            contract_sub_type,
            contract_sign_date,
            contract_create_time,
            contract_tax_code,
            contract_tax_code_rate,
            contract_no_tax_price,
            contract_pre_receipt_code,
            purchase_receipt_code,
            purchase_receipt_status,
            purchase_receipt_type,
            purchase_demand_plan_type,
            purchase_subject,
            purchase_bid_method,
            purchase_budget_amount,
            purchase_annual_budget_id,
            purchase_create_user_id,
            purchase_create_time,
            purchase_modify_time,
            purchase_pre_receipt_code,
            demand_receipt_code,
            demand_receipt_type,
            demand_receipt_status,
            demand_create_time,
            demand_modify_time,
            demand_plan_type,
            demand_type,
            demand_handle_user_id
        ) VALUES (
                     #{contractReceiptCode},
                     #{contractReceiptType},
                     #{contractReceiptStatus},
                     #{contractPurchaseType},
                     #{contractName},
                     #{contractFirstParty},
                     #{contractSupplierId},
                     #{contractCurrency},
                     #{contractAmountExcludeTax},
                     #{contractDemandQty},
                     #{contractSubType},
                     #{contractSignDate},
                     #{contractCreateTime},
                     #{contractTaxCode},
                     #{contractTaxCodeRate},
                     #{contractNoTaxPrice},
                     #{contractPreReceiptCode},
                     #{purchaseReceiptCode},
                     #{purchaseReceiptStatus},
                     #{purchaseReceiptType},
                     #{purchaseDemandPlanType},
                     #{purchaseSubject},
                     #{purchaseBidMethod},
                     #{purchaseBudgetAmount},
                     #{purchaseAnnualBudgetId},
                     #{purchaseCreateUserId},
                     #{purchaseCreateTime},
                     #{purchaseModifyTime},
                     #{purchasePreReceiptCode},
                     #{demandReceiptCode},
                     #{demandReceiptType},
                     #{demandReceiptStatus},
                     #{demandCreateTime},
                     #{demandModifyTime},
                     #{demandPlanType},
                     #{demandType},
                     #{demandHandleUserId}
                 )
    </insert>

</mapper>
