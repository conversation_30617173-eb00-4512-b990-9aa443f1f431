package com.inossem.wms.system.workflow.service.business.biz;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.oa.service.datawrap.OaTodoDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.workflow.WorkflowConst;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.workflow.EnumApprovalNode;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.model.approval.dto.*;
import com.inossem.wms.common.model.approval.entity.BizApprovalReceiptInstanceRel;
import com.inossem.wms.common.model.approval.entity.BizApprovalRule;
import com.inossem.wms.common.model.approval.po.*;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.oa.entity.OaTodo;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.*;
import com.inossem.wms.system.workflow.config.ICustomProcessDiagramGenerator;
import com.inossem.wms.system.workflow.service.business.datawrap.BizApprovalReceiptInstanceRelDataWrap;
import com.inossem.wms.system.workflow.service.business.datawrap.BizApprovalRuleDataWrap;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.*;
import org.activiti.engine.*;
import org.activiti.engine.history.*;
import org.activiti.engine.impl.TaskServiceImpl;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.activiti.engine.task.TaskQuery;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * activiti service
 *
 * <AUTHOR>
 * @date 2020/7/24 16:04
 */
@Service
@Slf4j
public class WorkflowService {

    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;
    @Autowired
    protected BizApprovalReceiptInstanceRelDataWrap bizApprovalReceiptInstanceRelDataWrap;

    @Autowired
    protected DataFillService dataFillService;

    @Autowired
    private SysUserDataWrap sysUserDataWrap;

    @Autowired
    private RuntimeService runtimeServiceImpl;

    @Autowired
    private TaskService taskServiceImpl;

    @Autowired
    private HistoryService historyServiceImpl;

    @Autowired
    private IdentityService identityServiceImpl;

    @Autowired
    private RepositoryService repositoryServiceImpl;

    @Autowired
    private ProcessEngine processEngineImpl;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private BizApprovalRuleDataWrap bizApprovalRuleDataWrap;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private OaTodoDataWrap oaTodoDataWrap;

    public String startWorkFlow(Long receiptId, String receiptCode, Integer receiptType, Map<String, Object> map) {
        CurrentUser currentUser = bizCommonService.getUser();
        String procId = null;
        // 从数据库审批表中直接查询审批配置
        BizApprovalRule approvalRule = bizApprovalRuleDataWrap.getOne(new QueryWrapper<BizApprovalRule>()
                .lambda()
                .eq(BizApprovalRule::getReceiptType, receiptType));

        if (approvalRule != null && UtilString.isNotNullOrEmpty(approvalRule.getProcId())) {
            procId = approvalRule.getProcId();
        }
        if (UtilString.isNullOrEmpty(procId)) {
            log.warn("业务类型{} 未查找到有效审批流程定义，单据 {} 启动审批流程已跳过", receiptType, receiptCode);
            return null;
        }
        StartProcessInstancePO startPo = new StartProcessInstancePO();

        startPo.setUserCode(currentUser.getUserCode()).setUserId(currentUser.getId()).setReceiptId(receiptId).setBusinessKey(receiptCode)
                .setProcessDefinitionKey(procId).setReceiptCode(receiptCode).setReceiptType(receiptType).setVariables(map);

        // 推送MQ edit by ChangBaoLong 避免消息丢失，采用同步机制发送消息
        ProcessInstanceDTO processInstanceDto = startProcessInstance(startPo);

        // 保存业务与审批关系
        BizApprovalReceiptInstanceRel approvalInfo = UtilBean.newInstance(startPo, BizApprovalReceiptInstanceRel.class);
        approvalInfo.setCreateUserId(startPo.getUserId());
        approvalInfo.setApproveStatus(EnumApprovalStatus.APPROVING.getValue());
        approvalInfo.setProcessInstanceId(processInstanceDto.getProcessInstanceId());
        approvalInfo.setReceiptHeadId(startPo.getReceiptId());
        bizApprovalReceiptInstanceRelDataWrap.save(approvalInfo);
        return procId;
    }

    public String startWorkFlow(Long receiptId, String receiptCode, Integer receiptType, Map<String, Object> map, Integer index) {
        CurrentUser currentUser = bizCommonService.getUser();
        String procId = null;
        // 从数据库审批表中直接查询审批配置
        List<BizApprovalRule> approvalRules = bizApprovalRuleDataWrap.list(new QueryWrapper<BizApprovalRule>()
                .lambda()
                .eq(BizApprovalRule::getReceiptType, receiptType));
        BizApprovalRule approvalRule = approvalRules.get(index);
        if (approvalRule != null && UtilString.isNotNullOrEmpty(approvalRule.getProcId())) {
            procId = approvalRule.getProcId();
        }
        if (UtilString.isNullOrEmpty(procId)) {
            log.warn("业务类型{} 未查找到有效审批流程定义，单据 {} 启动审批流程已跳过", receiptType, receiptCode);
            return null;
        }
        StartProcessInstancePO startPo = new StartProcessInstancePO();

        startPo.setUserCode(currentUser.getUserCode()).setUserId(currentUser.getId()).setReceiptId(receiptId).setBusinessKey(receiptCode)
                .setProcessDefinitionKey(procId).setReceiptCode(receiptCode).setReceiptType(receiptType).setVariables(map);

        // 推送MQ edit by ChangBaoLong 避免消息丢失，采用同步机制发送消息
        ProcessInstanceDTO processInstanceDto = startProcessInstance(startPo);

        // 保存业务与审批关系
        BizApprovalReceiptInstanceRel approvalInfo = UtilBean.newInstance(startPo, BizApprovalReceiptInstanceRel.class);
        approvalInfo.setCreateUserId(startPo.getUserId());
        approvalInfo.setApproveStatus(EnumApprovalStatus.APPROVING.getValue());
        approvalInfo.setProcessInstanceId(processInstanceDto.getProcessInstanceId());
        approvalInfo.setReceiptHeadId(startPo.getReceiptId());
        bizApprovalReceiptInstanceRelDataWrap.save(approvalInfo);
        return procId;
    }

    /**
     * 开启流程
     *
     * @param po 发起流程传输对象
     * @return ProcessInstanceDTO 流程实例传输对象
     */
    public ProcessInstanceDTO startProcessInstance(StartProcessInstancePO po) {
        ProcessInstanceDTO processInstanceDto = new ProcessInstanceDTO();
        // 用来设置启动流程的人员ID，引擎会自动把用户ID保存到activiti:initiator中
        identityServiceImpl.setAuthenticatedUserId(po.getUserCode());

        ProcessInstance processInstance = runtimeServiceImpl.startProcessInstanceByKey(po.getProcessDefinitionKey(),
            po.getBusinessKey(), po.getVariables());
        BeanUtils.copyProperties(processInstance, processInstanceDto);
        log.info("发起流程,流程定义ID {}", processInstance.getProcessInstanceId());

        // 获取单据的自定义跳转节点
        BizApprovalReceiptInstanceRel bizApprovalReceiptInstanceRel = bizApprovalReceiptInstanceRelDataWrap.getOne(new LambdaQueryWrapper<BizApprovalReceiptInstanceRel>()
                        .eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, po.getReceiptId())
                        .isNotNull(BizApprovalReceiptInstanceRel::getJumpApprovalNode)
                        .ne(BizApprovalReceiptInstanceRel::getJumpApprovalNode,  Const.STRING_EMPTY)
                , false);

        // 如果当前流程中没有设置跳转节点，则直接返回
        if(bizApprovalReceiptInstanceRel == null || StringUtils.isEmpty(bizApprovalReceiptInstanceRel.getJumpApprovalNode())){
            return processInstanceDto;
        }

        // 删除自定义跳转节点信息
        bizApprovalReceiptInstanceRelDataWrap.update(new LambdaUpdateWrapper<BizApprovalReceiptInstanceRel>()
                .set(BizApprovalReceiptInstanceRel::getJumpApprovalNode, StringUtils.EMPTY)
                .eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, po.getReceiptId())
        );

        // 获取当前节点
        Task task = taskServiceImpl.createTaskQuery().processInstanceId(processInstanceDto.getProcessInstanceId()).singleResult();

        // 如果当前节点和跳转节点一致，则直接返回
        if(task.getTaskDefinitionKey().equals(bizApprovalReceiptInstanceRel.getJumpApprovalNode())){
            return processInstanceDto;
        }

        BpmnModel bpmnModel = repositoryServiceImpl.getBpmnModel(processInstance.getProcessDefinitionId());

        ((TaskServiceImpl)taskServiceImpl).getCommandExecutor().execute(new CumtomJumpFlowNodeCommand(processInstance.getProcessInstanceId(), task.getTaskDefinitionKey(), bizApprovalReceiptInstanceRel.getJumpApprovalNode(), po.getUserCode(), "CumtomJumpGarbageData", bpmnModel, runtimeServiceImpl));

        return processInstanceDto;
    }

    /**
     * 设置单据变量
     *
     * @param variables   变量
     * @param receiptId   单据ID
     * @param receiptCode 单据Code
     * @param receiptType 单据Type
     */
    public void setBizReceiptVariables(Map<String, Object> variables, Long receiptId, String receiptCode, Integer receiptType, BizContext ctx, String receiptRemark) {
        variables.put(Const.RECEIPT_ID, receiptId);
        variables.put(Const.RECEIPT_CODE, receiptCode);
        variables.put(Const.RECEIPT_TYPE, receiptType);
        variables.put(Const.CURRENT_USER, ctx.getCurrentUser());
        variables.put(Const.RECEIPT_REMARK, receiptRemark);
    }

    /**
     * 审批完成，回调业务方法
     *
     * @param approvalStatus EnumApprovalStatus
     * @param mqKey          mqKey
     */
    public void approvalCallback(Map<String, Object> variables, EnumApprovalStatus approvalStatus, String mqKey) {
        // 质检会签审批通过，调用业务功能
        BizApprovalReceiptInstanceRelDTO bizApprovalReceiptInstanceRelDTO = new BizApprovalReceiptInstanceRelDTO();
        bizApprovalReceiptInstanceRelDTO.setReceiptHeadId((Long) variables.get(Const.RECEIPT_ID));
        bizApprovalReceiptInstanceRelDTO.setReceiptCode(variables.get(Const.RECEIPT_CODE).toString());
        bizApprovalReceiptInstanceRelDTO.setReceiptType((Integer) variables.get(Const.RECEIPT_TYPE));
        bizApprovalReceiptInstanceRelDTO.setApproveStatus(approvalStatus.getValue());
        bizApprovalReceiptInstanceRelDTO.setInitiator((CurrentUser) variables.get(Const.CURRENT_USER));
        bizApprovalReceiptInstanceRelDTO.setIsDiscard(EnumRealYn.TRUE.getStrValue().equals(variables.getOrDefault(Const.IS_DISCARD, StringUtils.EMPTY).toString()) ? EnumRealYn.TRUE.getIntValue() : null);
        ProducerMessageContent message = ProducerMessageContent.messageContent(mqKey, bizApprovalReceiptInstanceRelDTO);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(message);
    }



    /**
     * 通过/拒绝任务
     *
     * @param completeTaskDTO 通过/拒绝审批传输对象
     */
    @Transactional(rollbackFor = Exception.class)
    public void completeTask(CompleteTaskDTO completeTaskDTO) {
        if (UtilString.isNotNullOrEmpty(completeTaskDTO.getComment())
                || completeTaskDTO.getVariables() != null
                || UtilCollection.isNotEmpty(completeTaskDTO.getFileIdList())
                || UtilCollection.isNotEmpty(completeTaskDTO.getImgIdList())) {

            ActivitiCommentDTO activitiCommentDTO = new ActivitiCommentDTO();

            activitiCommentDTO.setCommentMsg(completeTaskDTO.getComment());
            activitiCommentDTO.setApproveStatus("false".equals(completeTaskDTO.getVariables().get("agree").toString()) ? EnumRealYn.FALSE.getStrValue() : EnumRealYn.TRUE.getStrValue());
            activitiCommentDTO.setFileIdList(completeTaskDTO.getFileIdList());
            activitiCommentDTO.setImgIdList(completeTaskDTO.getImgIdList());

            this.addComment(completeTaskDTO.getTaskId(), completeTaskDTO.getProcessInstanceId(), JSONObject.toJSONString(activitiCommentDTO));
        }
        // taskService.resolveTask(taskId,variables);
        taskServiceImpl.claim(completeTaskDTO.getTaskId(), completeTaskDTO.getClaimUser());

        Task task = taskServiceImpl.createTaskQuery().taskId(completeTaskDTO.getTaskId()).singleResult();
        if(Objects.nonNull(task)){
            String taskDefKey = task.getTaskDefinitionKey();
            Map<String, Object> variables = taskServiceImpl.getVariables(task.getId());
            Integer receiptType = Integer.valueOf(variables.get(Const.RECEIPT_TYPE).toString());
            Integer rejectedCount = (Integer) variables.get("rejectedCount");
            Integer approvedCount = (Integer) variables.get("approvedCount");
            if(EnumReceiptType.DELIVERY_NOTICE.getValue().equals(receiptType)){
                if(EnumApprovalNode.LEVEL_3_APPROVAL_NODE.getValue().equals(taskDefKey)){
                    // 离岸送货第三级审批通
                    // 获取当前审批结果
                    Boolean agree = Boolean.valueOf(completeTaskDTO.getVariables().get(Const.AGREE).toString());
                    // 历史审批结果
                    if(agree){
                        approvedCount++;    
                    }else{
                        // 驳回
                        rejectedCount++;
                    }
                    variables.put("approvedCount", approvedCount);
                    variables.put("rejectedCount", rejectedCount);
                }
            }
            completeTaskDTO.getVariables().put("approvedCount", approvedCount);
            completeTaskDTO.getVariables().put("rejectedCount", rejectedCount);
        }

        // 如果指定了驳回节点，则进行自定义跳转(目前的驳回都需要指定驳回节点)
        if(UtilString.isNotNullOrEmpty(completeTaskDTO.getRejectApprovalNode())) {

            // 如果是驳回到发起人，则直接结束流程，如果是驳回到审批节点，则跳转到指定节点
            if(EnumApprovalNode.START_NODE.getValue().equals(completeTaskDTO.getRejectApprovalNode())){
                taskServiceImpl.complete(completeTaskDTO.getTaskId(), completeTaskDTO.getVariables());
            } else {

                // 获取历史流程实例
                HistoricProcessInstance historicProcessInstance = historyServiceImpl.createHistoricProcessInstanceQuery().processInstanceId(completeTaskDTO.getProcessInstanceId()).singleResult();

                BpmnModel bpmnModel = repositoryServiceImpl.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

                ((TaskServiceImpl)taskServiceImpl).getCommandExecutor().execute(new CumtomJumpFlowNodeCommand(completeTaskDTO.getProcessInstanceId(), task.getTaskDefinitionKey(), completeTaskDTO.getRejectApprovalNode(), completeTaskDTO.getClaimUser(), "驳回流程中自定义跳转", bpmnModel, runtimeServiceImpl));
            }

            // 如果是驳回后跳转回当前节点，则把当前节点添加到跳转节点中
            if(EnumRealYn.TRUE.getIntValue().equals(completeTaskDTO.getIsRejectApprovalJumpCurrenNode())){
                // 设置自定义跳转节点为当前节点
                bizApprovalReceiptInstanceRelDataWrap.update(new LambdaUpdateWrapper<BizApprovalReceiptInstanceRel>()
                        .set(BizApprovalReceiptInstanceRel::getJumpApprovalNode, task.getTaskDefinitionKey())
                        .eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, completeTaskDTO.getReceiptId())
                );
            }

        } else {
            // 获取单据的自定义跳转节点
            BizApprovalReceiptInstanceRel bizApprovalReceiptInstanceRel = bizApprovalReceiptInstanceRelDataWrap.getOne(new LambdaQueryWrapper<BizApprovalReceiptInstanceRel>()
                            .eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, completeTaskDTO.getReceiptId())
                            .isNotNull(BizApprovalReceiptInstanceRel::getJumpApprovalNode)
                            .ne(BizApprovalReceiptInstanceRel::getJumpApprovalNode,  Const.STRING_EMPTY)
                    , false);

            // 如果没有自定义跳转节点，则进行正常审批
            if(bizApprovalReceiptInstanceRel == null || StringUtils.isEmpty(bizApprovalReceiptInstanceRel.getJumpApprovalNode())){
                taskServiceImpl.complete(completeTaskDTO.getTaskId(), completeTaskDTO.getVariables());
            } else {

                // 删除自定义跳转节点信息
                bizApprovalReceiptInstanceRelDataWrap.update(new LambdaUpdateWrapper<BizApprovalReceiptInstanceRel>()
                        .set(BizApprovalReceiptInstanceRel::getJumpApprovalNode, StringUtils.EMPTY)
                        .eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, completeTaskDTO.getReceiptId())
                );

                // 获取历史流程实例
                HistoricProcessInstance historicProcessInstance = historyServiceImpl.createHistoricProcessInstanceQuery().processInstanceId(completeTaskDTO.getProcessInstanceId()).singleResult();

                BpmnModel bpmnModel = repositoryServiceImpl.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

                ((TaskServiceImpl)taskServiceImpl).getCommandExecutor().execute(new CumtomJumpFlowNodeCommand(completeTaskDTO.getProcessInstanceId(), task.getTaskDefinitionKey(), bizApprovalReceiptInstanceRel.getJumpApprovalNode(), completeTaskDTO.getClaimUser(), "驳回流程中自定义跳转", bpmnModel, runtimeServiceImpl));
            }
        }

        log.info("完成任务,任务ID {}", completeTaskDTO.getTaskId());
    }

    /**
     * 创建沟通任务
     *
     * @param communicateTaskPO
     */
    @Transactional(rollbackFor = Exception.class)
    public void createCommunicateTask(CommunicateTaskPO communicateTaskPO) {

        ActivitiCommentDTO activitiCommentDTO = UtilBean.newInstance(communicateTaskPO, ActivitiCommentDTO.class);
        activitiCommentDTO.getCommunicateTaskHeadDTO().setUserCode(UtilCurrentContext.getCurrentUser().getUserCode());
        activitiCommentDTO.getCommunicateTaskHeadDTO().setUserName(UtilCurrentContext.getCurrentUser().getUserName());

        // 获取历史流程实例
        HistoricProcessInstance historicProcessInstance = historyServiceImpl.createHistoricProcessInstanceQuery().processInstanceId(communicateTaskPO.getProcessInstanceId()).singleResult();

        BpmnModel bpmnModel = repositoryServiceImpl.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

        ((TaskServiceImpl)taskServiceImpl).getCommandExecutor().execute(new CreateCommunicateTaskCommand(communicateTaskPO.getProcessInstanceId(), bpmnModel, runtimeServiceImpl, activitiCommentDTO));
    }

    /**
     * 完成沟通任务
     *
     * @param communicateTaskPO
     */
    @Transactional(rollbackFor = Exception.class)
    public void completeCommunicateTask(CommunicateTaskPO communicateTaskPO) {

        Comment comment = taskServiceImpl.getTaskComments(communicateTaskPO.getTaskId()).stream().filter(obj -> obj.getFullMessage().startsWith("{")).findFirst().orElse(null);

        // 该沟通任务的详细信息
        ActivitiCommentDTO communicateTaskInfo = JSONObject.parseObject(comment.getFullMessage(), ActivitiCommentDTO.class);

        // 获取当前用户在沟通任务中的信息
        CommunicateTaskItemDTO communicateTaskItemDTO = communicateTaskInfo.getCommunicateTaskHeadDTO().getItemList().stream().filter(item -> UtilCurrentContext.getCurrentUser().getUserCode().equals(item.getUserCode()) && EnumRealYn.FALSE.getStrValue().equals(item.getIsCancel())).findFirst().orElse(null);

        // 设置回复沟通时提交的办理意见
        if(communicateTaskItemDTO != null){
            communicateTaskItemDTO.setCommentMsg(communicateTaskPO.getCommentMsg());
            communicateTaskItemDTO.setApproveStatus(EnumRealYn.TRUE.getStrValue());
            communicateTaskItemDTO.setFileIdList(communicateTaskPO.getFileIdList());
            communicateTaskItemDTO.setImgIdList(communicateTaskPO.getImgIdList());
            communicateTaskItemDTO.setSubmitTime(new Date());

            // 完结当前用户的待办
            hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_APPROVAL, communicateTaskPO.getProcessInstanceId(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), communicateTaskPO.getReceiptCode());

            Map<String, Object> variables = taskServiceImpl.getVariables(communicateTaskPO.getTaskId());
            String subject = (String) variables.get("subject");
            if(UtilString.isNotNullOrEmpty(subject)){

                // 发送发起沟通用户的待办，提醒发起沟通的用户有人已经回复沟通了
                subject = subject.replaceAll("请审批", StringUtils.join("请查看", communicateTaskItemDTO.getUserName(), "的沟通回复并处理流程"));
                hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_APPROVAL, subject, "", communicateTaskPO.getProcessInstanceId(), Arrays.asList(communicateTaskInfo.getCommunicateTaskHeadDTO().getUserCode()), communicateTaskPO.getReceiptCode());
            }

            // 把当前用户从沟通任务中删除
            taskServiceImpl.deleteCandidateUser(communicateTaskPO.getTaskId(), UtilCurrentContext.getCurrentUser().getUserCode());
        }

        // 获取未回复沟通任务行项目数
        int unRepliedCount = communicateTaskInfo.getCommunicateTaskHeadDTO().getItemList().stream().filter(item -> (!EnumRealYn.TRUE.getStrValue().equals(item.getApproveStatus())) && EnumRealYn.FALSE.getStrValue().equals(item.getIsCancel())).collect(Collectors.toList()).size();

        // 只有所有沟通任务行项目都回复了，才设置审批状态为通过
        if(unRepliedCount == Const.ZERO){
            communicateTaskInfo.setApproveStatus(EnumRealYn.TRUE.getStrValue());
        } else {
            communicateTaskInfo.setApproveStatus(null);
        }

        // 更新该沟通任务的信息到comment中
        taskServiceImpl.deleteComment(comment.getId());
        taskServiceImpl.addComment(communicateTaskPO.getTaskId(), communicateTaskPO.getProcessInstanceId(), JSONObject.toJSONString(communicateTaskInfo));

        // 如果该沟通任务所有人都已回复完毕，则完成任务
        if(EnumRealYn.TRUE.getStrValue().equals(communicateTaskInfo.getApproveStatus())){

            // 办理人设置为当前用户
            taskServiceImpl.claim(communicateTaskPO.getTaskId(), UtilCurrentContext.getCurrentUser().getUserCode());

            taskServiceImpl.complete(communicateTaskPO.getTaskId());
        }
    }

    /**
     * 取消沟通任务
     *
     * @param communicateTaskPO
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelCommunicateTask(CommunicateTaskPO communicateTaskPO) {

        // 要取消沟通的用户列表
        List<String> cancelUserCodeList;

        Comment comment = taskServiceImpl.getTaskComments(communicateTaskPO.getTaskId()).stream().filter(obj -> obj.getFullMessage().startsWith("{")).findFirst().orElse(null);

        // 该沟通任务的详细信息
        ActivitiCommentDTO communicateTaskInfo = JSONObject.parseObject(comment.getFullMessage(), ActivitiCommentDTO.class);

        // 如果没有指定要取消的用户，则取消所有用户
        if(UtilCollection.isEmpty(communicateTaskPO.getCommunicateTaskHeadDTO().getItemList())){
            cancelUserCodeList = communicateTaskInfo.getCommunicateTaskHeadDTO().getItemList().stream().filter(obj -> EnumRealYn.FALSE.getStrValue().equals(obj.getIsCancel())).map(CommunicateTaskItemDTO::getUserCode).collect(Collectors.toList());
        } else {
            cancelUserCodeList = communicateTaskPO.getCommunicateTaskHeadDTO().getItemList().stream().map(CommunicateTaskItemDTO::getUserCode).collect(Collectors.toList());
        }

        // 删除待办
        hXOaIntegerfaceService.deleteTodo(HXOaIntegerfaceService.SEND_TYPE_APPROVAL, communicateTaskPO.getProcessInstanceId(), cancelUserCodeList, communicateTaskPO.getReceiptCode());

        // 取消沟通任务行项目
        communicateTaskInfo.getCommunicateTaskHeadDTO().getItemList().stream().forEach(item -> {
            if(cancelUserCodeList.contains(item.getUserCode())){
                item.setIsCancel(EnumRealYn.TRUE.getStrValue());
            }
        });

        // 取消沟通的用户从沟通任务中删除
        cancelUserCodeList.stream().forEach(obj -> taskServiceImpl.deleteCandidateUser(communicateTaskPO.getTaskId(), obj));

        // 更新该沟通任务的信息到comment中
        taskServiceImpl.deleteComment(comment.getId());
        taskServiceImpl.addComment(communicateTaskPO.getTaskId(), communicateTaskPO.getProcessInstanceId(), JSONObject.toJSONString(communicateTaskInfo));
    }

    /**
     * 获取沟通任务信息
     *
     * @param taskId 任务id
     */
    public CommunicateTaskInfoDTO getCommunicateTaskInfo(String taskId) {

        CommunicateTaskInfoDTO result = new CommunicateTaskInfoDTO();

        Task task = taskServiceImpl.createTaskQuery().taskId(taskId).singleResult();

        // 不是沟通任务直接返回
        if(!EnumApprovalNode.COMMUNICATE_NODE.getValue().equals(task.getTaskDefinitionKey())){
            return result;
        }

        Comment comment = taskServiceImpl.getTaskComments(taskId).stream().filter(obj -> obj.getFullMessage().startsWith("{")).findFirst().orElse(null);

        // 该沟通任务的详细信息
        ActivitiCommentDTO communicateTaskInfo = JSONObject.parseObject(comment.getFullMessage(), ActivitiCommentDTO.class);

        // 办理意见
        result.setCommentMsg(communicateTaskInfo.getCommentMsg());

        // 可以被取消沟通的用户列表
        result.setCanBeCancelUserList(communicateTaskInfo.getCommunicateTaskHeadDTO().getItemList().stream().filter(item -> !EnumRealYn.TRUE.getStrValue().equals(item.getIsCancel())).map(obj -> new SysUser().setUserCode(obj.getUserCode()).setUserName(obj.getUserName())).collect(Collectors.toList()));

        // 可以被沟通的用户列表
        if(UtilCollection.isNotEmpty(communicateTaskInfo.getCommunicateTaskHeadDTO().getChildUserCodeList())){
            result.setCanBeCommunicateUserList(communicateTaskInfo.getCommunicateTaskHeadDTO().getChildUserCodeList().stream().map(obj -> new SysUser().setUserCode(obj).setUserName(dictionaryService.getSysUserCacheByuserCode(obj).getUserName())).collect(Collectors.toList()));
        }

        return result;
    }

    /**
     * 撤销
     *
     * @param revokeDTO 撤销传输对象
     */
    public void revoke(RevokeDTO revokeDTO) {
        runtimeServiceImpl.deleteProcessInstance(revokeDTO.getProcessInstanceId(), revokeDTO.getDeleteReason());
    }

    /**
     * 待办
     *
     * @param todoTasksDTO 待办任务传输对象
     */
    public PageObjectVO<TaskDTO> getTodoTasks(TodoTasksDTO todoTasksDTO) {

        TaskQuery assigneeTaskQuery = taskServiceImpl.createTaskQuery();
        TaskQuery candidateUserTaskQuery = taskServiceImpl.createTaskQuery();
        if (Objects.nonNull(todoTasksDTO.getEndTime()) && Objects.nonNull(todoTasksDTO.getStartTime())) {
            assigneeTaskQuery = assigneeTaskQuery.taskCreatedAfter(todoTasksDTO.getStartTime()).taskCreatedBefore(todoTasksDTO.getEndTime());
            candidateUserTaskQuery = candidateUserTaskQuery.taskCreatedAfter(todoTasksDTO.getStartTime()).taskCreatedBefore(todoTasksDTO.getEndTime());
        }
        List<Task> assigneeList = assigneeTaskQuery.taskAssignee(todoTasksDTO.getUserCode()).orderByTaskCreateTime().desc().list();
        List<Task> candidateUserList = candidateUserTaskQuery.taskCandidateUser(todoTasksDTO.getUserCode()).orderByTaskCreateTime().desc().list();

        for (Task task : candidateUserList) {
            if (!assigneeList.contains(task)) {
                assigneeList.add(task);
            }
        }

        // 先内存分页再查询单据备注, 避免循环查询次数过多
        List<Task> pageList = UtilRamPage.page(assigneeList, todoTasksDTO.getPageIndex(), todoTasksDTO.getPageSize());

        // 设置单据信息
        List<TaskDTO> taskDTOS = new ArrayList<>();
        List<String> processInstanceIdList = pageList.stream().map(Task::getProcessInstanceId).collect(Collectors.toList());
        if (!UtilCollection.isEmpty(processInstanceIdList)) {
            Map<String, BizApprovalReceiptInstanceRelDTO> taskMap = this.getReceiptApproval(processInstanceIdList);
            for (Task task : pageList) {
                TaskDTO taskDTO = UtilBean.newInstance(task, TaskDTO.class);
                BizApprovalReceiptInstanceRelDTO bizApprovalReceiptInstanceRelDto = taskMap.get(task.getProcessInstanceId());
                if (Objects.nonNull(bizApprovalReceiptInstanceRelDto)) {
                    taskDTO.setReceiptId(bizApprovalReceiptInstanceRelDto.getReceiptHeadId());
                    taskDTO.setReceiptCode(bizApprovalReceiptInstanceRelDto.getReceiptCode());
                    taskDTO.setReceiptType(bizApprovalReceiptInstanceRelDto.getReceiptType());
                    taskDTO.setUserId(bizApprovalReceiptInstanceRelDto.getCreateUserId());
                    taskDTO.setUserName(bizApprovalReceiptInstanceRelDto.getUserName());
                    taskDTO.setCreateTime(bizApprovalReceiptInstanceRelDto.getCreateTime());
                    Map<String, Object>  taskInsMap = taskServiceImpl.getVariables(taskDTO.getId());
                    if (taskInsMap != null && UtilCollection.isNotEmpty(taskInsMap.keySet())) {
                        String receiptRemark = UtilObject.getStringOrEmpty(taskInsMap.get(Const.RECEIPT_REMARK));
                        if (StringUtils.isNotBlank(receiptRemark)) {
                            taskDTO.setReceiptRemark(receiptRemark);
                        }
                    }
                    taskDTOS.add(taskDTO);
                }
            }
        }

        return new PageObjectVO<>(taskDTOS, candidateUserTaskQuery.count());
    }

    /**
     * 已办
     *
     * @param doneTasksDTO 已办任务传输对象
     * @return HistoricTaskInstanceDTO 历史任务传输对象
     */
    public PageObjectVO<TaskDTO> getDoneTasks(DoneTasksDTO doneTasksDTO) {
        HistoricTaskInstanceQuery historicTaskInstanceQuery = historyServiceImpl.createHistoricTaskInstanceQuery();
        if (Objects.nonNull(doneTasksDTO.getEndTime()) && Objects.nonNull(doneTasksDTO.getStartTime())) {
            historicTaskInstanceQuery = historicTaskInstanceQuery.taskCreatedAfter(doneTasksDTO.getStartTime()).taskCreatedBefore(doneTasksDTO.getEndTime());
        }

        List<HistoricTaskInstance> list = historicTaskInstanceQuery.taskAssignee(doneTasksDTO.getUserCode()).finished().orderByHistoricTaskInstanceEndTime().desc().list();

        // 先内存分页再查询单据备注, 避免循环查询次数过多
        List<HistoricTaskInstance> pageList = UtilRamPage.page(list, doneTasksDTO.getPageIndex(), doneTasksDTO.getPageSize());

        // 设置单据信息
        List<TaskDTO> taskDTOS = new ArrayList<>();
        List<String> processInstanceIdList = pageList.stream().map(HistoricTaskInstance::getProcessInstanceId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(processInstanceIdList)) {
            Map<String, BizApprovalReceiptInstanceRelDTO> taskMap = this.getReceiptApproval(processInstanceIdList);
            for (HistoricTaskInstance historicTaskInstance : pageList) {
                BizApprovalReceiptInstanceRelDTO bizApprovalReceiptInstanceRelDto = taskMap.get(historicTaskInstance.getProcessInstanceId());
                if (Objects.nonNull(bizApprovalReceiptInstanceRelDto)) {
                    TaskDTO taskDTO = UtilBean.newInstance(historicTaskInstance, TaskDTO.class);
                    taskDTO.setReceiptCode(bizApprovalReceiptInstanceRelDto.getReceiptCode());
                    taskDTO.setReceiptId(bizApprovalReceiptInstanceRelDto.getReceiptHeadId());
                    taskDTO.setReceiptType(bizApprovalReceiptInstanceRelDto.getReceiptType());
                    taskDTO.setUserId(bizApprovalReceiptInstanceRelDto.getCreateUserId());
                    taskDTO.setUserName(bizApprovalReceiptInstanceRelDto.getUserName());
                    taskDTO.setCreateTime(bizApprovalReceiptInstanceRelDto.getCreateTime());
                    List<HistoricVariableInstance> historicVarList = historyServiceImpl.createHistoricVariableInstanceQuery().processInstanceId(historicTaskInstance.getProcessInstanceId()).list();
                    for (HistoricVariableInstance historicVar : historicVarList) {
                        if (historicVar.getVariableName().equals(Const.RECEIPT_REMARK) && UtilObject.isNotEmpty(historicVar.getValue())) {
                            taskDTO.setReceiptRemark(historicVar.getValue().toString());
                        }
                    }
                    taskDTOS.add(taskDTO);
                }
            }
        }
        return new PageObjectVO<>(taskDTOS, historicTaskInstanceQuery.count());
    }

    /**
     * 流程是否存活
     *
     * @param baseDTO 流程是否存活传输对象
     */
    public boolean isActive(BaseDTO baseDTO) {
        ProcessInstance processInstance =
            runtimeServiceImpl.createProcessInstanceQuery().processInstanceId(baseDTO.getProcessInstanceId()).singleResult();
        return !Objects.isNull(processInstance);
    }

    /**
     * 获取历史活动
     *
     * @param po 获取历史活动传输对象
     * @return HistoricActivityDTO 历史活动传输对象
     */
    public PageObjectVO<HistoricActivityDTO> getHistoryActivity(HistoryActivityPO po) {
        HistoricActivityInstanceQuery historicActivityInstanceQuery = historyServiceImpl.createHistoricActivityInstanceQuery();
        List<HistoricActivityInstance> list = historicActivityInstanceQuery.processInstanceId(po.getProcessInstanceId()).activityType("userTask")
            .finished().orderByHistoricActivityInstanceStartTime().desc().list();

        List<HistoricActivityDTO> historicActivityDtoList = new ArrayList<>();
        List<String> userCodeList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(historicActivityInstance -> {
                HistoricActivityDTO historicActivityDto = UtilBean.newInstance(historicActivityInstance, HistoricActivityDTO.class);

                historicActivityDto.setComment(this.getTaskComments(historicActivityInstance.getTaskId()));
                historicActivityDtoList.add(historicActivityDto);
                userCodeList.add(historicActivityInstance.getAssignee());

                if(historicActivityDto.getComment() != null && historicActivityDto.getComment().startsWith("{")){
                    ActivitiCommentDTO activitiCommentDTO = JSONObject.parseObject(historicActivityDto.getComment(), ActivitiCommentDTO.class);

                    historicActivityDto.setComment(activitiCommentDTO.getCommentMsg());
                }
            });
        }

        // 设置审批人姓名
        if (!CollectionUtils.isEmpty(list)) {
            QueryWrapper<SysUser> query = new QueryWrapper<>();
            query.lambda().in(SysUser::getUserCode, userCodeList);
            List<SysUser> assigneeList = sysUserDataWrap.list(query);
            Map<String, SysUser> userMap = assigneeList.stream().collect(Collectors.toMap(SysUser::getUserCode, a -> a, (k1, k2) -> k1));
            historicActivityDtoList.forEach(e -> {
                SysUser user = userMap.get(e.getAssignee());
                if (user != null) {
                    e.setAssigneeName(user.getUserName());
                }

            });
        }

        return new PageObjectVO<>(historicActivityDtoList, historicActivityInstanceQuery.count());
    }

    /**
     * 获取历史活动
     *
     * @param po 获取历史活动传输对象
     * @return HistoricActivityDTO 历史活动传输对象
     */
    public List<HistoricActivityDTO> getHistoryActivityList(HistoryActivityPO po) {
        HistoricActivityInstanceQuery historicActivityInstanceQuery = historyServiceImpl.createHistoricActivityInstanceQuery();
        List<HistoricActivityInstance> list = historicActivityInstanceQuery.processInstanceId(po.getProcessInstanceId()).activityType("userTask")
                .finished().orderByHistoricActivityInstanceStartTime().desc().list();

        List<HistoricActivityDTO> historicActivityDtoList = new ArrayList<>();
        List<String> userCodeList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(historicActivityInstance -> {
                HistoricActivityDTO historicActivityDto = UtilBean.newInstance(historicActivityInstance, HistoricActivityDTO.class);

                historicActivityDto.setComment(this.getTaskComments(historicActivityInstance.getTaskId()));
                historicActivityDtoList.add(historicActivityDto);
                userCodeList.add(historicActivityInstance.getAssignee());
            });
        }

        // 设置审批人姓名
        if (!CollectionUtils.isEmpty(list)) {
            QueryWrapper<SysUser> query = new QueryWrapper<>();
            query.lambda().in(SysUser::getUserCode, userCodeList);
            List<SysUser> assigneeList = sysUserDataWrap.list(query);
            Map<String, SysUser> userMap = assigneeList.stream().collect(Collectors.toMap(SysUser::getUserCode, a -> a, (k1, k2) -> k1));
            historicActivityDtoList.forEach(e -> {
                SysUser user = userMap.get(e.getAssignee());
                if (user != null) {
                    e.setAssigneeName(user.getUserName());
                }

            });
        }

        return historicActivityDtoList;
    }


    public Comment addComment(String taskId, String processInstanceId, String message) {
        return taskServiceImpl.addComment(taskId, processInstanceId, message);
    }

    public String getTaskComments(String taskId) {
        List<Comment> comments = taskServiceImpl.getTaskComments(taskId, "comment");
        if (!CollectionUtils.isEmpty(comments)) {
            return comments.get(0).getFullMessage();
        }
        return "";
    }

    /**
     * 转办
     *
     * @param po 转办传输对象
     */
    @Transactional(rollbackFor = Exception.class)
    public void transferTask(TransferTaskPO po) {
        CurrentUser user = bizCommonService.getUser();
        taskServiceImpl.deleteCandidateUser(po.getTaskId(), user.getUserCode());
        taskServiceImpl.addCandidateUser(po.getTaskId(), po.getUserCode());

        ActivitiCommentDTO activitiCommentDTO = new ActivitiCommentDTO();

        activitiCommentDTO.setCommentMsg(po.getCommentMsg());
        activitiCommentDTO.setFileIdList(po.getFileIdList());
        activitiCommentDTO.setImgIdList(po.getImgIdList());
        activitiCommentDTO.setApproveStatus(EnumRealYn.TRUE.getStrValue());

        // 获取历史流程实例
        HistoricProcessInstance historicProcessInstance = historyServiceImpl.createHistoricProcessInstanceQuery().processInstanceId(po.getProcessInstanceId()).singleResult();

        BpmnModel bpmnModel = repositoryServiceImpl.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

        // 查看当前审批流是否配置了转办节点
        UserTask userTask = (UserTask) bpmnModel.getFlowElement(EnumApprovalNode.TRANSFER_NODE.getValue());

        // 未配置过转办节点的，按照原代码逻辑直接返回
        if(userTask == null){
            return;
        }

        ((TaskServiceImpl)taskServiceImpl).getCommandExecutor().execute(new CreateTransferTaskCommand(po.getProcessInstanceId(), bpmnModel, runtimeServiceImpl, activitiCommentDTO, po.getUserName()));
    }

    /**
     * 获取审批进度
     *
     * @param po 获取审批进度传输对象
     */
    public void getApprovalProgress(ProgressPO po) {
        String processInstanceId = po.getProcessInstanceId();
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = ((ServletRequestAttributes)requestAttributes).getResponse();
        // 设置页面不缓存
        if (response != null) {
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "no-cache");
            response.setDateHeader("Expires", 0);
        }
        String processDefinitionId;
        ProcessInstance processInstance = runtimeServiceImpl.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (Objects.isNull(processInstance)) {
            HistoricProcessInstance historicProcessInstance =
                historyServiceImpl.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            processDefinitionId = historicProcessInstance.getProcessDefinitionId();
        } else {
            processDefinitionId = processInstance.getProcessDefinitionId();
        }
        ProcessDefinitionQuery pdq = repositoryServiceImpl.createProcessDefinitionQuery();
        ProcessDefinition pd = pdq.processDefinitionId(processDefinitionId).singleResult();

        String resourceName = pd.getDiagramResourceName();

        if (resourceName.endsWith(".png") && UtilString.isNotNullOrEmpty(processInstanceId)) {
            getActivitiProcessImage(processInstanceId, response);
            // ProcessDiagramGenerator.generateDiagram(pde, "png",
            // getRuntimeService().getActiveActivityIds(processInstanceId));
        } else {
            // 通过接口读取
            InputStream resourceAsStream = repositoryServiceImpl.getResourceAsStream(pd.getDeploymentId(), resourceName);

            // 输出资源内容到相应对象
            byte[] b = new byte[1024];
            int len = -1;
            try {
                while ((len = resourceAsStream.read(b, 0, 1024)) != -1) {
                    response.getOutputStream().write(b, 0, len);
                }
            } catch (IOException e) {
            }

        }
    }

    /**
     * 获取流程图像，已执行节点和流程线高亮显示
     *
     * @param processInstanceId 流程实例id
     * @param response response
     */
    private void getActivitiProcessImage(String processInstanceId, HttpServletResponse response) {
        try {
            // 获取历史流程实例
            HistoricProcessInstance historicProcessInstance =
                historyServiceImpl.createHistoricProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();

            if (historicProcessInstance == null) {
                // throw new BusinessException("获取流程实例ID[" + pProcessInstanceId + "]对应的历史流程实例失败！");
            } else {
                // 获取流程定义

                // 获取流程历史中已执行节点，并按照节点在流程中执行先后顺序排序
                List<HistoricActivityInstance> historicActivityInstanceList = historyServiceImpl.createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstanceId).orderByHistoricActivityInstanceId().asc().list();

                // 已执行的节点ID集合
                List<String> executedActivityIdList = new ArrayList<>();
                int index = 1;
                for (HistoricActivityInstance activityInstance : historicActivityInstanceList) {
                    executedActivityIdList.add(activityInstance.getActivityId());

                    index++;
                }

                BpmnModel bpmnModel = repositoryServiceImpl.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

                // 已执行的线集合
                List<String> flowIds;
                // 获取流程走过的线
                flowIds = getHighLightedFlows(bpmnModel, historicActivityInstanceList);
                Set<String> currIds = runtimeServiceImpl.createExecutionQuery().processInstanceId(processInstanceId).list().stream()
                    .map(e -> e.getActivityId()).collect(Collectors.toSet());

                ICustomProcessDiagramGenerator diagramGenerator =
                    (ICustomProcessDiagramGenerator)processEngineImpl.getProcessEngineConfiguration().getProcessDiagramGenerator();
                InputStream imageStream =
                    diagramGenerator.generateDiagram(bpmnModel, "png", executedActivityIdList, flowIds, WorkflowConst.FONT, WorkflowConst.FONT,
                        WorkflowConst.FONT, null, 1.0, new Color[] {WorkflowConst.COLOR_NORMAL, WorkflowConst.COLOR_CURRENT}, currIds);

                response.setContentType("image/png");
                OutputStream os = response.getOutputStream();
                int bytesRead;
                byte[] buffer = new byte[8192];
                while ((bytesRead = imageStream.read(buffer, 0, 8192)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.close();
                imageStream.close();
            }
            log.info("获取流程图图像");
        } catch (Exception e) {
            log.error("获取流程图失败！", e);
        }
    }

    private List<String> getHighLightedFlows(BpmnModel bpmnModel, List<HistoricActivityInstance> historicActivityInstances) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 用以保存高亮的线flowId
        List<String> highFlows = new ArrayList<>();

        for (int i = 0; i < historicActivityInstances.size() - 1; i++) {
            // 对历史流程节点进行遍历
            // 得到节点定义的详细信息
            FlowNode activityImpl = (FlowNode)bpmnModel.getMainProcess().getFlowElement(historicActivityInstances.get(i).getActivityId());
            // 用以保存后续开始时间相同的节点
            List<FlowNode> sameStartTimeNodes = new ArrayList<>();
            FlowNode sameActivityImpl1 = null;
            // 第一个节点
            HistoricActivityInstance historicActivityInstance = historicActivityInstances.get(i);
            HistoricActivityInstance historicActivityInstanceNext;

            for (int k = i + 1; k <= historicActivityInstances.size() - 1; k++) {
                // 后续第1个节点
                historicActivityInstanceNext = historicActivityInstances.get(k);
                // 都是usertask，且主节点与后续节点的开始时间相同，说明不是真实的后继节点
                if ("userTask".equals(historicActivityInstance.getActivityType()) && "userTask".equals(historicActivityInstanceNext.getActivityType())
                    && df.format(historicActivityInstance.getStartTime()).equals(df.format(historicActivityInstanceNext.getStartTime()))) {

                } else {
                    // 找到紧跟在后面的一个节点
                    sameActivityImpl1 = (FlowNode)bpmnModel.getMainProcess().getFlowElement(historicActivityInstances.get(k).getActivityId());
                    break;
                }

            }
            // 将后面第一个节点放在时间相同节点的集合里
            sameStartTimeNodes.add(sameActivityImpl1);
            for (int j = i + 1; j < historicActivityInstances.size() - 1; j++) {
                // 后续第一个节点
                HistoricActivityInstance activityImpl1 = historicActivityInstances.get(j);
                // 后续第二个节点
                HistoricActivityInstance activityImpl2 = historicActivityInstances.get(j + 1);
                // 如果第一个节点和第二个节点开始时间相同保存
                if (df.format(activityImpl1.getStartTime()).equals(df.format(activityImpl2.getStartTime()))) {
                    FlowNode sameActivityImpl2 = (FlowNode)bpmnModel.getMainProcess().getFlowElement(activityImpl2.getActivityId());
                    sameStartTimeNodes.add(sameActivityImpl2);
                } else {
                    break;
                }
            }
            // 取出节点的所有出去的线
            List<SequenceFlow> pvmTransitions = activityImpl.getOutgoingFlows();
            // 对所有的线进行遍历
            for (SequenceFlow pvmTransition : pvmTransitions) {
                // 如果取出的线的目标节点存在时间相同的节点里，保存该线的id，进行高亮显示
                FlowNode pvmActivityImpl = (FlowNode)bpmnModel.getMainProcess().getFlowElement(pvmTransition.getTargetRef());
                if (sameStartTimeNodes.contains(pvmActivityImpl)) {
                    highFlows.add(pvmTransition.getId());
                }
            }

        }
        return highFlows;

    }

    public List<Task> getInstanceTasks(String processInstanceId) {
        return taskServiceImpl.createTaskQuery().processInstanceId(processInstanceId).list();
    }

    /**
     * 获取下一级审批节点
     *
     * @param receiptId 单据ID
     */
    public List<SysUser> getNextApproveInfo(Long receiptId) {
        LambdaQueryWrapper<BizApprovalReceiptInstanceRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, receiptId);
        queryWrapper.eq(BizApprovalReceiptInstanceRel::getApproveStatus, 0);
        queryWrapper.orderByDesc(BizApprovalReceiptInstanceRel::getCreateTime);
        List<BizApprovalReceiptInstanceRel> approvalReceiptList = bizApprovalReceiptInstanceRelDataWrap.list(queryWrapper);
        if (UtilCollection.isEmpty(approvalReceiptList)) {
            return new ArrayList<>();
        }
        String processId = approvalReceiptList.get(0).getProcessInstanceId();
        List<Task> taskList = getInstanceTasks(processId);
        Set<String> users = new HashSet<>();
        for (Task task : taskList) {
            users.addAll(getTaskCandidate(task.getId()));
        }
        if (CollectionUtils.isEmpty(users)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SysUser> userQueryWrapper = new LambdaQueryWrapper<>();
        userQueryWrapper.in(SysUser::getUserCode, users);
        return sysUserDataWrap.list(userQueryWrapper);
    }

    /**
     * 删除待办
     */
    public String deleteTodo(Long receiptId) {
        LambdaQueryWrapper<BizApprovalReceiptInstanceRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BizApprovalReceiptInstanceRel::getReceiptHeadId, receiptId);
        queryWrapper.eq(BizApprovalReceiptInstanceRel::getApproveStatus, 0);
        queryWrapper.orderByDesc(BizApprovalReceiptInstanceRel::getCreateTime);
        List<BizApprovalReceiptInstanceRel> approvalReceiptList = bizApprovalReceiptInstanceRelDataWrap.list(queryWrapper);
        if (UtilCollection.isEmpty(approvalReceiptList)) {
            return "";
        }
        String processId = approvalReceiptList.get(0).getProcessInstanceId();
        List<Task> taskList = getInstanceTasks(processId);
        Set<String> users = new HashSet<>();
        for (Task task : taskList) {
            users.addAll(getTaskCandidate(task.getId()));

            List<String> userCodes = users.stream().collect(Collectors.toList());
            hXOaIntegerfaceService.deleteTodo(HXOaIntegerfaceService.SEND_TYPE_APPROVAL, task.getId(), userCodes, approvalReceiptList.get(0).getReceiptCode());
        }
        // 删除沟通待办
        LambdaQueryWrapper<OaTodo> oaTodoWrapper = new LambdaQueryWrapper<>();
        oaTodoWrapper.eq(OaTodo::getTodoId, processId);
        List<OaTodo> oaTodoList = oaTodoDataWrap.list(oaTodoWrapper);
        if (UtilCollection.isNotEmpty(oaTodoList)) {
            hXOaIntegerfaceService.deleteTodo(HXOaIntegerfaceService.SEND_TYPE_APPROVAL, processId, oaTodoList.stream().map(OaTodo::getUserCode).collect(Collectors.toList()), approvalReceiptList.get(0).getReceiptCode());
        }
        return processId;
    }

    /**
     * 获取任务审批人
     *
     * @param taskId 任务ID
     * @return 用户
     */
    private Set<String> getTaskCandidate(String taskId) {
        Set<String> users = new HashSet<>();
        List<IdentityLink> identityLinkList = taskServiceImpl.getIdentityLinksForTask(taskId);
        if (identityLinkList != null && identityLinkList.size() > 0) {
            for (IdentityLink identityLink : identityLinkList) {
                if (identityLink.getUserId() != null) {
                    users.add(identityLink.getUserId());
                }
            }
        }
        return users;
    }


    /**
     * 根据流程实例id查询审批信息
     *
     * @param pIdList
     * @return
     */
    private Map<String, BizApprovalReceiptInstanceRelDTO> getReceiptApproval(List<String> pIdList) {
        QueryWrapper<BizApprovalReceiptInstanceRel> query = new QueryWrapper<>();
        query.lambda().in(BizApprovalReceiptInstanceRel::getProcessInstanceId, pIdList);
        List<BizApprovalReceiptInstanceRel> approvalTaskDtoList = bizApprovalReceiptInstanceRelDataWrap.list(query);
        List<BizApprovalReceiptInstanceRelDTO> dtoList = UtilCollection.toList(approvalTaskDtoList, BizApprovalReceiptInstanceRelDTO.class);
        dataFillService.fillRlatAttrDataList(dtoList);

        return dtoList.stream().collect(Collectors.toMap(BizApprovalReceiptInstanceRelDTO::getProcessInstanceId, a -> a, (k1, k2) -> k1));
    }

    /**
     * 创建新任务
     */
    public Task newTask() {
        return taskServiceImpl.newTask();
    }

    /**
     * 保存任务
     */
    public void saveTask(Task task) {
        taskServiceImpl.saveTask(task);
    }

    /**
     * <AUTHOR>
     *
     * 获取可以进行驳回的节点列表
     *
     * @param po
     * @return
     */
    public List<ModelPO> getRejectApprovalNode(ProgressPO po) {

        List<ModelPO> result = new ArrayList<>();
        result.add(new ModelPO().setKey(EnumApprovalNode.START_NODE.getValue()).setDescription("发起人"));

        // 获取当前节点
        List<Task> taskList = taskServiceImpl.createTaskQuery().processInstanceId(po.getProcessInstanceId()).list();

        if(UtilCollection.isEmpty(taskList)){
            return result;
        }

        // 分支审批节点列表（审批主流程之外的）
        List<String> branchApprovalNodeList = Arrays.asList(EnumApprovalNode.COMMUNICATE_NODE.getValue(), EnumApprovalNode.TRANSFER_NODE.getValue());

        // 过滤掉分支审批节点
        taskList = taskList.stream().filter(obj -> !branchApprovalNodeList.contains(obj.getTaskDefinitionKey())).collect(Collectors.toList());

        int taskLv = Integer.parseInt(taskList.get(0).getTaskDefinitionKey().substring(5,6));

        // 获取历史流程实例
        HistoricProcessInstance historicProcessInstance = historyServiceImpl.createHistoricProcessInstanceQuery().processInstanceId(po.getProcessInstanceId()).singleResult();

        BpmnModel bpmnModel = repositoryServiceImpl.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

        bpmnModel.getProcesses().get(0).getFlowElements().stream().filter(obj -> obj instanceof UserTask && (!branchApprovalNodeList.contains(obj.getId())) && Integer.parseInt(obj.getId().substring(5,6)) < taskLv).sorted(Comparator.comparing(FlowElement::getId)).forEach(obj -> result.add(new ModelPO().setKey(obj.getId()).setDescription(obj.getName())));

        return result;
    }

    /**
     * 设置审批相关按钮权限
     * @param buttonVO 按钮信息
     * @param taskId 当前任务id
     */
    public void setApproveButton(ButtonVO buttonVO, String taskId){
        if(StringUtils.isEmpty(taskId)){
            return;
        }

        // 获取当前任务信息
        Task task = taskServiceImpl.createTaskQuery().taskId(taskId).singleResult();

        // 如果当前是沟通任务
        if(EnumApprovalNode.COMMUNICATE_NODE.getValue().equals(task.getTaskDefinitionKey())){

            // 获取沟通任务的详细信息
            Comment comment = taskServiceImpl.getTaskComments(task.getId()).stream().filter(obj -> obj.getFullMessage().startsWith("{")).findFirst().orElse(null);

            // 该沟通任务的详细信息
            ActivitiCommentDTO communicateTaskInfo = JSONObject.parseObject(comment.getFullMessage(), ActivitiCommentDTO.class);

            // 如果当前用户是沟通任务的发起人，则设置【沟通、取消沟通】按钮可见
            if(UtilCurrentContext.getCurrentUser().getUserCode().equals(communicateTaskInfo.getCommunicateTaskHeadDTO().getUserCode())){
                buttonVO.setButtonApproveCommunicate(EnumRealYn.TRUE.getBoolValue());
                buttonVO.setButtonApproveCommunicateCancel(EnumRealYn.TRUE.getBoolValue());
            } else {
                // 如果当前用户是沟通任务的被沟通人，则回复沟通按钮可见
                buttonVO.setButtonApproveCommunicateComplete(EnumRealYn.TRUE.getBoolValue());

                // 如果当前沟通任务创建时勾选了允许向下沟通，则设置【沟通】按钮可见
                if(EnumRealYn.TRUE.getStrValue().equals(communicateTaskInfo.getCommunicateTaskHeadDTO().getIsCreateChild())){
                    buttonVO.setButtonApproveCommunicate(EnumRealYn.TRUE.getBoolValue());
                }
            }
        } else {
            // 如果当前任务不是沟通任务，则需要查看当前人是否已经发起过未完成的沟通任务

            // 先默认设置审批相关按钮都可见【通过、驳回、转办、沟通、废弃】
            buttonVO.setButtonApproved(EnumRealYn.TRUE.getBoolValue());
            buttonVO.setButtonApproveReject(EnumRealYn.TRUE.getBoolValue());
            buttonVO.setButtonApproveTransfer(EnumRealYn.TRUE.getBoolValue());
            buttonVO.setButtonApproveCommunicate(EnumRealYn.TRUE.getBoolValue());
            buttonVO.setButtonApproveDiscard(EnumRealYn.TRUE.getBoolValue());

            // 当前流程下进行中的所有任务
            List<Task> processTaskList = this.getInstanceTasks(task.getProcessInstanceId());

            for (Task processTask : processTaskList) {

                // 如果当前任务不是沟通任务，则跳过
                if(!EnumApprovalNode.COMMUNICATE_NODE.getValue().equals(processTask.getTaskDefinitionKey())){
                    continue;
                }

                // 获取沟通任务的详细信息
                Comment comment = taskServiceImpl.getTaskComments(processTask.getId()).stream().filter(obj -> obj.getFullMessage().startsWith("{")).findFirst().orElse(null);

                // 该沟通任务的详细信息
                ActivitiCommentDTO communicateTaskInfo = JSONObject.parseObject(comment.getFullMessage(), ActivitiCommentDTO.class);

                // 如果当前用户是沟通任务的发起人，则设置【通过、驳回、转办、废弃】按钮不可见
                if(UtilCurrentContext.getCurrentUser().getUserCode().equals(communicateTaskInfo.getCommunicateTaskHeadDTO().getUserCode())){
                    buttonVO.setButtonApproved(EnumRealYn.FALSE.getBoolValue());
                    buttonVO.setButtonApproveReject(EnumRealYn.FALSE.getBoolValue());
                    buttonVO.setButtonApproveTransfer(EnumRealYn.FALSE.getBoolValue());
                    buttonVO.setButtonApproveDiscard(EnumRealYn.FALSE.getBoolValue());

                    break;
                }
            }
        }
    }
}
