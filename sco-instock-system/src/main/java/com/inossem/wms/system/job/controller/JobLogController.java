package com.inossem.wms.system.job.controller;

import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.job.po.SysJobLogSearchPO;
import com.inossem.wms.common.model.job.vo.SysJobLogPageVO;
import com.inossem.wms.system.job.service.biz.JobLogService;

import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2021/3/3 18:19
 */
@RestController
@Api(tags = "定时任务日志")
public class JobLogController {
    @Autowired
    private JobLogService sysJobLogService;

    /**
     * 查询定时任务调度日志列表
     */
    @ApiOperation(value = "定时任务列表", tags = {"定时任务日志"})
    @PostMapping("/cron/log/results")
    public BaseResult<PageObjectVO<SysJobLogPageVO>> getPage(@RequestBody SysJobLogSearchPO sysJobLogSearchPo) {
        return BaseResult.success(sysJobLogService.getPage(sysJobLogSearchPo));
    }

    /**
     * 删除定时任务调度日志
     */
    @ApiOperation(value = "批量刪除定時任务", tags = {"定时任务日志"})
    @DeleteMapping("/cron/log/{jobLogIds}")
    public BaseResult<?> multiDelete(@PathVariable Long[] jobLogIds) {
        sysJobLogService.multiDeleteJobLogByIds(jobLogIds);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_JOB_DELETE_SUCCESS);
    }

    /**
     * 清空定时任务调度日志
     */
    @ApiOperation(value = "清空定时任务日志", tags = {"定时任务日志"})
    @DeleteMapping("/cron/log/all")
    public BaseResult<?> deleteAll() {
        sysJobLogService.deleteAll();
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_JOB_DELETE_SUCCESS);
    }
}
