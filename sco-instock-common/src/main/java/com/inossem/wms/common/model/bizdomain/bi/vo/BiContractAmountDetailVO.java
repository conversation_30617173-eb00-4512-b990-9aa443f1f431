package com.inossem.wms.common.model.bizdomain.bi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * BI-合同金额明细VO
 *
 * <AUTHOR>
 * @since 2025-01-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BiContractAmountDetailVO对象", description = "BI-合同金额明细VO")
public class BiContractAmountDetailVO {

    @ApiModelProperty(value = "合同ID")
    private Long id;

    @ApiModelProperty(value = "合同币种")
    private Integer currency;

    @ApiModelProperty(value = "合同签订年份")
    private Integer year;

    @ApiModelProperty(value = "合同签订月份")
    private Integer month;

    @ApiModelProperty(value = "合同金额")
    private BigDecimal amount;

}