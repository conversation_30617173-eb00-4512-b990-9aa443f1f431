package com.inossem.wms.common.model.auth.user.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SysUser对象", description = "用户表")
@TableName("sys_user")
public class SysUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID", example = "1")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户编码" , example = "Admin")
    private String userCode;

    @ApiModelProperty(value = "用户名称" , example = "管理员")
    private String userName;

    @ApiModelProperty(value = "密码" , example = "123456")
    private String password;

    @ApiModelProperty(value = "10内部用户， 20外部供应商，30外部客户用户，40外部业务用户，50接口用户" , example = "10")
    private Integer userType;

    @ApiModelProperty(value = "是否被锁定 0-FALSE 1-true" , example = "0")
    private Integer isLocked;

    @ApiModelProperty(value = "0-未冻结，1-已冻结" , example = "0")
    private Integer isFreeze;

    @ApiModelProperty(value = "上次登录时间" , example = "2021-05-10")
    private Date lastLogin;

    @ApiModelProperty(value = "登录错误重试次数" , example = "2")
    private Integer failAttempts;

    @ApiModelProperty(value = "上次登录错误时间试次数" , example = "2021-05-10")
    private Date lastFailAttempt;

    @ApiModelProperty(value = "用户有效期始" , example = "2021-05-10")
    private Date validityBeginDate;

    @ApiModelProperty(value = "用户有效期止" , example = "2022-05-10")
    private Date validityEndDate;

    @ApiModelProperty(value = "组织ID" , example = "1")
    private String orgCode;

    @ApiModelProperty(value = "员工ID" , example = "1")
    private String employeeCode;

    @ApiModelProperty(value = "岗位" , example = "1")
    private String userPost;

    @ApiModelProperty(value = "公司id" , example = "1", required = false)
    private Long corpId;

    @ApiModelProperty(value = "部门id", example = "100", required = true)
    private Long deptId;

    @ApiModelProperty(value = "手机号" , example = "1300000000")
    private String phoneNumber;

    @ApiModelProperty(value = "邮箱" , example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "是否单点登录【1是，0否】" , example = "1")
    private Integer isSyn;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "上次修改密码时间" , example = "2021-05-10")
    private Date lastPasswordModifyTime;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "用户签名id" , example = "1", required = false)
    private Long commonImgId;

}
