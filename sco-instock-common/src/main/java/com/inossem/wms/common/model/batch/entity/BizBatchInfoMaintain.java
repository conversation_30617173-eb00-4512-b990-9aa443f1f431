package com.inossem.wms.common.model.batch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 批次信息维保表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="BizBatchInfoMaintain对象", description="批次信息维保表")
@TableName("biz_batch_info_maintain")
public class BizBatchInfoMaintain implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "批次id" , example = "159707553660932")
    private Long batchId;

    @ApiModelProperty(value = "物料工厂维保id")
    private Long matFtyMaintainId;

    @ApiModelProperty(value = "下次维保日期")
    private Date maintenanceDate;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

}