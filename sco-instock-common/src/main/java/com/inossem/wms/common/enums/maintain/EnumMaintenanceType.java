package com.inossem.wms.common.enums.maintain;

import com.inossem.wms.common.model.common.enums.maintain.MaintenanceTypeMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 维保类型
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2022-06-22
 */
@AllArgsConstructor
public enum EnumMaintenanceType {

    /** 日常维保  202305术语改名 【普通维保】  202307术语改名 【包装更换】 */
    DAILY_MAINTAIN(1,"包装更换"),
    /** 特殊维保  202305术语改名 【专业维保】  202307术语改名 【维护保养】 */
    SPECIAL_MAINTAIN(2,"维护保养"),
    /** 缺陷维保  202307术语改名 【库存状态检查】 202409高温堆又名【包装恢复】 */
    DEFECT_MAINTAIN(3,"库存状态检查"),
    /** 首次保养 验收入库单上架作业完成后自动生成 */
    FIRST_MAINTAIN(4,"首次保养");

    @Getter
    private final Integer value;

    @Getter
    private final String desc;

    public static List<MaintenanceTypeMapVO> list;
    public static List<MaintenanceTypeMapVO> createList;

    public static List<MaintenanceTypeMapVO> toList() {
        if (list == null) {
            List<MaintenanceTypeMapVO> listInner = new ArrayList<>();
            EnumMaintenanceType[] ary = EnumMaintenanceType.values();
            for (EnumMaintenanceType e : ary) {
                MaintenanceTypeMapVO tempMap = new MaintenanceTypeMapVO();
                tempMap.setMaintenanceType(e.getValue());
                listInner.add(tempMap);
            }
            list = listInner;
        }
        return list;
    }

    public static List<MaintenanceTypeMapVO> toCreateList() {
        if (createList == null) {
            List<MaintenanceTypeMapVO> listInner = new ArrayList<>();
            EnumMaintenanceType[] ary = EnumMaintenanceType.values();
            for (EnumMaintenanceType e : ary) {
                // 创建时不允许选中首次保养
                if (!EnumMaintenanceType.FIRST_MAINTAIN.getValue().equals(e.getValue())) {
                    MaintenanceTypeMapVO tempMap = new MaintenanceTypeMapVO();
                    tempMap.setMaintenanceType(e.getValue());
                    listInner.add(tempMap);
                }
            }
            createList = listInner;
        }
        return createList;
    }

    public static String getDescByValue(Integer val) {
        EnumMaintenanceType[] ary = EnumMaintenanceType.values();
        for (EnumMaintenanceType e : ary) {
            if (e.getValue().equals(val)) {
                return e.getDesc();
            }
        }
        return null;
    }

}
