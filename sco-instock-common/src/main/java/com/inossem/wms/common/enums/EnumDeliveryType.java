package com.inossem.wms.common.enums;

import com.inossem.wms.common.model.common.enums.DeliveryTypeMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
public enum EnumDeliveryType {

    // 离岸送货
    OFFSHORE_DELIVERY(1),

    // 内贸送货
    DOMESTIC_TRADE_DELIVERY(2)
    ;

    @Getter
    private final Integer value;

    public static List<DeliveryTypeMapVO> list;

    public static List<DeliveryTypeMapVO> toList() {
        if (list == null) {
            List<DeliveryTypeMapVO> listInner = new ArrayList<>();
            EnumDeliveryType[] ary = EnumDeliveryType.values();
            for (EnumDeliveryType e : ary) {
                DeliveryTypeMapVO tempMap = new DeliveryTypeMapVO();
                tempMap.setDeliveryType(e.getValue());
                listInner.add(tempMap);
            }
            list = listInner;
        }
        return list;
    }

}
