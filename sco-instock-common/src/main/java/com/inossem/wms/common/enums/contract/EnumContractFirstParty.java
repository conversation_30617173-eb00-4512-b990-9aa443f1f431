package com.inossem.wms.common.enums.contract;

import com.inossem.wms.common.model.common.enums.EnumContractFirstPartyMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同甲方枚举
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@Getter
@AllArgsConstructor
public enum EnumContractFirstParty {

    SHANGHAI_ELECTRIC(10, "华信资源有限责任公司"), // "Sino Sindh Resources (PVT.) Limited"
    SHANGHAI_ENERGY(30, "上海能股实业有限公司"); // "Sino Sindh Resources (Private) Limited"

    private final Integer code;
    private final String desc;

    private static List<EnumContractFirstPartyMapVO> list;

    public static List<EnumContractFirstPartyMapVO> toList() {
        if (list == null) {
            List<EnumContractFirstPartyMapVO> listInner = new ArrayList<>();
            EnumContractFirstParty[] ary = EnumContractFirstParty.values();
            for (EnumContractFirstParty e : ary) {
                EnumContractFirstPartyMapVO vo = new EnumContractFirstPartyMapVO();
                vo.setFirstParty(e.getCode());
                listInner.add(vo);
            }
            list = listInner;
        }
        return list;
    }

    public static EnumContractFirstParty getByValue(Integer code) {
        if (code == null) {
            return null;
        }
        for (EnumContractFirstParty e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
} 
