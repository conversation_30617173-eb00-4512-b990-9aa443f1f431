package com.inossem.wms.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 所得税免税文件类型
 * </p>
 *
 */
@AllArgsConstructor
public enum EnumIncomeTaxExemptionFileType {
    /** 发票文件 */
    INVOICE_FILE(1),
    /** 免税文件 */
    TAX_EXEMPT_DOCUMENTS(2),
    /** 缴税文件 */
    TAX_PAYMENT_DOCUMENTS(3);

    /**
     * 存储数据库的属性
     */
    @Getter
    private final Integer value;

}
