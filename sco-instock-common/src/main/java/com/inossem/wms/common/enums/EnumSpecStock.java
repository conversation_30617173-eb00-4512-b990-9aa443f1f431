package com.inossem.wms.common.enums;

import com.inossem.wms.common.model.common.enums.SpecStockMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 特殊库存标识
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum EnumSpecStock {
    /** 正常库存 */
    SPEC_STOCK_BATCH_STATUS_NORMAL(""),
    /** 项目库存 Q */
    SPEC_STOCK_BATCH_STATUS_PROJECT("Q"),
    /** 寄售库存 K */
    SPEC_STOCK_BATCH_STATUS_CONSIGNMENT("K"),
    /** 销售库存 E */
    SPEC_STOCK_BATCH_STATUS_SALE("E"),
    /** 分包库存 O */
    SPEC_STOCK_BATCH_STATUS_SUBPACKAGE("O"),
    /** ins库存 I */
    SPEC_STOCK_BATCH_STATUS_OTHER_INPUT("I"),
    /** 暂存物资 Z */
    SPEC_STOCK_BATCH_STATUS_TEMP_STORE("Z"),
    /** 成套设备 暂存物资 Y */
    SPEC_STOCK_UNITIZED_BATCH_STATUS_TEMP_STORE("Y"),
    /** 工器具物资 T */
    SPEC_STOCK_BATCH_STATUS_TOOL("T"),
    /** 废旧物资 F */
    SPEC_STOCK_BATCH_STATUS_WASTER("F"),
    /** 退旧物资 J */
    SPEC_STOCK_BATCH_STATUS_RETURN_OLD("J"),
    /** 闲置物资 X */
    SPEC_STOCK_BATCH_STATUS_LEISURE("X");

    @Getter
    private final String value;


    public static List<SpecStockMapVO> list;


    public static List<SpecStockMapVO> toList() {
        if (list == null) {
            List<SpecStockMapVO> listInner = new ArrayList<>();
            EnumSpecStock[] ary = EnumSpecStock.values();
            for (EnumSpecStock e : ary) {
                SpecStockMapVO tempMap = new SpecStockMapVO();
                tempMap.setSpecStock(e.getValue());
                listInner.add(tempMap);
            }
            list = listInner;
        }
        return list;
    }

}
