package com.inossem.wms.common.model.label.po;

import com.inossem.wms.common.model.label.dto.BizLabelDataDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @create 2024/8/1 16:44
 * @desc LabelMergePO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "pda标签合并po", description = "pda标签合并po")
public class LabelMergePO implements Serializable {

    private static final long serialVersionUID = -9029133898804518606L;
    /**
     * 标签信息
     */
    private List<BizLabelDataDTO> bizLabelDataDTOList;
}
