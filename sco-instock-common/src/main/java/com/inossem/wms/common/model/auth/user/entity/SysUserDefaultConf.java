package com.inossem.wms.common.model.auth.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 用户默认配置信息表，该表用于记录用户不同业务配置项的个性化信息。通常用于在用户进行某些业务操作时，系统需要记录下不同用户的个性化配置，便于用户操作
 * </p>
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SysUserDefaultConf对象", description="用户默认配置信息表，该表用于记录用户不同业务配置项的个性化信息。通常用于在用户进行某些业务操作时，系统需要记录下不同用户的个性化配置，便于用户操作")
@TableName("sys_user_default_conf")
public class SysUserDefaultConf implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "配置项关键字（具体关键字由前端统一进行管理，前端应有对应的枚举类进行记录与使用）")
    private String confItemKey;

    @ApiModelProperty(value = "配置项对应的值")
    private String confItemValue;


}
