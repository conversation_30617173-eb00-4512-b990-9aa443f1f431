package com.inossem.wms.common.enums.contract;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Getter
@AllArgsConstructor
public enum EnumContractPaymentNode {
    NODE_1(1, "预付款", "合同同步后自动生成"),
    NODE_2(2, "物资类进度款", "合同同步后自动生成"),
    NODE_3(3, "发货款", "根据发货信息（托收PO、内贸、在岸）生成"),
    NODE_4(4, "到货款", "离港时间+条件天数自动生成付款计划"),
    NODE_5(5, "验收款", "入库（物资入库、合同收货）完成后自动生成;"),
    NODE_6(6, "物资类质保金", "入库完成+质保期"),
    NODE_7(7, "工程服务进度款", "根据合同收货自动生成付款预算"),
    NODE_8(8, "工程服务结算款", "据合同状态（已完成/已终止）生成所有已完成产值之和*结算款比例的付款预算");


    private final Integer code;
    private final String desc;
    private final String remark;

    public static EnumContractPaymentNode getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (EnumContractPaymentNode e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

}
