package com.inossem.wms.common.enums;

/**
 * 异常信息
 *
 * <AUTHOR>
 */
public enum EnumReturnMsg {
    // 成功
    RETURN_CODE_SUCCESS,
    // 创建预留单失败
    CREATE_RESERVE_RECEIPT_FAIL,
    // 关闭预留单失败
    CLOSE_RESERVE_RECEIPT_FAIL,
    // 请求处理中
    RETURN_CODE_REQUEST_ALREADY_IN_PROGRESS_EXCEPTION,
    // 失败
    RETURN_CODE_FAILURE,
    // 异常
    RETURN_CODE_EXCEPTION,
    // 未登录
    RETURN_CODE_UNAUTH,
    // 没有访问权限
    RETURN_CODE_ACCESS_DENY,
    // 接口调用失败
    RETURN_CODE_INTERFACE_CALL_FAILURE,
    // XX已存在
    RETURN_CODE_PRIMARY_KEY_EXISTED,
    // XX不存在
    RETURN_CODE_PRIMARY_KEY_NOT_EXIST,
    // "参数不全",
    RETURN_CODE_PARAMETER_LOST,
    // "参数不全 {0}",
    RETURN_CODE_PARAMETER_LOST_WITH_DESC,
    // "参数错误",
    RETURN_CODE_PARAMETER_ERROR,
    // "参数错误 {0}",
    RETURN_CODE_PARAMETER_ERROR_WITH_DESC,
    // "未获取到签名信息",
    RETURN_CODE_NON_AUTOGRAPH,
    // "无权限",
    RETURN_CODE_NO_AUTHORITY,
    // "状态错误",
    RETURN_CODE_RECEIPT_STATUS_FALSE,
    // "行项目为空",
    RETURN_CODE_EMPTY_ITEM,
    // "作业模式不同",
    RETURN_CODE_TASK_MODEL_DIFFERENCE,
    // "仓位模式不同",
    RETURN_CODE_BIN_MODEL_DIFFERENCE,
    // "erp批次启用模式不同",
    RETURN_CODE_ERP_BATCH_MODEL_DIFFERENCE,
    // "生产批次启用模式不同",
    RETURN_CODE_PRODUCTION_BATCH_MODEL_DIFFERENCE,
    // "包装启用模式不同",
    RETURN_CODE_PACKAGE_MODEL_DIFFERENCE,
    // "数量不足",
    RETURN_CODE_QTY_NOT_ENOUGH,
    // {0}物料合同数量大于采购未清数量
    IS_MATERIAL_CONTRACT_EXCEEDS_PURCHASE_OPEN_QUANTITY,
    //{0}  物料单价（不含税）最多保留两位小数
    TRUNCATE_MATERIAL_UNIT_PRICE_TO_TWO_DECIMALS,

    // "结果为空",
    RETURN_CODE_RESULT_EMPTY,
    // "结果溢出",
    RETURN_CODE_RESULT_OVERFLOW,
    // "未找到国际化信息",
    RETURN_CODE_RESULT_TEXT,
    // "库存地点为空"
    RETURN_CODE_LOCATION_EMPTY,
    // "无保存权限",
    RETURN_CODE_NO_AUTHORITY_SAVE,
    // "无提交权限",
    RETURN_CODE_NO_AUTHORITY_SUBMIT,
    // "无过账权限",
    RETURN_CODE_NO_AUTHORITY_POST,
    // "无冲销权限",
    RETURN_CODE_NO_AUTHORITY_WRITE_OFF,
    // "供应商不同",
    RETURN_CODE_SUPPLIER_DIFFERENCE,
    // "批次特性必填",
    RETURN_CODE_BATCHSPEC_REQUIRED,
    // "仓位不存在",
    RETURN_CODE_BIN_ERROR,
    // 同一仓库，同一存储类型，仓位号重复,
    RETURN_CODE_BIN_DUPLICATE_POSITION_NUMBER,
    // 单据号生成失败
    RETURN_CODE_SEQUENCE_GENERATE_FAILURE,
    // 批次号生成失败
    RETURN_CODE_BATCH_GENERATE_FAILURE,
    // 批次特性值获取失败
    RETURN_CODE_BATCH_SPEC_VALUE_FAILURE,
    // 批次特性保存失败
    RETURN_CODE_SAVE_BATCH_SPEC_FAILURE,
    // 批次信息保存失败
    RETURN_CODE_SAVE_BATCH_MSG_FAILURE,
    // 批次特性分类保存失败
    RETURN_CODE_SAVE_BATCH_SPEC_CLASSIFY_FAILURE,
    // 批次特性关系保存失败
    RETURN_CODE_SAVE_REL_BATCH_SPEC_CLASSIFY_FAILURE,
    // 批次信息获取失败
    RETURN_CODE_BATCH_MSG_FAILURE,
    /* ========= WBS编号重复 ========= */
    // 已添加物料的WBS编号不一致，请重新选择
    RETURN_CODE_WBS_CODE_DUPLICATE,
    // WBS编码 {0} 获取失败
    RETURN_CODE_WBS_CODE_GET_FAILED,
    // 已添加物料的工厂不一致，请重新选择
    RETURN_CODE_FTY_DUPLICATE,
    // 批次特性获取失败
    RETURN_CODE_BATCH_SPEC_FAILURE,
    // 单据备注获取失败
    RETURN_CODE_RECEIPT_REMARK,
    // bean转换异常
    RETURN_CODE_BEAN_CAST_EXCEPTION,
    // 借贷标识异常
    RETURN_CODE_DEBIT_CREDIT_EXCEPTION,
    // 前序单据不存在
    RETURN_CODE_REFER_RECEIPT_NOT_EXISTED,
    // "参数不全",
    RETURN_CODE_CLASSIFY_INSPECT_REQUIRED,
    // "配货信息为空",
    RETURN_CODE_EMPTY_BIN,
    // "配货数量异常，请重新配货",
    RETURN_CODE_QUANTITY_DIFFERENCES,
    // "erp批次为空",
    RETURN_CODE_ERP_BATCH_EMPTY,
    // "生产批次为空",
    RETURN_CODE_PRODUCTION_BATCH_EMPTY,
    // "包装类型为空",
    RETURN_CODE_PACKAGE_EMPTY,
    // 抬头备注新增修改失败
    RETURN_CODE_RECEIPT_REMARK_INSERT_OR_UPDATE_EXCEPTION,
    // 仓位为空
    RETURN_CODE_BIN_NULL_EXCEPTION,
    // 冲销行项目在入库单行项内存在未冲销数据
    RETURN_CODE_NO_WRITE_OFF_ITEM_EXCEPTION,
    // 获取ins凭证异常
    RETURN_CODE_GET_INS_MOVE_TYPE_EXCEPTION,
    // 无权限对此单据退库
    RETURN_CODE_REFER_RECEIPT_RETURN_NO_AUTHORITY,
    // INS物料凭证错误
    RETURN_CODE_REFER_RECEIPT_INS_DOC_ERROR,
    // 不同工厂不允许同时送货
    RETURN_CODE_DIFFERENT_FACTORY_NO_DELIVERY,
    // 仓库已被使用，不允许修改仓位等信息
    RETURN_CODE_WH_USED,
    // 公司下面有工厂，不能删除
    RETURN_CODE_DELETE_FAIL_DUE_TO_USED_FACTORY,
    // 工厂下面有储存地点，不能删除
    RETURN_CODE_DELETE_FAIL_DUE_TO_USED_STOCK_LOCATION,
    // 其他入库双主键异常
    RETURN_CODE_OTHER_INPUT_DOUBLE_PRIMARYKEY_EXCEPTION,
    // 行项目数量与仓位数量不一致
    RETURN_CODE_ITEM_QTY_DIFFERENCE_BIN_QTY,
    // 工厂库存地点物料批次 数量不足
    RETURN_CODE_FOURKEY_QTY_NOT_ENOUGH,
    // "数量不足",
    RETURN_CODE_ITEM_QTY_NOT_ENOUGH,
    // "行项目仓库与仓位仓库不一致",
    RETURN_CODE_ITEM_WH_NOT_EQUALS_BIN_WH,
    // "行项目仓位信息为空",
    RETURN_CODE_BIN_LIST_EMPTY,
    // "存储单元为空",
    RETURN_CODE_CELL_EMPTY,
    // "存储类型存储单元未激活",
    RETURN_CODE_TYPE_CELL_ENABLE,
    // "存储单元错误",
    RETURN_CODE_CELL_ERROR,
    // "存储单元多仓位",
    RETURN_CODE_SAME_CELL_MULTIPLY_BIN,
    // "原单位与目标单位不能相同",
    RETURN_CODE_UNIT_CODE_NOT_SAME,
    // "原单位与目标单位不一致",
    RETURN_CODE_UNIT_CODE_MUST_SAME,
    // "数量不能为0",
    RETURN_CODE_QTY_NOT_ZERO,
    // "未配置全部行项目信息"
    RETURN_CODE_NOT_ALL_ITEM_SET,
    // 用户编码为空
    RETURN_CODE_USER_CODE_NOT_ENOUGH,
    // 用户名称为空
    RETURN_CODE_USER_NAME_NOT_ENOUGH,
    // 用户名称为空
    RETURN_CODE_USER_TYPE_NOT_ENOUGH,
    // 公司编码为空
    RETURN_CODE_CORP_CODE_NOT_ENOUGH,
    // 部门编码为空
    RETURN_CODE_DEPT_CODE_NOT_ENOUGH,
    // 用户有效期始为空
    RETURN_CODE_VALIDITY_BEGIN_DATE_NOT_ENOUGH,
    // 用户有效期始为空
    RETURN_CODE_VALIDITY_END_DATE_NOT_ENOUGH,
    // 用户签名不能为空
    RETURN_CODE_COMMON_IMG_NOT_ENOUGH,
    // 用户编码已经存在
    RETURN_CODE_USER_CODE_ALREADY_EXIST,
    // 该用户不存在
    RETURN_CODE_USER_DOES_NOT_EXIST,
    // 用户编码不可以修改
    RETURN_CODE_USER_CODE_CAN_NOT_BE_MODIFIED,
    // 用户类型不可以修改
    RETURN_CODE_USER_TYPE_CAN_NOT_BE_MODIFIED,
    // 公司编码不可以修改
    RETURN_CODE_CORP_CODE_CAN_NOT_BE_MODIFIED,
    // 不可以重复配置库存地点
    RETURN_CODE_NO_DUPLICATION_OF_INVENTORY_LOCATIONS,
    // 不可以重复配置角色
    RETURN_CODE_ROLES_CANNOT_BE_RECONFIGURED_REPEATEDLY,
    // 仓位号在该仓库和存储类型下不存在
    RETURN_CODE_BIN_CODE_NOT_EXIST,
    // 该仓位下的物料数量不能大于数据库中的数量
    RETURN_CODE_CHANGE_QTY_GREATER_THAN_DATA_BASE_QTY,
    // 当前存储类型、仓位和存储单元的库存数量与数据库不一致
    RETURN_CODE_CHANGE_QTY_NOT_SAME_DATA_BASE_QTY,
    // 当前存储类型、仓位和存储单元的行项目数量与数据库不一致
    RETURN_CODE_ROW_COUNT_NOT_SAME_DATA_ROW_COUNT,
    // 如果目标端存储类型启用了存储单元，则目标端的存储单元必填
    RETURN_CODE_TARGET_CELL_CODE_MUST_FILL,
    // 外部用户对应供应商或者客户重复
    RETURN_CODE_EXTERNAL_USER_DUPLICATES_WITH_SUPPLIER_OR_CUSTOMER,
    // 旧密码输入错误
    RETURN_CODE_OLD_PASSWORD_INPUT_ERROR,
    // 两次新密码不一致
    RETURN_CODE_TWO_INCONSISTENT_NEW_PASSWORDS,
    // 新密码和旧密码不可以一致
    RETURN_CODE_NEW_PASSWORD_AND_OLD_PASSWORD_CANNOT_BE_IDENTICAL,
    // "存储单元不存在",
    RETURN_CODE_CELL_CODE_NOT_EXIST,
    // "此承运商税务登记号已存在",
    RETURN_CODE_DELIVERY_CARRIER_EXIST,
    // "此司机身份证号已存在",
    RETURN_CODE_DELIVERY_DRIVER_EXIST,
    // "此车辆车牌号已存在",
    RETURN_CODE_DELIVERY_VEHICLE_EXIST,
    // 用户正在使用无法删除
    RETURN_CODE_THIS_USER_HAS_INCOMPLETE_DOCUMENTS_PLEASE_CONFIRM,
    // 外部用户不存在
    RETURN_CODE_DIC_OUTSIDE_NOT_EXIST,
    // 非已提交状态单据不允许关闭
    RETURN_CODE_NO_SUBMIT_NO_DELETE,
    // 单据不存在
    RETURN_CODE_RECEIPT_NOT_EXIST,
    // 角色编码不可以为空
    RETURN_CODE_ROLE_CODE_NOT_ENOUGH,
    // 角色描述不可以为空
    RETURN_CODE_ROLE_NAME_NOT_ENOUGH,
    // 该角色编码已存在
    RETURN_CODE_ROLE_CODE_ALREADY_EXIST,
    // 该角色描述已存在
    RETURN_CODE_ROLE_NAME_ALREADY_EXIST,
    // 角色不存在
    RETURN_CODE_ROLE_DOES_NOT_EXIST,
    // 角色描述已被使用
    RETURN_CODE_ROLE_DESCRIPTION_HAS_BEEN_USED,
    // 没有此公司
    RETURN_CODE_NO_SUCH_CORP,
    // 没有此工厂
    RETURN_CODE_NO_SUCH_FACTORY,
    // 没有此库存地点
    RETURN_CODE_NO_SUCH_STOCK_LOCATION,
    // 公司已经存在
    RETURN_CODE_CORP_ALERADY_EXISTS,
    // 仓库整理不能存在多个仓库号
    RETURN_CODE_EXIST_MANY_WH_CODE,
    // 请选择相同仓库号物料进行码盘操作
    RETURN_CODE_PALLET_EXIST_MANY_WH_CODE,
    // 填写的目标物料不合法
    RETURN_CODE_VALIDITY_TARGET_MAT_FAIL,
    // 经办人不可以为空
    RETURN_CODE_NULL_ACTOR,
    // 单据提交类型错误
    RETURN_CODE_SUBMIT_TYPE_ERROR,
    // 提交数据中存在已生成过冲销凭证的行项目
    RETURN_CODE_WRITE_OFF_MODEL_ERROR,
    // 该供应商存在非已结算状态的服务工单: {0} ,请先处理后再进行操作
    RETURN_CODE_SUPPLIER_SERVICE_RECEIPT_DUPLICATE,
    // 当前单据已关联: {0} 单据，不允许撤销
    RETURN_CODE_HAS_ASSOCIATED_RECEIPT_NOT_REVOKE,
    // 该单据还存在有关联的作业单数据,请先处理后再进行操作
    RETURN_CODE_HAS_TASK,
    // 作业数量异常
    RETURN_CODE_TASK_QTY_ERROR,
    // 仓库为空
    RETURN_CODE_WH_EMPTY,
    // 存储类型为空
    RETURN_CODE_STORAGE_TYPE_EMPTY,
    // 移动类型异常
    RETURN_CODE_MOVE_TYPE_ERROR,
    // 物料编码不可以为空
    RETURN_CODE_MATERIAL_CODING_CAN_NOT_BE_EMPTY,
    // 语言类型不可以为空
    RETURN_CODE_LANGUAGE_TYPES_CAN_NOT_BE_EMPTY,
    // 物料描述不可以为空
    RETURN_CODE_MATERIAL_NAME_CAN_NOT_BE_EMPTY,
    // 物料类型编码不可以为空
    RETURN_CODE_MATERIAL_TYPES_CODE_CAN_NOT_BE_EMPTY,
    // 计量单位编码不可以为空
    RETURN_CODE_UNITCODE_CAN_NOT_BE_EMPTY,
    // 物料组编码不可以为空
    RETURN_CODE_MATERIAL_GROUP_CODE_CAN_NOT_BE_EMPTY,
    // 物料编码已经被使用
    RETURN_CODE_MATERIAL_CODE_HAVE_BEEN_USED,
    // 物料编码不可以修改
    RETURN_CODE_MAT_CODE_CAN_NOT_BE_MODIFIED,
    // 物料类型不可以修改
    RETURN_CODE_MAT_TYPE_CAN_NOT_BE_MODIFIED,
    // 单位编码不可以修改
    RETURN_CODE_UNIT_CODE_CAN_NOT_BE_MODIFIED,
    // 仓库号已存在
    RETURN_CODE_WH_CODE_EXIST,
    // 存储类型已存在
    RETURN_CODE_TYPE_CODE_EXIST,
    // 仓位已存在
    RETURN_CODE_BIN_CODE_EXIST,
    // 仓位已认领
    RETURN_CODE_BIN_IS_GET,
    // 数据{0}已存在
    RETURN_CODE_DATA_EXIST,
    // 数据{0}不存在
    RETURN_CODE_DATA_NOT_EXIST,
    // 登录信息不正确
    RETURN_CODE_INCORRECT_LOGIN_INFORMATION,
    // 单据类型不可以为空
    RETURN_CODE_RECEIPT_TYPE_CAN_NOT_BE_EMPTY,
    // 据编码不可以为空
    RETURN_CODE_RECEIPT_CODE_CAN_NOT_BE_EMPTY,
    // "无删除权限",
    RETURN_CODE_NO_AUTHORITY_DELETE,
    // "无操作权限",
    RETURN_CODE_NO_AUTHORITY_OPERATION,
    // 不合格数量大于到货数量
    RETURN_CODE_UNQUALIFIED_QTY_GREATER_THAN_ARRIVAL_QTY,
    // 行项目中存在相同的物料{0}和批次{1}信息
    RETURN_CODE_SAME_ROW_INFO,
    // 启用仓位入库计划不能为空
    RETURN_CODE_TASK_INPUT_NOT_EMPTY,
    // 启用仓位出库计划不能为空
    RETURN_CODE_TASK_OUTPUT_NOT_EMPTY,
    // 语言编码不能相同
    RETURN_CODE_LANGUAGE_CODE_NOT_SAME,
    // 自动过账配置不同
    RETURN_CODE_AUTO_POST_DIFFERENCE,
    // 参数长度不正确
    RETURN_CODE_PARAMETER_LENGTH_ERROR,
    // 选中后不可以取消
    RETURN_CODE_CHECKED_CANNOT_BE_CANCELED,
    // erp批次配置错误
    RETURN_CODE_BATCH_ERP_TYPE_ERROR,
    // 存储单元不允许取消激活
    RETURN_CODE_CELL_NOT_CANCEL_ACTIVATION,
    // 库存地点已经被使用
    RETURN_CODE_LOCATION_USED,
    // 当前仓库已经绑定了工厂{0}和库存地点{1}，不允许修改
    RETURN_CODE_LOCATION_USED_NOT_MODIFY,
    /** 缺少单据{0}指定的库存地点{1}权限 */
    RETURN_CODE_LACK_OF_RECEIPT_SPECIFIED_PERMISSIONS,
    /** 存储类型和仓位不能为空 */
    RETURN_CODE_TYPE_AND_BIN_NOT_NULL,
    /** 接收库存地点不能为空 */
    RETURN_CODE_INPUT_LOCATION_CODE_NOT_NULL,
    // 缺少工厂代码，无法修改库存
    RETURN_CODE_MODIFY_STOCK_MISS_FACTORY_CODE,
    // 缺少物料代码，无法修改库存
    RETURN_CODE_MODIFY_STOCK_MISS_MATERIAL_CODE,
    // 缺少单位代码，无法修改库存
    RETURN_CODE_MODIFY_STOCK_MISS_UNIT_CODE,
    // 缺少库存状态代码，无法修改库存
    RETURN_CODE_MODIFY_STOCK_MISS_STOCK_STATUS,
    // 批次库存仓位库存不平，无法修改库存
    RETURN_CODE_MODIFY_STOCK_STOCK_UNEQUAL,
    // 批次{0}的移动数量必须为正数
    RETURN_CODE_MODIFY_STOCK_MOVE_QTY_MUST_BE_POSITIVE,
    // 参考单据行{0}的物料代码必须相同
    RETURN_CODE_MODIFY_STOCK_REFER_RECEIPT_RID_MATERIAL_SAME,
    // 参考单据行{0}的单位代码必须相同
    RETURN_CODE_MODIFY_STOCK_REFER_RECEIPT_RID_UNIT_SAME,
    // 参考单据行{0}的库存状态必须相同
    RETURN_CODE_MODIFY_STOCK_REFER_RECEIPT_RID_STOCK_STATUS_SAME,
    // 批次{0}的实际修改数量计算失败，无法修改库存
    RETURN_CODE_MODIFY_STOCK_ACTUAL_QTY_ERROR,
    // 经办组{0}中存在此岗位，不能删除
    RETURN_CODE_GROUP_CONTAIN_THIS_POST_CANNOT_BE_DELETE,
    // 请选择角色信息
    RETURN_CODE_POST_CONTAIN_ROLE_CANNOT_BE_NULL,
    // 角色代码{0}未被分配用户，不能保存
    RETURN_CODE_ROLE_CONTAIN_USER_CANNOT_BE_NULL,
    // 请选择岗位信息
    RETURN_CODE_GROUP_CONTAIN_POST_CANNOT_BE_NULL,
    // 岗位代码{0}未被分配用户，不能保存
    RETURN_CODE_POST_CONTAIN_USER_CANNOT_BE_NULL,
    // 岗位代码{0}已存在
    RETURN_CODE_POST_CODE_DO_EXIST,
    // 经办组代码{0}已存在
    RETURN_CODE_GROUP_CODE_DO_EXIST,
    // 业务类型{0}公司默认经办组已存在
    RETURN_CODE_RECEIPT_TYPE_DEFAULT_CORP_GROUP_HAS_EXIST,
    // 业务类型{0}个人默认经办组已存在
    RETURN_CODE_RECEIPT_TYPE_DEFAULT_OWN_GROUP_HAS_EXIST,
    // 当前盘点单不是所有的行项目都是计数状态，不能全部重盘
    RETURN_CODE_NOT_ALL_ROW_DATE_COUNTED_STATUS,
    // {0}必须整单冲销
    RETURN_CODE_SAP_WRITE_OFF_ONLY_RECEIPT_EXCEPTION,
    // {0}行项目不可冲销
    RETURN_CODE_ITEM_CAN_NOT_WRITE_OFF,
    // {0}行项目不可撤销
    RETURN_CODE_ITEM_CAN_NOT_REVOKE,
    // 当前盘点单不是所有的行项目都是计数状态，不能进行仓位差异处理
    RETURN_CODE_NOT_ALL_ROW_DATE_COUNTED_STATUS_BIN_DIFFERENCE,
    // 当前盘点单不是所有的行项目都是仓位差异处理 状态，不能进行批次差异处理
    RETURN_CODE_NOT_ALL_ROW_DATE_BIN_DIFFERENCE_STATUS,
    // 当前盘点单存在已计数项，不能删除
    RETURN_CODE_EXITS_ROW_COUNTED_DELLETE_FAILURE,
    // 当前盘点单存在未计数项，不能重新盘点
    RETURN_CODE_EXITS_ROW_UN_COUNTED_REINVERTORY_FAILURE,
    // 无差异物料不能进行盘点过账
    RETURN_CODE_NO_DIFFERENCE_CAN_NOT_POST,
    // 不存在差异物料，无法进行差异复盘
    RETURN_CODE_CAN_NOT_REINVETORY,
    // {0}行项目已入库不可冲销
    RETURN_CODE_INSPECT_HAS_INPUTED,
    // {0}行项目已创建验收入库单不可冲销
    RETURN_CODE_INSPECT_HAS_CREATE_INPUT,
    // {0}行项目已作业 无法删除
    RETURN_CODE_CAN_NOT_DELETE_BY_HAS_TASK,
    // 单据已过账无法删除
    RETURN_CODE_CAN_NOT_DELETE_BY_HAS_POST,
    // 单据不可删除
    RETURN_CODE_CAN_NOT_DELETE,
    // 下架作业单对应{0}上架请求已作业
    RETURN_CODE_UN_LOAD_TASK_REFER_LOAD_REQ_HAS_TASK,
    // 物料{0}和工厂{1}下不存在该批次{2}
    RETURN_CODE_MAT_FTY_NOT_EXIST_BATCH,
    // {0}行项目仓位库存不足无法冲销
    RETURN_CODE_WRITE_OFF_BIN_QTY_NOT_ENOUGH,
    // 验收单可入库行项目为空
    RETURN_CODE_INSPEC_INPUT_ITEM_EMPTY,
    // 验收1
    RETURN_CODE_INSPEC_SUCCESS,
    // 验收单撤销成功
    RETURN_CODE_INSPEC_REVOKE_SUCCESS,
    // 验收单{0}保存成功
    RETURN_CODE_INSPEC_SAVE_SUCCESS,
    // 验收单{0}提交成功
    RETURN_CODE_INSPEC_SUBMIT_SUCCESS,
    // 验收单{0}过账成功
    RETURN_CODE_INSPEC_POST_SUCCESS,
    // 验收单{0}冲销成功
    RETURN_CODE_INSPEC_WRITE_OFF_SUCCESS,
    // 验收单{0}删除成功
    RETURN_CODE_INSPEC_DELETE_SUCCESS,
    // 验收入库单{0}保存成功
    RETURN_CODE_INSPEC_INPUT_SAVE_SUCCESS,
    // 验收入库单{0}提交成功
    RETURN_CODE_INSPEC_INPUT_SUBMIT_SUCCESS,
    // 验收入库单{0}删除成功
    RETURN_CODE_INSPEC_INPUT_DELETE_SUCCESS,
    // 验收入库单{0}冲销成功
    RETURN_CODE_INSPEC_INPUT_WRITEOFF_SUCCESS,
    // 验收入库单{0}过账成功
    RETURN_CODE_INSPEC_INPUT_POST_SUCCESS,
    // 采购入库单 {0} 保存成功
    RETURN_CODE_PURCHASE_INPUT_SAVE_SUCCESS,
    // 采购入库单 {0} 提交成功
    RETURN_CODE_PURCHASE_INPUT_SUBMIT_SUCCESS,
    // 采购入库单 {0} 删除成功
    RETURN_CODE_PURCHASE_INPUT_DELETE_SUCCESS,
    // 采购入库单 {0} 冲销成功
    RETURN_CODE_PURCHASE_INPUT_WRITEOFF_SUCCESS,
    // 采购入库单 {0} 过账成功
    RETURN_CODE_PURCHASE_INPUT_POST_SUCCESS,
    // 其他入库单{0}保存成功
    RETURN_CODE_OTHER_INPUT_SAVE_SUCCESS,
    // 工器具入库单{0}保存成功
    RETURN_CODE_TOOLS_INPUT_SAVE_SUCCESS,
    // 工器具数据{0}保存成功
    RETURN_CODE_TOOLS_MASTER_DATA_SAVE_SUCCESS,
    // 废旧物资入库单{0}导入成功
    RETURN_CODE_WASTER_MATERIALS_INPUT_SAVE_SUCCESS,
    // 其他入库单{0}提交成功
    RETURN_CODE_OTHER_INPUT_SUBMIT_SUCCESS,
    // 其他入库单{0}删除成功
    RETURN_CODE_OTHER_INPUT_DELETE_SUCCESS,
    // 其他入库单{0}冲销成功
    RETURN_CODE_OTHER_INPUT_WRITEOFF_SUCCESS,
    // 其他入库单{0}过账成功
    RETURN_CODE_OTHER_INPUT_POST_SUCCESS,
    // 零价值入库单 {0} 保存成功
    RETURN_CODE_WORTHLESS_INPUT_SAVE_SUCCESS,
    // 零价值入库单 {0} 提交成功
    RETURN_CODE_WORTHLESS_INPUT_SUBMIT_SUCCESS,
    // 零价值入库单 {0} 删除成功
    RETURN_CODE_WORTHLESS_INPUT_DELETE_SUCCESS,
    // 零价值入库单 {0} 冲销成功
    RETURN_CODE_WORTHLESS_INPUT_WRITEOFF_SUCCESS,
    // 零价值入库单 {0} 过账成功
    RETURN_CODE_WORTHLESS_INPUT_POST_SUCCESS,
    // 生产入库单{0}保存成功
    RETURN_CODE_PRODUCTION_INPUT_SAVE_SUCCESS,
    // 生产入库单{0}提交成功
    RETURN_CODE_PRODUCTION_INPUT_SUBMIT_SUCCESS,
    // 生产入库单{0}删除成功
    RETURN_CODE_PRODUCTION_INPUT_DELETE_SUCCESS,
    // 生产入库单{0}冲销成功
    RETURN_CODE_PRODUCTION_INPUT_WRITEOFF_SUCCESS,
    // 生产入库单{0}过账成功
    RETURN_CODE_PRODUCTION_INPUT_POST_SUCCESS,
    // 临时入库单{0}提交成功
    RETURN_CODE_TEMP_INPUT_SUBMIT_SUCCESS,
    // 临时入库单{0}保存成功
    RETURN_CODE_TEMP_INPUT_SAVE_SUCCESS,
    // 临时入库单{0}删除成功
    RETURN_CODE_TEMP_INPUT_DELETE_SUCCESS,
    // 临时入库单{0}冲销成功
    RETURN_CODE_TEMP_INPUT_WRITEOFF_SUCCESS,
    // 临时入库单{0}过账成功
    RETURN_CODE_TEMP_INPUT_POST_SUCCESS,
    // 临时入库单{0}核销成功
    RETURN_CODE_TEMP_INPUT_DEPT_OFFSET_SUCCESS,
    // 临时入库单{0}撤销成功
    RETURN_CODE_TEMP_INPUT_REVOKE_SUCCESS,
    // 工具归还单{0}保存成功
    RETURN_CODE_BORROW_INPUT_SAVE_SUCCESS,
    // 工具归还单{0}提交成功
    RETURN_CODE_BORROW_INPUT_SUBMIT_SUCCESS,
    // 工具归还单{0}过账成功
    RETURN_CODE_BORROW_INPUT_POST_SUCCESS,
    // 工具归还单{0}删除成功
    RETURN_CODE_BORROW_INPUT_DELETE_SUCCESS,
    // 盘点单{0}保存成功
    RETURN_CODE_STOCKTAKING_SAVE_SUCCESS,
    // 盘点单{0}提交成功
    RETURN_CODE_STOCKTAKING_SUBMIT_SUCCESS,
    // 盘点凭证{0}保存成功
    RETURN_CODE_STOCKTAKING_DOC_SAVE_SUCCESS,
    // 盘点凭证{0}提交成功
    RETURN_CODE_STOCKTAKING_DOC_SUBMIT_SUCCESS,
    // 盘点报告{0}保存成功
    RETURN_CODE_STOCKTAKING_REPORT_SAVE_SUCCESS,
    // 盘点报告{0}提交成功
    RETURN_CODE_STOCKTAKING_REPORT_SUBMIT_SUCCESS,
    // 盘点单{0}删除成功
    RETURN_CODE_STOCKTAKING_DELETE_SUCCESS,
    // 仓位{0}盘点成功
    RETURN_CODE_STOCKTAKING_BIN_COUNT_SUCCESS,
    // 盘点仓位认领成功
    RETURN_CODE_STOCKTAKING_BIN_GET_SUCCESS,
    //盘点按仓位盘点才可添加物料
    RETURN_CODE_STOCKTAKING_NO_ADD,
    //盘点抽盘物料来源非被首盘列表
    RETURN_CODE_STOCKTAKING_MAT_ERROR,
    //盘点抽盘物料数量小于原单据物料30%
    RETURN_CODE_STOCKTAKING_MAT_COUNT_ERROR,
    // 盘点人不能为空
    RETURN_CODE_STOCKTAKING_STOCKTAKING_USER_CANNOT_BE_NULL,
    // 送货通知单{0}保存成功
    RETURN_CODE_DELIVERY_SAVE_SUCCESS,
    // 送货通知单{0}提交成功
    RETURN_CODE_DELIVERY_SUBMIT_SUCCESS,
    // 送货通知单{0}删除成功
    RETURN_CODE_DELIVERY_DELETE_SUCCESS,
    // 送货通知单{0}完成送货
    RETURN_CODE_DELIVERY_FINISH_SUCCESS,
    // 申请单{0}保存成功
    RETURN_CODE_APPLY_SAVE_SUCCESS,
    // 申请单{0}提交成功
    RETURN_CODE_APPLY_SUBMIT_SUCCESS,
    // 虚拟出入库申请单{0}保存成功
    RETURN_CODE_VIRTUAL_APPLY_SAVE_SUCCESS,
    // 虚拟出入库申请单{0}提交成功
    RETURN_CODE_VIRTUAL_APPLY_SUBMIT_SUCCESS,
    // 申请单{0}删除成功
    RETURN_CODE_APPLY_DELETE_SUCCESS,
    // 登记单{0}保存成功
    RETURN_CODE_REGISTER_SAVE_SUCCESS,
    // 登记单{0}提交成功
    RETURN_CODE_REGISTER_SUBMIT_SUCCESS,
    // 登记单{0}过账成功
    RETURN_CODE_REGISTER_POST_SUCCESS,
    // 登记单{0}删除成功
    RETURN_CODE_REGISTER_DELETE_SUCCESS,
    // 登记单{0}冲销成功
    RETURN_CODE_REGISTER_WRITEOFF_SUCCESS,
    // 不符合项单{0}保存成功
    RETURN_CODE_INCONFORMITY_SAVE_SUCCESS,
    // 不符合项单{0}提交成功
    RETURN_CODE_INCONFORMITY_SUBMIT_SUCCESS,
    // 不符合项单{0}冲销成功
    RETURN_CODE_INCONFORMITY_WRITEOFF_SUCCESS,
    // 寿期检定单{0}保存成功
    RETURN_CODE_LIFETIME_SAVE_SUCCESS,
    // 寿期检定单{0}提交成功
    RETURN_CODE_LIFETIME_SUBMIT_SUCCESS,
    // 寿期检定单{0}删除成功
    RETURN_CODE_LIFETIME_DELETE_SUCCESS,
    // 维保单{0}保存成功
    RETURN_CODE_MAINTAIN_SAVE_SUCCESS,
    // 维保单{0}提交成功
    RETURN_CODE_MAINTAIN_SUBMIT_SUCCESS,
    // 维保单{0}删除成功
    RETURN_CODE_MAINTAIN_DELETE_SUCCESS,
    // 工具{0}库存已被占用
    RETURN_CODE_ITEM_OCCUPY_QTY,
    // 无新增配置数据
    RETURN_CODE_NO_CONF,
    // 无效的查询物料
    RETURN_CODE_INVALID_MAT_CODE,
    /** 存在基于出库单创建的退库单，出库单不能冲销 */
    RETURN_CODE_OUTPUT_CAN_NOT_WRITE_OFF_BY_HAS_REFER_RETURN_RECEIPT,
    // {0}仓位冻结
    RETURN_CODE_BIN_FREEZING,
    // {0}库存地点冻结
    RETURN_CODE_LOCATION_FREEZING,
    // 行项目{0}库存地点{1}出库冻结
    RETURN_CODE_LOCATION_FREEZING_OUTPUT_ITEM,
    // 行项目{0}库存地点{1}入库冻结
    RETURN_CODE_LOCATION_FREEZING_INPUT_ITEM,
    // {0}托盘冻结
    RETURN_CODE_CELL_FREEZING,
    // {0}物料冻结
    RETURN_CODE_MATERIAL_FREEZING,
    // 存储单元{0}不能放在多个仓位{1}上
    RETURN_CODE_MODIFY_STOCK_CELL_BIN,
    // 目标存储类型未激活存储单元
    RETURN_CODE_TYPE_TARGET_CELL_ENABLE,
    // 目标存储单元不能为空
    RETURN_CODE_TARGET_CELL_CODE_NOT_EMPTY,
    // 源存储单元与目标存储单元不相同
    RETURN_CODE_SOURCE_CELL_CODE_NOT_SAME_TARGET_CELL_CODE,
    // 行项目中存在源存储单元与目标存储单元相同的数据
    RETURN_CODE_SOURCE_CELL_CODE_SAME_TARGET_CELL_CODE_DATA,
    /** 单据{0}的行项目{1}不支持操作特殊库存标识{2} */
    RETURN_CODE_RECEIPT_SPEC_STOCK_UNSUPPORTED,
    /** 单据{0}的行项目{1}不支持操作特殊库存标识代码{2} */
    RETURN_CODE_RECEIPT_SPEC_STOCK_CODE_UNSUPPORTED,
    // 盘点人不能为空
    RETURN_CODE_STOCK_TAKE_USER_NOT_EMPTY,
    /** 特殊库存代码格式错误 */
    RETURN_CODE_SPEC_STOCK_CODE_FORMAT_EXCEPTION,
    // 叉车司机长度不能超出10位
    RETURN_CODE_TASK_FORKLIFT_DRIVER_CODE_LENGTH,
    // 理货员长度不能超出10位
    RETURN_CODE_TASK_TALLYMAN_CODE_LENGTH,
    /** 经办组必须选择所在公司下的岗位 */
    RETURN_CODE_ACTOR_GROUP_CORP_POST,
    // 叉车司机保存失败
    RETURN_CODE_TASK_FORKLIFT_DRIVER_SAVE_FAILURE,
    // 理货员保存失败
    RETURN_CODE_TASK_TALLYMAN_SAVE_FAILURE,
    // 仓位库存数量不能为空
    RETURN_CODE_STOCK_BIN_QTY_NOT_EMPTY,
    // 启用仓位及存储单元入库顺序不能为同时入库上架
    RETURN_CODE_BIN_AND_CELL_ENABLE_TASK_INPUT_CANNOT_SAME,
    // 启用仓位及存储单元出库顺序不能为同时出库下架
    RETURN_CODE_BIN_AND_CELL_ENABLE_TASK_OUTPUT_CANNOT_SAME,
    // 单据不是已计数状态，不能全部重盘
    RETURN_CODE_APPROVAL_NOT_ALL_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目是审批状态，不能差异重盘
    RETURN_CODE_APPROVAL_NOT_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目是仓位差异处理状态，不能差异重盘
    RETURN_CODE_BIN_NOT_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目是批次差异处理状态，不能差异重盘
    RETURN_CODE_BATCH_NOT_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目是审批状态，不能仓位差异处理
    RETURN_CODE_APPROVAL_NOT_BIN_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目已经是仓位处理状态，不能再次仓位差异处理
    RETURN_CODE_BIN_NOT_BIN_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目是批次处理状态，不能仓位差异处理
    RETURN_CODE_BATCH_NOT_BIN_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目是审批状态，不能批次差异处理
    RETURN_CODE_APPROVAL_NOT_BATCH_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目是计数状态，不能批次差异处理
    RETURN_CODE_COUNT_NOT_BATCH_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目是批次处理状态，不能再次批次差异处理
    RETURN_CODE_BATCH_NOT_BATCH_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目已经是计数状态，不能再次计数
    RETURN_CODE_COUNT_NOT_COUNT_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目已经重新计数，不能差异重盘
    RETURN_CODE_AGAIN_COUNT_NOT_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目已经重新计数，不能仓位差异处理
    RETURN_CODE_AGAIN_COUNT_NOT_BIN_DIFFERENCE_STOCK_TAKE,
    // 存储类型{0}仓位{1}批次{2}库存数量{3}实际数量{4}的行项目已经重新计数，不能批次差异处理
    RETURN_CODE_AGAIN_COUNT_NOT_BATCH_DIFFERENCE_STOCK_TAKE,
    // 角色编码不能超过10位
    RETURN_CODE_ROLE_CODE_LENGTH_TOO_LONG,
    // 角色描述不能超过100位
    RETURN_CODE_ROLE_NAME_LENGTH_TOO_LONG,
    /** 单据类型错误 */
    RETURN_CODE_RECEIPT_TYPE_FALSE,
    // {0}工厂erp批次未设置
    RETURN_CODE_BATCH_ERP_FTY_UN_SET,
    // 公司{0}已分配用户，不能删除
    RETURN_CODE_DELETE_FAIL_DUE_TO_USED_USER,
    // 公司{0}已分配经办组，不能删除
    RETURN_CODE_DELETE_FAIL_DUE_TO_USED_ACTOR_GROUP,
    // 公司{0}已分配岗位，不能删除
    RETURN_CODE_DELETE_FAIL_DUE_TO_USED_ACTOR_POST,
    // {0}被占用不能删除
    RETURN_CODE_DELETE_FAIL_IN_USE,
    // 所选仓位下不存在特殊库存{0}
    RETURN_CODE_BIN_NOT_EXIST_SPEC_STOCK,
    // 所选仓库{0}不启用存储单元，所以存储类型中的存储单元选项不允许激活
    RETURN_CODE_WH_CELL_UN_ENABLE,
    // 所选仓库{0}启用存储单元，所以存储类型中的存储单元选项必须激活
    RETURN_CODE_WH_CELL_ENABLE,
    // 紧急批次号生成失败
    RETURN_CODE_HASTE_BATCH_GENERATE_FAILURE,
    // 单据{0}不存在
    RETURN_CODE_TEMP_RECEIPT_NOT_EXISTS,
    // 核销 物料不一致
    RETURN_CODE_TEMP_DEBT_OFFSET_MAT_CODE_NOT_SAME,
    // 核销 工厂不一致
    RETURN_CODE_TEMP_DEBT_OFFSET_FTY_CODE_NOT_SAME,
    // 核销 erp批次不一致
    RETURN_CODE_TEMP_DEBT_OFFSET_BATCH_ERP_NOT_SAME,
    // 核销 特殊库标识不一致
    RETURN_CODE_TEMP_DEBT_OFFSET_SPEC_STOCK_NOT_SAME,
    // 核销 特殊库存代码不一致
    RETURN_CODE_TEMP_DEBT_OFFSET_SPEC_STOCK_CODE_NOT_SAME,
    // 核销 特殊库存描述不一致
    RETURN_CODE_TEMP_DEBT_OFFSET_SPEC_STOCK_NAME_NOT_SAME,
    // 物料凭证{0}不匹配
    RETURN_CODE_WRITE_OFF_MAT_DOC_NOT_SAME,
    // 存储类型{0}已被使用不能修改存储单元激活状态
    RETURN_CODE_STORAGE_TYPE_CELL_ENABLE_CANNOT_EDIT_DUE_TO_USED,
    // 单据流保存失败
    RETURN_CODE_REL_RECEIPT_RID_GENERATE_FAILURE,
    // 单据{0}行{1}对应的前续单据行必须唯一
    RETURN_CODE_PRE_RECEIPT_RID_MUST_BE_UNIQUE,
    // 该仓库下的存储类型已经启用存储单元，所以仓库必须启用存储单元
    RETURN_CODE_WH_CELL_MUST_ENABLED,
    /** 存储单元{0}所在仓库{1}与当前仓库{2}不一致 */
    RETURN_CODE_CELL_WAREHOUSE_NOT_SAME_THE_CURRENT_WAREHOUSE,
    /** 行项目{0}选配的ERP批次信息不一致 */
    RETURN_CODE_BATCH_ERP_NOT_IDENTICAL_EXCEPTION,
    /** 拣货区已存在 */
    RETURN_CODE_PICKING_AREA_CODE_EXIST,
    /** 波次已存在 */
    RETURN_CODE_DIC_WAVE_CODE_EXIST,
    /** 波次{0}的仓库{1}存储类型{2} 与波次{3}的仓库{4}存储类型{5} 存在波次时间冲突 */
    RETURN_CODE_DIC_WAVE_TIME_EXIST,
    /** 物料类型不合法 */
    RETURN_CODE_VALIDITY_TARGET_MAT_TYPE_FAIL,
    /** 物料类型不存在 */
    RETURN_CODE_MAT_TYPE_NOT_EXIST,
    /** 单位不合法 */
    RETURN_CODE_VALIDITY_TARGET_UNIT_FAIL,
    /** 物料组不存在 */
    RETURN_CODE_MAT_GROUP_NOT_EXIST,
    /** 物料组不合法 */
    RETURN_CODE_VALIDITY_TARGET_MAT_GROUP_FAIL,
    /** 公告不存在 */
    RETURN_CODE_NOTICE_NOT_EXIST,
    /** 账期不存在 */
    RETURN_CODE_ACCOUNT_NOT_EXIST,
    /** 设置账期失败 */
    RETURN_CODE_ACCOUNT_SET_FAIL,
    /** 物料不合法 */
    RETURN_CODE_VALIDITY_MAT_FAIL,
    /** 元数据 DTO属性不存在 */
    RETURN_CODE_METADATA_DTO_ATTRIBUTE_DOES_NOT_EXIST,
    /** 元数据模型不存在 */
    METADATA_MODULE_NAME_DOES_NOT_EXIST,
    /** 核销同时作业模式的行项目，配货信息必须选择存储类型和仓位 */
    RETURN_CODE_HASTE_DEBT_OFFSET_MISSING_STORAGE_TYPE_OR_BIN_CODE_PARAMETER_EXCEPTION,
    /** 核销选配的物料erp批次与单据提交保存时选配的物料erp批次不一致 */
    RETURN_CODE_HASTE_DEBT_OFFSET_BATCH_ERP_DIFFERENT_EXCEPTION,
    /** 核销选配的物料erp批次出库总数与单据提交保存时选配的物料erp批次出库总数不一致 */
    RETURN_CODE_HASTE_DEBT_OFFSET_BATCH_ERP_TOTAL_QTY_DIFFERENT_EXCEPTION,
    /** 未找到物料仓库数据 中 特殊存储类型标识 */
    RETURN_CODE_TASK_STRATEGY_SPECIAL_STORAGE_TYPE_IDENTIFIER_NOT_EXIST,
    /** 未找到仓储作业申请明细 中 存储类型集合 */
    RETURN_CODE_TASK_STRATEGY_SPECIAL_STORAGE_TYPE_COLLECTION_NOT_EXIST,
    /** 上架策略不存在 */
    RETURN_CODE_TASK_STRATEGY_SPECIAL_NOT_EXIST,
    /** 当前指令{0}下的存储单元不存在 */
    RETURN_CODE_INSTRUCTIONS_NOT_EXIST,
    /** 当前指令{0}正在作业，不能往前操作 */
    RETURN_CODE_INSTRUCTIONS_WORK,
    /** 当前指令{0}已经在缓存区，不能往前操作 */
    RETURN_CODE_INSTRUCTIONS_IN_CACHE,
    /** 当前指令{0}在非缓存区第一位，不能往前操作 */
    RETURN_CODE_INSTRUCTIONS_FIRST_PLACE,
    /** 仓库号{0}存储类型{1}仓位{2}没有配置对应的仓位 */
    RETURN_CODE_NOT_CONFIGURATION_BIN_CODE,
    /** 该类型业务未配置审批流 */
    RETURN_CODE_NO_PROCESS,
    /** 仓库编码{0}，物料编码:{1}对应的物料仓库对象(DicMaterialWh)为空 */
    RETURN_CODE_EMPTY_DIC_MATERIAL_WH,
    /** 仓库编码{0}，物料编码:{1}对应的仓储作业申请明细对象为空 */
    RETURN_CODE_EMPTY_TASK_STRATEGY,
    /** 仓库编码{0}，物料编码:{1}对应的仓储类型对象(DicWhStorageType)为空 */
    RETURN_CODE_EMPTY_DIC_WH_STORAGE_TYPE,
    /** 仓库编码{0}，存储类型编码:{1}对应的下架策略为空 */
    RETURN_CODE_EMPTY_STRATEGY_OUTPUT,
    /** 已下架数量{0}，大于待下架数量{1} */
    RETURN_CODE_TASKQTY_GT_QTY,
    /** 目标仓位{0}下的存储区{1}没有配置对应的工作区内的设备 */
    RETURN_CODE_BIN_CODE_SECTION_NOT_CONFIGURATION_EQUIPMENT,
    /** 目标仓位{0}下的拣货区{1}没有配置对应的工作区内的设备 */
    RETURN_CODE_BIN_CODE_PICK_NOT_CONFIGURATION_EQUIPMENT,
    /** 目标仓位{0}没有配置在工作区{1}下 */
    RETURN_CODE_BIN_CODE_NOT_CONFIG_WORK_AREA,
    /** 仓库编码：{0}，存储类型：{1}，物料编码：{2}物料仓库存储类型对象为空(dic_material_wh_storage_type)为空 */
    RETURN_CODE_EMPTY_DIC_MATERIAL_WH_STORAGE_TYPE,
    /** 立体仓库号：{0}，接口类型：{1}，接口提供方：{2}已经存在 */
    RETURN_CODE_WH_CODE_TYPE_PROVIDER_EXIST,
    /** 仓库编码：{0}，存储类型：{1}，上下架标识：{2}，的策略已存在 */
    RETURN_CODE_DIC_STOCK_TASK_STRATEGY_ALREADY_EXIST,
    /** 存储区{0}没有配置对应的工作区 */
    RETURN_CODE_SECTION_NOT_CONFIGURATION_WORKAREA,
    /** 拣货区{0}没有配置对应的工作区 */
    RETURN_CODE_PICK_NOT_CONFIGURATION_WORKAREA,
    /** 工作区{0}没有配置上架设备 */
    RETURN_CODE_WORKAREA_NOT_CONFIGURATION_SECTION_EQUIPMENT,
    /** 工作区{0}没有配置下架设备 */
    RETURN_CODE_WORKAREA_NOT_CONFIGURATION_PICK_EQUIPMENT,
    /** 存储区{0}没有启用搬运 */
    RETURN_CODE_SECTION_NOT_USED_AGV,
    /** 拣货区区{0}没有启用搬运 */
    RETURN_CODE_PICKING_NOT_USED_AGV,
    /** 仓库编码或门编码不能为空！ */
    RETURN_CODE_EMPTY_WH_CODE_AND_GATE_CODE,
    /** 仓库编码：{0}，门编码：{1}，已存在 */
    RETURN_CODE_DIC_GATE_ALREADY_EXIST,
    /** 由于该门和拣货区存在关联关系所以不能删除该门，仓库编码:{0}，存储类型编码:{1}，拣货区编码:{2}，门编码:{3} */
    RETURN_CODE_REL_GATE_PICKINGAREA_ALREADY_EXIST,
    // 文件格式不正确
    RETURN_CODE_FILE_FORMAT_IS_INCORRECT,
    //存在运行中的流程实例
    RETURN_CODE_EXIST_RUNNING_PROCESS_INSTANCE,
    /** 不允许上传空文件  */
    RETURN_CODE_DONT_ALLOW_UPLOAD_EMPTY_FILE,
    // 语言不存在
    RETURN_CODE_LANGUAGE_NOT_EXIST,
    // 板块不存在
    RETURN_CODE_BOARD_NOT_EXIST,
    // 公司不存在
    RETURN_CODE_CORP_NOT_EXIST,
    // 工厂不存在
    RETURN_CODE_FACTORY_NOT_EXIST,
    // 库存地点不存在
    RETURN_CODE_STOCK_LOCATION_NOT_EXIST,
    // 仓库{0}不存在
    RETURN_CODE_WAREHOUSE_NOT_EXIST,
    // 存储类型不存在
    RETURN_CODE_STORAGE_TYPE_NOT_EXIST,
    // 存储区不存在
    RETURN_CODE_STORAGE_SECTION_NOT_EXIST,
    // 仓位不存在
    RETURN_CODE_STORAGE_BIN_NOT_EXIST,
    // 物料不存在
    RETURN_CODE_MATERIAL_NOT_EXIST,
    // 物料{0}不存在
    RETURN_CODE_MATERIAL_ALERT_NOT_EXIST,
    // 物料【{0}】净重为空
    RETURN_CODE_MATERIAL_NET_WEIGHT_EMPTY,
    // 移动类型{0}不存在
    RETURN_CODE_MOVE_TYPE_NOT_EXIST,
    // 单位{0}不存在
    RETURN_CODE_UNIT_NOT_EXIST,
    // 工厂{0}中物料{1}单位{2}与{3}的换算关系不存在
    RETURN_CODE_REL_UNIT_NOT_EXIST,
    // 物料的单位不存在
    RETURN_CODE_MATERIAL_UNIT_NOT_EXIST,
    // 系统配置错误
    RETURN_CODE_SYSTEM_CONFIG_ERROR,
    // 工厂已经存在
    RETURN_CODE_FACTORY_ALREADY_EXIST,
    // 库存地点已经存在
    RETURN_CODE_STOCK_LOCATION_ALREADY_EXIST,
    // 移动类型{0}已存在
    RETURN_CODE_MOVE_TYPE_ALREADY_EXIST,
    // 单位{0}已存在
    RETURN_CODE_UNIT_ALREADY_EXIST,
    // 工厂{0}中物料{1}单位{2}与{3}的换算关系已存在
    RETURN_CODE_REL_UNIT_ALREADY_EXIST,
    // 该存储单元已存在
    RETURN_CODE_SERIAL_CELL_EXIST,
    // 存储区已存在
    RETURN_CODE_STORAGE_SECTION_EXIST,
    // 编码已存在
    RETURN_CODE_CODE_EXIST,
    // 仓库{0}存储类型{1}不存在
    RETURN_CODE_STORAGE_WH_TYPE_NOT_EXIST,
    // 仓库{0}存储类型{1}仓位{2}不存在
    RETURN_CODE_STORAGE_WH_TYPE_BIN_NOT_EXIST,
    // 算术异常：ArithmeticException
    RETURN_CODE_ARITHMETIC_EXCEPTION,
    // 空指针异常：NullPointerException
    RETURN_CODE_NULL_POINTER_EXCEPTION,
    // 类型强制转换异常：ClassCastException
    RETURN_CODE_CLASS_CAST_EXCEPTION,
    // 数组负下标异常：NegativeArrayException
    RETURN_CODE_NEGATIVE_ARRAY_SIZE_EXCEPTION,
    // 数组下标越界异常：ArrayIndexOutOfBoundsException
    RETURN_CODE_ARRAY_INDEX_OUT_OF_BOUNDS_EXCEPTION,
    // 违背安全原则异常：SecturityException
    RETURN_CODE_SECURITY_EXCEPTION,
    // 文件已结束异常：EOFException
    RETURN_CODE_EOF_EXCEPTION,
    // 文件未找到异常：FileNotFoundException
    RETURN_CODE_FILE_NOT_FOUND_EXCEPTION,
    // 字符串转换为数字异常：NumberFormatException
    RETURN_CODE_NUMBER_FORMAT_EXCEPTION,
    // 操作数据库异常：SQLException
    RETURN_CODE_SQL_EXCEPTION,
    // 输入输出异常：IOException
    RETURN_CODE_IO_EXCEPTION,
    // 方法未找到异常：NoSuchMethodException
    RETURN_CODE_NO_SUCH_METHOD_EXCEPTION,
    /** 缺少库存地点{0}操作权限 */
    RETURN_CODE_NO_LOCATION_AUTHORITY_EXCEPTION,
    /** 采购订单{0}已创建内向交货单，不能通过当前业务类型处理 */
    RETURN_CODE_PURCHASE_CREATE_INWARD_EXCEPTION,
    /** 销售订单{0}已创建外向交货单，不能通过当前业务类型处理 */
    RETURN_CODE_SALE_CREATE_OUTWARD_EXCEPTION,
    /** 外向交货单{0}对应的销售订单已过账，不能通过当前业务类型处理 */
    RETURN_CODE_OUTWARD_REFER_SALE_POST_EXCEPTION,
    // 必填岗位不全
    RETURN_CODE_REQUIRED_POST_NOT_ENOUGH_EXCEPTION,
    // 此外向交货单已创建出库单
    RETURN_CODE_OUTPUT_BY_OUTWARD_ALREADY_EXIST_EXCEPTION,
    // 外向交货单已在系统中创建过退库单
    RETURN_CODE_OUTWARD_CREATE_RETURN_INS_EXCEPTION,
    // 仓库物料已存在
    RETURN_CODE_WH_MAT_EXISTED,
    // 物料正在使用无法删除
    RETURN_CODE_THIS_MATERIAL_HAS_INCOMPLETE_DOCUMENTS_PLEASE_CONFIRM,
    // 物料占用库存无法删除
    RETURN_CODE_THIS_MATERIAL_HAS_STOCK_BIN_PLEASE_CONFIRM,
    // {0}板块{1}公司{2}年{3}月账期已存在
    RETURN_CODE_ACCOUNT_PERIOD_EXIST,
    // {0}板块{1}公司{2}年{3}月账期管理时间段冲突
    ERROR_CODE_ACCOUNT_PERIOD_TIME_BEGIN_END_EXIST,
    // 无效的行项目{0}
    RETURN_CODE_INVALID_RECEIPT_ITEM,
    // {0}行项目不可核销
    RETURN_CODE_ITEM_CAN_NOT_DEBT_OFFSET,
    // {0}行项目未绑定采购订单
    RETURN_CODE_ITEM_NOT_BIND_PURCHASE_ORDER,
    // 不能修改行项目核销引用的前序单据信息，已生效的引用{0}-{1}
    RETURN_CODE_ITEM_DEBT_OFFSET_REFER_INFO_CHANGED_EXCEPTION,
    // {}公司的帐期管理时间范围有重合
    RETURN_CODE_ACCOUNT_PERIOD_TIME_CONFLICT,
    /** {0}仓库未激活存储单元无法组盘 */
    RETURN_CODE_WHAREHOUSE_CELL_UNABLE_CAN_NOT_SETUP,
    /** {0}存储单元已被{1}作业单组盘 */
    RETURN_CODE_CELL_HAS_SETUP_USED,
    /** 作业单状态无法完成指令 */
    RETURN_CODE_TASK_STATUS_CAN_NOT_FINISH_INSTRUCTION,
    /** 作业单生成指令无法删除 */
    RETURN_CODE_TASK_HAS_INSTRUCTION_CAN_NOT_DELETE,
    /** 作业单状态无法删除 */
    RETURN_CODE_TASK_STASTUS_CAN_NOT_DELETE,
    /** 用户已冻结 */
    RETURN_CODE_USER_FREEZEN,
    /** 用户已过期 **/
    RETURN_CODE_USER_EXPIRED,
    // 批次库存数量不足,
    RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_UNRESTRICTED, RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_ON_THE_WAY,

    RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_QUALITY_INSPECTION, RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_FREEZE,

    RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_HASTE, RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_TEMP, RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_BOOK,

    RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_PHY, RETURN_CODE_STOCK_BATCH_QTY_NOT_ENOUGH_USABLE,
    // 仓位库存数量不足,
    RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_UNRESTRICTED, RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_ON_THE_WAY,

    RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_QUALITY_INSPECTION, RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_FREEZE, RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_HASTE,
    
    RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_TEMP, RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_BOOK, RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_PHY,
    
    RETURN_CODE_STOCK_BIN_QTY_NOT_ENOUGH_USABLE, RETURN_CODE_STOCK_BATCH_QTY_INSUFFICIENT,
    // SAP同步失败
    RETURN_CODE_SAP_SYNC_EXCEPTION,
    // 此单据已同步SAP不能进行删除操作
    RETURN_CODE_SAP_SYNC_SUCCESS_NO_DELETE_AUTHORITY,
    // SAP连接超时
    RETURN_CODE_SAP_CONNECTION_TIMEOUT_EXCEPTION,
    // SAP冲销失败
    RETURN_CODE_SAP_WRITE_OFF_EXCEPTION,
    // 前序单据是销售订单只能进行整单冲销
    RETURN_CODE_SAP_WRITE_OFF_REFER_SALE_ONLY_RECEIPT_EXCEPTION,
    // 前序单据是外向交货单只能进行整单冲销
    RETURN_CODE_SAP_WRITE_OFF_REFER_OUTWARD_ONLY_RECEIPT_EXCEPTION,
    // 行项目已冲销不能再次冲销
    RETURN_CODE_SAP_WRITE_OFF_ITEM_HAS_WRITE_OFF_CANNOT_AGAIN_EXCEPTION,
    // 行项目未生成物料凭证
    RETURN_CODE_SAP_WRITE_OFF_ITEM_NO_MAT_DOC_CODE_EXCEPTION,
    /** 未同步单据的冲销必须整单提交 */
    RETURN_CODE_SAP_UNSYNC_ITEM_PARTIALLY_WRITE_OFF_EXCEPTION,
    // 获取采购订单接口调用失败
    RETURN_CODE_SAP_DOWNLOAD_PURCHASE_EXCEPTION,
    // 获取内向交货单接口调用失败
    RETURN_CODE_SAP_DOWNLOAD_DELIVERY_INWARD_EXCEPTION,
    // 标识不在可用区间
    RETURN_CODE_IOT_MARK_NOT_BETWEEN_TWO_NUM,
    // 标识已满
    RETURN_CODE_IOT_MARK_IS_FULL,
    // 标识正被使用
    RETURN_CODE_IOT_MARK_IS_USED,
    // 设备编号已存在
    RETURN_CODE_IOT_DEVICE_CODE_EXIST,
    // 立库仓位已存在
    RETURN_CODE_WCS_WH_STORAGE_BIN_CODE_EXIST,
    // 立库已存在
    RETURN_CODE_WCS_WH_CODE_EXIST,
    // 该仓位已配置
    RETURN_CODE_WCS_WH_BIN_CONF_EXIST,
    // 该仓位已配置立体仓位，无法修改
    RETURN_CODE_WH_BIN_WCS_WH_BIN_CONF_EXIST,
    // 开始编号{0}不能大于截止编号{1}
    RETURN_CODE_START_NUMBER_NOT_GREATER_THAN_END_NUMBER,
    // 序列号组{0}被参数规则引用，不能删除
    RETURN_CODE_SEGMENT_GROUPING_IS_USED,
    // 规则参数{0}已存在
    RETURN_CODE_SEGMENT_RULE_CODE_IS_EXIST,
    // 序列号{0}已存在
    RETURN_CODE_SERIAL_NUMBER_IS_EXIST,
    // 号码组{0}的起始和截止号码在别的号码组被使用
    RETURN_CODE_START_NUMBER_AND_END_NUMBER_IS_EXIST,
    // 打印成功
    RETURN_CODE_PRINT_SUCCESS,
    // 打印参数缺失
    RETURN_CODE_PRINT_PARAMETER_LOST_EXCEPTION,
    // 打印机IP地址为空
    RETURN_CODE_PRINT_IP_LOST_EXCEPTION,
    // 打印机端口号不能为空
    RETURN_CODE_PRINT_PORT_LOST_EXCEPTION,
    // 打印份数不能为空
    RETURN_CODE_PRINT_NUM_LOST_EXCEPTION,
    // 打印物料类型不能为空
    RETURN_CODE_PRINT_MAT_TYPE_EXCEPTION,
    // 验收单已完成不可删除
    RETURN_CODE_INSPECT_NOTICE_COMPLETED,
    // 验收单已验收不可删除
    RETURN_CODE_INSPECT_NOTICE_INSPECTED,
    // 验收单行项目{0}已打印
    RETURN_CODE_INSPECT_NOTICE_PRINTED,
    // 退库单配货信息{0}已打印
    RETURN_CODE_RETURN_RECEIPT_PRINTED,
    // 请输入查询条件
    RETURN_CODE_PLEASE_ENTER_QUERY_CRITERIA, RETURN_REQ_IS_DELETE_NOT_SUBMIT, RETURN_REQ_IS_COMPLETE_NOT_SUBMIT,
    // 行项目{0}可操作数量大于待验收数量
    RETURN_CODE_ARRIVAL_MAX_ORDER_QTR,
    // 行项目{0}订单数量为零
    RETURN_CODE_ORDER_QTR_ZERO,
    // 行项目{0}可验收数量为零
    RETURN_CODE_ARRIVAL_QTR_ZERO,
    // 行项目{0}合格数量为零
    RETURN_CODE_QTR_ZERO,
    // 行项目{0}出库数量为零
    RETURN_CODE_OUTPUT_QTY_ZERO,
    // 行项目{0}退库数量为零
    RETURN_CODE_RETURN_QTY_ZERO,
    // 行项目{0}操作数量为0
    RETURN_CODE_OPERATION_QTY_ZERO,
    // 本次发运数量总和不等于合同数量,请输入正确数量
    RETURN_CODE_OPERATION_QTY_NOT_EQUAL,
    // 行项目{0}已超过送货数量
    RETURN_CODE_EXCEED_DELIVERY_QTY,
    // 行项目{0}已超过可出库数量
    RETURN_CODE_EXCEED_OUTPUT_QTY,
    // 行项目{0}运单行{0}合格数量加不合格数量加未到货数量不等于本次到货数量
    RETURN_CODE_ARRIVAL_QTR_UN_ENUOUGH,
    // 计划到货日期为空
    RETURN_CODE_PLANNED_ARRIVAL_DATE_IS_EMPTY,
    // 码盘单请求类型为空
    RETURN_CODE_CODE_DISK_TYPE_EMPTY,
    // 验收单行项目{0}不存在
    RETURN_CODE_INSPECT_NOTICE_ITEM_EMPTY_RID,
    // 验收单行项目不存在
    RETURN_CODE_INSPECT_NOTICE_ITEM_EMPTY,
    // 验收单行不存在
    RETURN_CODE_INSPECT_NOTICE_HEAD_EMPTY,
    // RFID{0}已码盘
    RETURN_CODE_RFID_CODED_DISK,
    // 物料已过门不能删除码盘单
    RETURN_CODE_MAT_PASSED_NOT_DELETE,
    // 行项目实际入库数据为0，不可冲销
    RETURN_CODE_INPUT_ITEM_ZERO_QTY_NOT_WRITE_OFF,
    // 行项目{0}存在未完成或未冲销的下游单据，请根据单据流优先处理
    RETURN_CODE_REGISTER_ITEM_NOT_WRITE_OFF,
    // 存在未完成的质检会签单
    RETURN_CODE_SIGN_INSPECT_UN_COMPLETED,
    // 存在未完成的不符合项处置单
    RETURN_CODE_INCONFORMITY_MAINTAIN_UN_COMPLETED,
    // 存在未完成的验收入库单
    RETURN_CODE_INSPECT_INPUT_UN_COMPLETED,
    // 用户已锁定
    RETURN_CODE_USER_LOCK,
    // 用户名或密码错误
    RETURN_CODE_PASSWORD_ERROR,
    // 登录成功
    RETURN_CODE_LOGIN_SUCCESS,
    // 登录失败
    RETURN_CODE_LOGOUT_SUCCESS,
    // token过期
    RETURN_CODE_TOKEN_EXPIRED,
    // token 失效
    RETURN_CODE_TOKEN_IN_VALID,
    // MQ推送失败
    RETURN_CODE_MQ_SEND_FAILURE,
    // 发送邮件失败
    RETURN_CODE_EMAIL_SEED_FAILURE,
    // 发送定时任务失败
    RETURN_CODE_JOB_INVOKE_FAILURE,
    // 账期保存成功
    RETURN_CODE_ACCOUNT_PERIOD_SAVE_SUCCESS,
    // 账期删除成功
    RETURN_CODE_ACCOUNT_PERIOD_DELETE_SUCCESS,
    // 账期删除失败
    RETURN_CODE_ACCOUNT_PERIOD_DELETE_FAILURE,
    // 用户{0}修改密码成功
    RETURN_CODE_UPDATE_PASSORD_SUCCESS,
    // 菜单{0}保存成功
    RETURN_CODE_RESOURCES_SAVE_SUCCESS,
    // 菜单{0}删除成功
    RETURN_CODE_RESOURCES_DELETE_SUCCESS,
    // 菜单{0}删除失败
    RETURN_CODE_RESOURCES_DELETE_FAILURE,
    // 角色{0}保存成功
    RETURN_CODE_ROLE_SAVE_SUCCESS,
    // 角色{0}删除成功
    RETURN_CODE_ROLE_DELETE_SUCCESS,
    // 角色{0}删除失败
    RETURN_CODE_ROLE_DELETE_FAILURE,
    // 用户{0}保存成功
    RETURN_CODE_USER_SAVE_SUCCESS,
    // 用户{0}删除成功
    RETURN_CODE_USER_DELETE_SUCCESS,
    // 用户{0}删除失败
    RETURN_CODE_USER_DELETE_FAILURE,
    // 用户{0}保存库存地点配置成功
    RETURN_CODE_USER_STOCK_LOCATION_SAVE_SUCCESS,
    // 用户{0}冻结成功
    RETURN_CODE_USER_FREEZE_SUCCESS,
    // 用户{0}解冻成功
    RETURN_CODE_USER_UNFREEZE_SUCCESS,
    // 用户{0}锁定成功
    RETURN_CODE_USER_LOCK_SUCCESS,
    // 用户{0}解锁成功
    RETURN_CODE_USER_UNLOCK_SUCCESS,
    // 文件上传成功
    RETURN_CODE_APP_UPGRADE_UPLOAD_SUCCESS,
    // 文件删除成功
    RETURN_CODE_APP_UPGRADE_DELETE_SUCCESS,
    // 文件删除失败
    RETURN_CODE_APP_UPGRADE_DELETE_FAILURE,
    // 附件{0}上传成功
    RETURN_CODE_FILE_UPLOAD_SUCCESS,
    // 附件{0}删除成功
    RETURN_CODE_FILE_DELETE_SUCCESS,
    // 附件{0]删除失败
    RETURN_CODE_FILE_DELETE_FAILURE,
    // 附件不允许为空
    RETURN_CODE_FILE_NOT_NULL,
    // 图片{0}上传成功
    RETURN_CODE_IMAGES_UPLOAD_SUCCESS,
    // 图片{0]删除成功
    RETURN_CODE_IMAGES_DELETE_SUCCESS,
    // 图片{0}删除失败
    RETURN_CODE_IMAGES_DELETE_FAILURE,
    // 图片较大,请选择较小的图片
    RETURN_CODE_IMAGES_SIZE_LARGER,
    // 通道门保存成功
    RETURN_CODE_GATE_SAVE_SUCCESS,
    // 通道门删除成功
    RETURN_CODE_GATE_DELETE_SUCCESS,
    // 通道门删除失败
    RETURN_CODE_GATE_DELETE_FAILURE,
    // 打印机保存成功
    RETURN_CODE_PRINTER_SAVE_SUCCESS,
    // 打印机删除成功
    RETURN_CODE_PRINTER_DELETE_SUCCESS,
    // 打印机删除失败
    RETURN_CODE_PRINTER_DELETE_FAILURE,
    // 国际化语言保存成功
    RETURN_CODE_LANGUAGE_SAVE_SUCCESS,
    // 国际化语言删除成功
    RETURN_CODE_LANGUAGE_DELETE_SUCCESS,
    // 国际化语言删除失败
    RETURN_CODE_LANGUAGE_DELETE_FAILURE,
    // 定时任务保存成功
    RETURN_CODE_JOB_SAVE_SUCCESS,
    // 定时任务执行成功
    RETURN_CODE_JOB_START_SUCCESS,
    // 定时任务删除成功
    RETURN_CODE_JOB_DELETE_SUCCESS,
    // 定时任务删除失败
    RETURN_CODE_JOB_DELETE_FAILURE,
    // 物料{0}保存成功
    RETURN_CODE_MATERIAL_SAVE_SUCCESS,
    // 物料{0}删除成功
    RETURN_CODE_MATERIAL_DELETE_SUCCESS,
    // 物料{0}删除失败
    RETURN_CODE_MATERIAL_DELETE_FAILURE,
    // 物料{0}同步成功
    RETURN_CODE_MATERIAL_SYS_SUCCESS,
    // 物料工厂保存成功
    RETURN_CODE_MATERIAL_FACTORY_SAVE_SUCCESS,
    // 物料工厂删除成功
    RETURN_CODE_MATERIAL_FACTORY_DELETE_SUCCESS,
    // 物料工厂删除失败
    RETURN_CODE_MATERIAL_FACTORY_DELETE_FAILURE,
    // 计量单位换算保存成功
    RETURN_CODE_UNIT_REL_SAVE_SUCCESS,
    // 计量单位换算删除成功
    RETURN_CODE_UNTI_REL_DELETE_SUCCESS,
    // 计量单位换算删除失败
    RETURN_CODE_UNIT_REL_DELETE_FAILURE,
    // 仓位{0}保存成功
    RETURN_CODE_STORAGE_BIN_SAVE_SUCCESS,
    // 仓位{0}删除成功
    RETURN_CODE_STORAGE_BIN_DELETE_SUCCESS,
    // 仓位{0}删除失败
    RETURN_CODE_STORAGE_BIN_DELETE_FAILURE,
    // 公告保存成功
    RETURN_CODE_NOTICE_SAVE_SUCCESS,
    // 公告删除成功
    RETURN_CODE_NOTICE_DELETE_SUCCESS,
    // 公告删除失败
    RETURN_CODE_NOTICE_DELETE_FAILURE,
    // 公司{0}保存成功
    RETURN_CODE_CORP_SAVE_SUCCESS,
    // 公司{0}删除成功
    RETURN_CODE_CORP_DELETE_SUCCESS,
    // 公司{0}删除失败
    RETURN_CODE_CORP_DELETE_FAILURE,
    // 工厂{0}保存成功
    RETURN_CODE_FACTORY_SAVE_SUCCESS,
    // 工厂{0}删除成功
    RETURN_CODE_FACTORY_DELETE_SUCCESS,
    // 工厂{0}删除失败
    RETURN_CODE_FACTORY_DELETE_FAILURE,
    // 库存地点{0}保存成功
    RETURN_CODE_STOCK_LOCATION_SAVE_SUCCESS,
    // 库存地点{0}删除成功
    RETURN_CODE_STOCK_LOCATION_DELETE_SUCCESS,
    // 库存地点{0}删除失败
    RETURN_CODE_STOCK_LOCATION_DELETE_FAILURE,
    // 仓库{0}保存成功
    RETURN_CODE_WH_SAVE_SUCCESS,
    // 仓库{0}删除成功
    RETURN_CODE_WH_DELETE_SUCCESS,
    // 仓库{0}删除失败
    RETURN_CODE_WH_DELETE_FAILURE,
    // 存储类型{0}保存成功
    RETURN_CODE_STORAGE_TYPE_SAVE_SUCCESS,
    // 存储类型{0}删除成功
    RETURN_CODE_STORAGE_TYPE_DELETE_SUCCESS,
    // 存储类型{0}删除失败
    RETURN_CODE_STORAGE_TYPE_DELETE_FAILURE,
    // 消息重发成功
    RETURN_CODE_MQ_SENDMESSAGE_SUCESS,
    // 分类{0}保存成功
    RETURN_CODE_SPEC_CLASSIFY_SAVE_SUCCESS,
    // 分类{0}删除成功
    RETURN_CODE_SPEC_CLASSIFY_DELETE_SUCCESS,
    // 分类{0}删除失败
    RETURN_CODE_SPEC_CLASSIFY_DELETE_FAILURE,
    // 特性{0}保存成功
    RETURN_CODE_SPEC_FEATURE_SAVE_SUCCESS,
    // 特性{0}删除成功
    RETURN_CODE_SPEC_FEATURE_DELETE_SUCCESS,
    // 特性{0}删除失败
    RETURN_CODE_SPEC_FEATURE_DELETE_FAILURE,
    // WCS设备{0}保存成功
    RETURN_CODE_WCS_DEIVCE_SAVE_SUCCESS,
    // WCS设备{0}删除成功
    RETURN_CODE_WCS_DEIVCE_DELETE_SUCCESS,
    // WCS设备{0}删除失败
    RETURN_CODE_WCS_DEIVCE_DELETE_FAILURE,
    // WCS巷道{0}保存成功
    RETURN_CODE_WCS_TUNNEL_SAVE_SUCCESS,
    // WCS巷道{0}删除成功
    RETURN_CODE_WCS_TUNNEL_DELETE_SUCCESS,
    // WCS巷道{0}删除失败
    RETURN_CODE_WCS_TUNNEL_DELETE_FAILURE,
    // WCS仓位{0}保存成功
    RETURN_CODE_WCS_STORAGE_BIN_SAVE_SUCCESS,
    // WCS仓位{0}删除成功
    RETURN_CODE_WCS_STORGAE_BIN_DELETE_SUCCESS,
    // WCS仓位{0}删除失败
    RETURN_CODE_WCS_STORAGE_BIN_DELETE_FAILURE,
    // 作业单{0}保存成功
    RETURN_CODE_TASK_SAVE_SUCCESS,
    // 作业单{0}提交成功
    RETURN_CODE_TASK_SUMBIT_SUCCESS,
    // 模型key已存在
    RETURN_CODE_MODEL_KEY_EXISTED,
    // 流程模型为空不允许导出
    RETURN_CODE_MODEL_NULL_NOT_EXPORT,
    // 读取流程资源失败
    RETURN_CODE_READ_RESOURCE_FAILURE,
    // 模型key已存在单据绑定类型，不允许绑定
    RETURN_CODE_MODEL_KEY_EXIST_RECEIPT_TYPE,
    // 存储单元{0}保存成功
    RETURN_CODE_CELL_SAVE_SUCCESS,
    // 存储单元{0}删除成功
    RETURN_CODE_CELL_DELETE_SUCCESS,
    // 存储单元{0}删除失败
    RETURN_CODE_CELL_DELETE_FAILURE,
    // 存储区{0}保存成功
    RETURN_CODE_SECTION_SAVE_SUCCESS,
    // 存储区{0}删除成功
    RETURN_CODE_SECTION_DELETE_SUCCESS,
    // 存储区{0}删除失败
    RETURN_CODE_SECTION_DELETE_FAILURE,
    // 请求{0}行项目{1}已完成，不能删除
    RETURN_CODE_REQ_ITEM_NOT_DELETE,
    // 出库单{0}保存成功
    RETURN_CODE_OUTPUT_SAVE_SUCCESS,
    // 出库单{0}提交成功
    RETURN_CODE_OUTPUT_SUBMIT_SUCCESS,
    // 出库单{0}删除成功
    RETURN_CODE_OUTPUT_DELETE_SUCCESS,
    // 出库单{0}冲销成功
    RETURN_CODE_OUTPUT_WRITEOFF_SUCCESS,
    // 出库单{0}过账成功
    RETURN_CODE_OUTPUT_POST_SUCCESS,
    // 出库单{0}关闭预留成功
    RETURN_CODE_OUTPUT_CLOSE_SUCCESS,
    // 出库单{0}关闭成功
    RETURN_CODE_OUTPUT_CLOSE_ONLY_SUCCESS,
    // 出库单{0}撤销成功
    RETURN_CODE_OUTPUT_REVOKE_SUCCESS,
    // 出库单{0}核销成功
    RETURN_CODE_OUTPUT_DEPT_OFFSET_SUCCESS,
    // 维修接收单{0}保存成功
    RETURN_CODE_REPAIR_INPUT_SAVE_SUCCESS,
    // 维修接收单{0}提交成功
    RETURN_CODE_REPAIR_INPUT_SUBMIT_SUCCESS,
    // 维修接收单{0}删除成功
    RETURN_CODE_REPAIR_INPUT_DELETE_SUCCESS,
    // 维修接收单{0}过账成功
    RETURN_CODE_REPAIR_INPUT_POST_SUCCESS,
    // 吊带{0}保存成功
    RETURN_CODE_SLING_SAVE_SUCCESS,
    // 吊带{0}删除成功
    RETURN_CODE_SLING_DELETE_SUCCESS,
    // 调用目标字符串或执行表达式格式错误
    RETURN_CODE_INVOKE_TARGET_OR_CRON_EXPRESSION_FORMAT_ERROR,
    // 退库单{0}保存成功
    RETURN_CODE_RETURN_SAVE_SUCCESS,
    // 退库单{0}提交成功
    RETURN_CODE_RETURN_SUBMIT_SUCCESS,
    // 退库单{0}删除成功
    RETURN_CODE_RETURN_DELETE_SUCCESS,
    // 退库单{0}冲销成功
    RETURN_CODE_RETURN_WRITEOFF_SUCCESS,
    // 退库单{0}过账成功
    RETURN_CODE_RETURN_POST_SUCCESS,
    // 请求单据异常
    RETURN_CODE_RECEIPT_REQ_EXCEPTION,
    // Excel 读取异常
    RETURN_CODE_EXCEL_READ_EXCEPTION,
	// 导入失败，导入模板中无数据
	RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA,
    // {0} 数据长度超过 {1} 字符
    RETURN_CODE_DATA_LENGTH_TOO_LONG,
    // 已拆分的质检会签单不允许二次拆分，请保证待质检数量 = 合格数量+不合格数量+未到货数量
    RETURN_CODE_INSPECT_RECEIPT_CAN_NOT_SUBMIT,
    /*WCS--相关异常*/
    // 取货成功 结束任务
    RETURN_CODE_SUCCESSFULLY_PICKED_UP,
    // WCS 指令行项目为空，不能生成任务
    RETURN_CODE_COMMAND_LINE_ITEM_IS_EMPTY,
    // WCS 所需仓位为空 异常
    RETURN_CODE_WCS_THE_REQUIRED_POSITION_IS_EMPTY,
    // WCS 未找到相关托盘指令
    THE_RELEVANT_PALLET_INSTRUCTION_WAS_NOT_FOUND,
    //level参数为空，不能获取仓位
    RETURN_CODE_WCS_CHECK_PORT_LEVEL_PARAMETER_IS_EMPTY,
    // WCS 存储类型{0} 获取仓位为空
    RETURN_CODE_WCS_GET_EMPTY_POSITION_IS_EMPTY,
    // WCS 指令不存在
    RETURN_CODE_WCS_INSTRUCTION_DOES_NOT_EXIST,
    // EXCEL中存在相同的编码 {0}
    RETURN_CODE_EXCEL_HAS_SAME_CODE,
    // 仓库号不存在 {0}
    RETURN_CODE_WH_CODE_NOT_EXIST,
    // 出库单 {0} 可退库数量不足
    RETURN_CODE_OUTPUT_CAN_RETURN_QTY_NOT_ENOUGH,
    // 调拨出库数量大于申请数量
    RETURN_CODE_OUTPUT_QTY_MAX_APPLY_QTY,
    // 【{0}】电子秤不存在
    RETURN_CODE_ELECTRONIC_SCALE_DOES_NOT_EXIST,
    // 电子秤ID已存在
    RETURN_CODE_ELECTRONIC_SCALE_ID_IS_EXIST,
    // 【{0}】非电子秤
    RETURN_CODE_ELECTRONIC_SCALE_NOT_TYPE,
    // 电子秤【{0}】已冻结
    RETURN_CODE_ELECTRONIC_SCALE_FROZEN,
    // 【{0}】电子秤不存在上报记录
    RETURN_CODE_ELECTRONIC_SCALE_RECORD_NOT_EXIST,
    // 电子秤ID为空
    RETURN_CODE_ELECTRONIC_SCALE_EMPTY,
    // 【{0}】电子秤设备状态为空
    RETURN_CODE_ELECTRONIC_SCALE_STATUS_EMPTY,
    // 【{0}】电子秤设备电量为空
    RETURN_CODE_ELECTRONIC_SCALE_QUANTITY_EMPTY,
    // 【{0}】电子秤已离线
    RETURN_CODE_ELECTRONIC_SCALE_STATUS_OFFLINE,
    // 【{0}】电子秤电量不足
    RETURN_CODE_ELECTRONIC_SCALE_STATUS_LOW_BATTERY,
    // 【{0}】电子秤未关联物料
    RETURN_CODE_ELECTRONIC_SCALE_NOT_REL_MAT,
    // 【{0}】电子秤未关联仓位
    RETURN_CODE_ELECTRONIC_SCALE_NOT_REL_BIN,
    // 电子秤【{0}】不在作业范围内
    RETURN_CODE_ELECTRONIC_SCALE_NOT_BIND_MAT,
    // 电子秤【{0}】已生成采集任务,无法编辑
    RETURN_CODE_COLLECTION_TASK_GEN_NOT_UPDATE,
    // 电子秤【{0}】已生成采集任务,无法删除
    RETURN_CODE_COLLECTION_TASK_GEN_NOT_DELETE,
    // 【{0}】被占用不能编辑
    RETURN_CODE_UPDATE_FAIL_IN_USE,
    // 采集任务【{0}】编辑成功
    RETURN_CODE_COLLECTION_TASK_UPDATE_SUCCESS,
    // 采集任务已生成
    RETURN_CODE_COLLECTION_TASK_GEN_SUCCESS,
    // 请选择核销的订单
    RETURN_CODE_SELECT_WRITE_OFF_ORDER,
    // 维保通知单{0}保存成功
    TOOL_MAINTAIN_CODE_OUTPUT_SAVE_SUCCESS,
    // 维保通知单{0}提交成功
    TOOL_MAINTAIN_CODE_OUTPUT_SUBMIT_SUCCESS,
    // 工器具维保记录单{0}保存成功
    TOOL_MAINTAIN_CODE_INPUT_SAVE_SUCCESS,
    // 工器具维保记录单{0}提交成功
    TOOL_MAINTAIN_CODE_INPUT_SUBMIT_SUCCESS,
    // 报废申请单{0}提交成功
    TOOL_SCRAP_APPLY_CODE_SUBMIT_SUCCESS,
    // 报废申请单{0}保存成功
    TOOL_SCRAP_APPLY_CODE_SAVE_SUCCESS,
    // 报废出库单{0}提交成功
    TOOL_SCRAP_OUTPUT_CODE_SUBMIT_SUCCESS,
    // 报废出库单{0}保存成功
    TOOL_SCRAP_OUTPUT_CODE_SAVE_SUCCESS,
    /** 暂存申请单{0}保存成功 */
    RETURN_CODE_TEMPORARY_STORAGE_SAVE_SUCCESS,
    /** 暂存申请单{0}提交成功 */
    RETURN_CODE_TEMPORARY_STORAGE_SUBMIT_SUCCESS,
    /** 暂存领用申请单{0}保存成功 */
    RETURN_CODE_TEMPORARY_STORAGE_APPLY_OUTPUT_SAVE_SUCCESS,
    /** 暂存领用申请单{0}提交成功 */
    RETURN_CODE_TEMPORARY_STORAGE_APPLY_OUTPUT_SUBMIT_SUCCESS,
    /** 暂存入库单{0}提交成功 */
    RETURN_CODE_TEMP_STORE_INPUT_SUBMIT_SUCCESS,
    // 物资返运单{0}保存成功
    MATERIAL_RETURN_CODE_SAVE_SUCCESS,
    // 物资返运单{0}提交成功
    MATERIAL_RETURN_CODE_SUBMIT_SUCCESS,
    // 物资返运单删除成功
    MATERIAL_RETURN_CODE_DELETE_SUCCESS,
    // 物资返运数量不能为零
    MATERIAL_RETURN_CODE_RETURN_QTY_NO_ZERO,
    // 报废冻结单{0}提交成功
    RETURN_CODE_SCRAP_FREEZE_SUBMIT_SUCCESS,
    // 报废冻结单{0}保存成功
    RETURN_CODE_SCRAP_FREEZE_SAVE_SUCCESS,
    // 报废冻结单{0}冲销成功
    RETURN_CODE_SCRAP_FREEZE_WRITEOFF_SUCCESS,
    // 报废冻结单{0}过账成功
    RETURN_CODE_SCRAP_FREEZE_POST_SUCCESS,
    // 报废冻结单{0}删除成功
    RETURN_CODE_SCRAP_FREEZE_DELETE_SUCCESS,
    // 报废申请单{0}提交成功
    RETURN_CODE_SCRAP_APPLY_CODE_SUBMIT_SUCCESS,
    // 报废申请单{0}保存成功
    RETURN_CODE_SCRAP_APPLY_CODE_SAVE_SUCCESS,
    // 报废出库单{0}提交成功
    RETURN_CODE_SCRAP_OUTPUT_CODE_SUBMIT_SUCCESS,
    // 报废出库单{0}保存成功
    RETURN_CODE_SCRAP_OUTPUT_CODE_SAVE_SUCCESS,
    // "若为吊车吊带编码必填",
    RETURN_CODE_SLING_CODE_LOST,
    // 科室{0}保存成功
    RETURN_CODE_DEPT_OFFICE_SAVE_SUCCESS,
    // {0}级审批节点:{1}{2}{3}级审批人没有对应的处理人
    RETURN_CODE_APPROVE_NO_USER_DEPT_OFFICE,
    // 当前用户没有绑定部门
    RETURN_CODE_USER_NO_DEPT,
    // {0}级审批节点没有对应的审批负责人
    RETURN_CODE_APPROVE_NO_USER_DEPT,
    // 第{0}行: {1}不存在
    RETURN_CODE_EXCEL_MSG,
    // 第{0}行: 必填项不能为空
    RETURN_CODE_EXCEL_REQUIRED_MSG,
    // 批次信息id不能为空
    RETURN_CODE_BATCH_ID_EMPTY,
    /** 领料人不能为空 */
    RETURN_CODE_RECEIVER_ID_EMPTY,
    /** * 出库数量不能为零 */
    RETURN_CODE_OUTPUT_QTY_IS_ZERO,
    /** 该出库单已经没有可入库的数量 */
    RETURN_CODE_OUTPUT_ORDER_OUTCOUNT_IS_ZERO,
    /**  "用户信息不存在" */
    SSO_USER_NOT_EXIST,
    /**  "用户未被设置单点登录" */
    USER_UN_PERMISSION_SSO,
    // 标签数量不足
    LABEL_QTY_NOT_ENOUGH,
    // 标签不存在
    LABEL_NOT_EXIST,
    //自定义异常
    INIT_EXCEPTION_DES,
    // 运单第{0}行的行号不存在
    UNITIZED_DELIVERY_WAYBILL_RID_MISS,
    // 单据第{0}行的{1}为非末级wbs, 请先进行转性
    WBS_NOT_LEAF,
    // 作业单{0}撤销成功
    RETURN_CODE_TASK_ROLLBACK_SUCCESS,
    // 单据类型不允许
    RETURN_CODE_RECEIPT_TYPE_FORBID,
    // 单据已提交
    RETURN_CODE_RECEIPT_HAVE_SUBMIT,
    // 存在代理人已代理他人或委托人已被他人代理
    RETURN_CODE_RECEIPT_PROXYED,
    // 委托人存在生效的代理任务
    RETURN_CODE_RECEIPT_PROXYED_CAN,
    //{0}行为不支持的包装形式，请重新修改
    RETURN_CODE_RECEIPT_UNSUP_PACK,
    //验收入库单未过账，请完成入库
    RETURN_CODE_RECEIPT_INPUT_NOT_POST,
    //验收入库单存在未上架的行项目，请完成上架作业
    RETURN_CODE_RECEIPT_INPUT_NOT_TASK,
    //固定资产类物资必须单独冲销
    RETURN_CODE_RECEIPT_FIX_MAT_SELF_POST,
    //称必须一致
    RETURN_CODE_WAYBILL_MANUFACTURE_MUST_BE_SAME,
    // 未找到对应合同
    RETURN_CODE_CONTRACT_NOT_FOUND,
    // 回写合同发货数量类型错误
    RETURN_CODE_CONTRACT_SEND_QTY_WRITE_TYPE_ERROR,
    // 本次发运数量不允许大于合同未清数量
    RETURN_CODE_CONTRACT_SEND_QTY_ERROR,
    // 本次发运数量不允许大于合同门到门送货数量
    RETURN_CODE_D2D_DELIVERY_QTY_ERROR,
    // 导入数量不能超过{0}条
    RETURN_CODE_IMP_QTR,
    // 需求类型不能为空
    RETURN_CODE_DEMAND_TYPE_EMPTY,
    // 紧急标识不能为空
    RETURN_CODE_URGENT_FLAG_EMPTY,
    // 预算类型不能为空
    RETURN_CODE_BUDGET_TYPE_EMPTY,
    // 物料编码不能为空
    RETURN_CODE_MAT_CODE_EMPTY,
    // 物料描述不能为空
    RETURN_CODE_MAT_NAME_EMPTY,
    // 资产卡片号不能为空
    RETURN_CODE_ASSET_CARD_CODE_EMPTY,
    // 资产卡片描述不能为空
    RETURN_CODE_ASSET_CARD_NAME_EMPTY,
    // 品名不能为空
    RETURN_CODE_PRODUCT_NAME_EMPTY,
    // 数量必须大于0
    RETURN_CODE_QTY_MUST_GT_ZERO,
    // 计量单位不能为空
    RETURN_CODE_UNIT_CODE_EMPTY,
    // 成本中心不能为空
    RETURN_CODE_COST_CENTER_EMPTY,
    // WBS编号不能为空
    RETURN_CODE_WBS_CODE_EMPTY,
    // 需求类型错误
    RETURN_CODE_DEMAND_TYPE_ERROR,
    /** 关联的需求计划部分行项目不存在 */
    RETURN_CODE_ITEM_NOT_EXIST,
    /** 数据库更新失败 */
    RETURN_CODE_DB_UPDATE_ERROR,
    /** 数量不能为负数 */
    RETURN_CODE_QTY_CANNOT_BE_NEGATIVE,
    // 资产卡片不存在
    RETURN_CODE_ASSET_CARD_NOT_EXIST,
    // WBS编码不存在
    RETURN_CODE_WBS_CODE_NOT_EXIST,
    // 成本中心不存在
    RETURN_CODE_COST_CENTER_NOT_EXIST,
    // 行项目已创建合同不能取消
    RETURN_CODE_ITEM_HAS_CONTRACT,
    // 合同行项目新数量必须大于已送货数量且小于原合同数量
    RETURN_CODE_QTY_MUST_GT_SEND_QTY_AND_LT_CONTRACT_QTY,
    // 离岸采购送货通知不能关闭
    RETURN_CODE_OFFSHORE_DELIVERY_CANNOT_CLOSE, 
    // 送货通知单不存在
    RETURN_ERROR_DELIVERY_NOTICE_NOT_EXISTS,
    // 离岸采购送货通知不能关闭 
    RETURN_ERROR_OFFSHORE_DELIVERY_CANNOT_CLOSE,
    // 到货登记单状态必须为草稿 
    RETURN_ERROR_REGISTER_STATUS_NOT_DRAFT,
    
    
    DELIVERY_NOTICE_NO_ITEMS,

    // 只有离岸采购的送货通知可以撤销
    RETURN_ERROR_ONLY_OFFSHORE_CAN_REVOKE,
    // 物流清关费用状态必须为草稿或已关闭
    RETURN_ERROR_LOGISTICS_STATUS_INVALID,
    // 存在付款预算单 {0} 状态不为待编制,不允许撤销
    RETURN_ERROR_PAYMENT_PLAN_STATUS_CANNOT_REVOKE,
    // 当前单据已创建合同 {0} ,不允许撤销
    RETURN_ERROR_CONTRACT_EXISTS_CANNOT_REVOKE,
    // 采购订单不存在
    RETURN_ERROR_PURCHASE_ORDER_NOT_EXISTS,

    // 物流清关费用不存在
    RETURN_ERROR_LOGISTICS_NOT_EXISTS,
    // 物流清关费用无行项目数据
    RETURN_ERROR_LOGISTICS_NO_ITEMS,
    // 存在已完成的到货登记单，不可关闭
    RETURN_ERROR_REGISTER_COMPLETED_CANNOT_CLOSE,
    // 采购申请行项目[{}]未清数量不能小于0
    RETURN_CODE_PURCHASE_APPLY_ITEM_UNCLEARED_QTY_CANNOT_BE_NEGATIVE,
    // 采购申请行项目[{}]已创建合同数量不能小于0
    RETURN_CODE_PURCHASE_APPLY_ITEM_CONTRACT_QTY_CANNOT_BE_NEGATIVE,
    // 合同有效期已过，不能创建油品送货单
    RETURN_CODE_CONTRACT_VALIDITY_EXPIRED ,

    // 质检分配单已创建质检单，不能关闭
    RETURN_CODE_INSPECT_ASSIGN_HAS_INSPECT_HEAD,

    // 存在送货单{0}，不允许修改箱件
    RETURN_CODE_EXIST_DELIVERY_NOTICE,
    // 单据编号[{}]已存在
    RETURN_CODE_RECEIPT_CODE_EXIST,
    // 批次号[{}]已存在
    RETURN_CODE_BATCH_CODE_EXIST,

    // 不支持的采购类型
    RETURN_CODE_UNSUPPORTED_PURCHASE_TYPE,

    // 创建采购订单失败：{0}
    RETURN_CODE_CREATE_PURCHASE_ORDER_FAILED,


    // 物料类型{0}的费用比例不一致
    RETURN_CODE_FEE_RATIO_ERROR_BY_MAT_TYPE,
    
    // 费用比例之和不等于100%
    RETURN_CODE_FEE_RATIO_ERROR,

    // 物料类型不能为空
    RETURN_CODE_MAT_TYPE_IS_EMPTY,

    // 费用比例不能为空
    RETURN_CODE_FEE_RATIO_IS_EMPTY  ,

    // 分摊税额不等于总税额
    RETURN_CODE_SHARE_TAX_NOT_EQUAL  ,

    // 密码长度必须大于8位
    RETURN_CODE_PASSWORD_LENGTH_ERROR,

    // 密码需要包含3种字符，数字、字母、特殊字符
    RETURN_CODE_PASSWORD_COMPLEXITY_ERROR,

    // 采购单存在未完成的到货登记单[{0}]，不能关闭
    RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_REGISTER, 

    // 采购单存在未完成的质检分配单[{0}]，不能关闭
    RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_INSPECT_ASSIGN,

    // 采购单存在已完成的质检分配单[{0}]，不能撤销
    RETURN_CODE_PURCHASE_ORDER_HAS_FINISHED_INSPECT_ASSIGN,

    // 采购单存在未完成的质检会签[{0}]，不能关闭
    RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_INSPECT_SIGN, 

    // 采购单存在未完成的差异通知[{0}]，不能关闭
    RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_DIFFERENCE_NOTICE,

    // 采购单存在未完成的差异处置[{0}]，不能关闭
    RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_DIFFERENCE_DISPOSAL,  

    // 采购单存在未完成的入库单[{0}]，不能关闭
    RETURN_CODE_PURCHASE_ORDER_HAS_UNFINISHED_INPUT,

    // 关闭采购单失败：{0}
    RETURN_CODE_CLOSE_PURCHASE_ORDER_FAILED,

    // 账号未绑定供应商
    RETURN_CODE_UN_BIND_SUPPLIER,

    // 采购订单{0}不存在
    RETURN_CODE_PURCHASE_ORDER_NOT_FOUND,

    // 采购退货单不可撤销
    RETURN_CODE_PURCHASE_RETURN_CANNOT_REVOKE,

    // 供应商名称{0}重复
    RETURN_CODE_SUPPLIER_DUPLICATE,

    // 网络连接失败，请稍后重试！（Http-Code: 502，网关通信失败）
    NO_MSG_1873,
    // 远程接口返回信息：{0}
    NO_MSG_1874,
    // 远程接口返回信息：{0}
    NO_MSG_1875,
    // 远程接口返回信息：{0}
    NO_MSG_1876,
    // 远程接口返回信息：{0}
    NO_MSG_1877,
    // 房间正在使用，无法提交
    NO_MSG_1878,
    // 已超出房间入住人数，请重新选择
    NO_MSG_1879,
    // 请上传交接单附件
    NO_MSG_1880,
    // 历史单据无法使用此功能
    NO_MSG_1881,
    // 所选送货单{}已创建运单{}，请检查
    NO_MSG_1882,
    // 交单管理或所得税免税管理未完成，无法清关完成，请检查
    NO_MSG_1883,

    // NAUTHM⽤户身份认证接⼝返回为空
    RETURN_CODE_NAUTHM_USER_AUTH_INTERFACE_RETURN_NULL,
    // NAUTHM⽤户身份认证接⼝返回错误
    RETURN_CODE_NAUTHM_USER_AUTH_INTERFACE_RETURN_ERROR,

    // 任务不存在
    RETURN_CODE_TASK_DOES_NOT_EXIST,

    // 存在不同采购订单物料，请检查输入
    RETURN_CODE_ITEM_RETURN_PURCHASE_CODE_DIFFERENT,

    // 物料编码{0}的内贸倍率不一致，请修改
    RETURN_CODE_INCONSISTENT_DOMESTIC_TRADE_RATE,
    // 行号{0}操作数量之和大于待下架数量
    RETURN_CODE_QTY_GT_UNLOAD_QTY,
    // 行号{0}操作数量大于待上架数量
    RETURN_CODE_QTY_GT_LOAD_QTY,
    // 导入库存地点与抬头库存地点不一致
    RETURN_CODE_IMPORT_LOCATION_NOT_EQUAL_HEAD_LOCATION,
    ;

}
