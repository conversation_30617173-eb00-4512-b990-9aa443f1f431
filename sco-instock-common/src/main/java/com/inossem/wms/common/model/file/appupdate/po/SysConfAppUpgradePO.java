package com.inossem.wms.common.model.file.appupdate.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "app升级包列表查询对象")
public class SysConfAppUpgradePO {

    /** 当前设备app版本号 */
    @ApiModelProperty(value = "当前设备app版本号", example = "80", required = true)
    private String currentVersionCode;
    /** 当前设备app版本名称 */
    @ApiModelProperty(value = "当前设备app版本名称", example = "1.0.1.80", required = false)
    private String currentVersionName;
    /** 设备操作系统类型， 0：android，1：ios */
    @ApiModelProperty(value = "设备操作系统类型， 0：android，1：ios", example = "0", required = true)
    private byte osType;
    /** 目标语言 */
    @ApiModelProperty(value = "目标语言, 从请求头信息中获取", example = "zh-CN", required = false)
    private String langCode;
    /** 设备类型 */
    @ApiModelProperty(value = "设备类型", example = "mate 20", required = false)
    private String devicesType;

    /** 安装包所属类型 */
    @ApiModelProperty(value = "安装包所属类型", example = "PDA|Pad|TV", required = false)
    private String apkType;

}
